<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course\Season;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Season;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminSeasonEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class UpdateSeasonFunctionalTest extends FunctionalTestCase
{
    use CourseCreatorFixtureTrait;
    private ?User $user = null;
    private ?User $otherUser = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();

        $this->user = $this->createAndGetUser(
            firstName: 'test',
            lastName: 'test',
            roles: [User::ROLE_ADMIN],
            email: '<EMAIL>',
        );

        $this->otherUser = $this->createAndGetUser(
            firstName: 'other',
            lastName: 'user',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>',
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('providerCourseSeasonUpdateAuthorization')]
    public function testCourseSeasonUpdateAuthorization(
        array $roles,
        bool $isCreatorCourse,
        bool $shareCourseWithUser,
        int $expectedStatusCode,
    ): void {
        $this->user->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $course = $this->createAndGetCourse(
            createdBy: $isCreatorCourse ? $this->user : $this->otherUser,
        );

        $season = $this->createAndGetSeason(
            course: $course,
            name: 'Season to update',
            type: 'sequential',
        );

        if ($shareCourseWithUser && !$isCreatorCourse) {
            $this->setAndGetCourseCreatorInRepository(
                userId: new Id($this->user->getId()),
                courseId: new Id($course->getId()),
            );
        }

        $updateData = [
            'name' => 'Updated Season Name',
            'type' => 'free',
        ];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'PUT',
            uri: AdminSeasonEndpoints::courseSeasonEndpoint($season->getId()),
            body: $updateData,
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerCourseSeasonUpdateAuthorization(): \Generator
    {
        yield 'Super Admin can update season' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Admin can update season' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager cannot update season' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 403,
        ];
        yield 'Creator can update season if is creator of course' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorCourse' => true,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator can update season if the course is shared with him' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator cannot update season if not related to course' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 403,
        ];
        yield 'Tutor cannot update season' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'Inspector cannot update season' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'Support cannot update season' => [
            'roles' => [User::ROLE_SUPPORT],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'User cannot update season' => [
            'roles' => [User::ROLE_USER],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Season::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->user?->getId(),
            $this->otherUser?->getId(),
        ]);

        parent::tearDown();
    }
}
