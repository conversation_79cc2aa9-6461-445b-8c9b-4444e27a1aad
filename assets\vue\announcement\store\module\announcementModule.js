import axios from 'axios';
import { make } from 'vuex-pathify';
import '../../../../css/courseStats.scss'

const state = {
  loading: true,
  config: {
    subsidizerActive: false,
    clientsFields: [],
  },

  // State valid only when viewing the announcement
  announcement: null, // Valid only when viewing the announcement
  // Store in store to avoid calling multiple times
  courseChapters: [],

  loadingCalledUsers: true,
  calledUsers: [],
  loadingAssistanceUsers: true,
  assistanceUsers: [],
  sessionSelected: {},
  showSessionSelectedFiles: false,
  userSelected: {},
  groupSelected: {},
  tutors: [],
  managers:[],
  fileSelected: {},
  calledUsersPagination: {
    page: 1,
    totalItems: 0,
  },

  loadingAnnouncementTutors: true,
  announcementTutors: [],

  loadingTasksCourse: true,
  tasksCourse: [],
  opinions: [],

  refresh: {
    refresh: false,
    action: null, // If null, refresh all elements in page, otherwise only the specified method
  },

  uncompletedUserProfile: [],

  activePane: 'info',
  selectedUserProfile: null,
  fundaeCatalogs: {}
};

const getters = {
  ...make.getters(state),
  isLoading(state) {
    return state.loading;
  },
  getManagers: state => state.managers,
};

const mutations = {
  ...make.mutations(state),
  SET_RESET_VIEW_STATE(state) {
    state.announcement = null;
    state.refresh = {
      refresh: false,
      action: null, // If null, refresh all elements in page, otherwise only the specified method
    };
  },
  RESET(state) {
    state.announcement = null;
    state.courseChapters = [];
    state.calledUsers = [];
    state.assistanceUsers = [];
    state.sessionSelected = {};
    state.userSelected = {};
    state.groupSelected = {};
    state.fileSelected = {};
    state.announcementTutors = [];
    state.tasksCourse = [];
    state.opinions = [];
    state.selectedUserProfile = null;
    state.uncompletedUserProfile = [];
  },
  SET_MANAGERS(state, managers) {
    state.managers = managers
  },
};

/**
 * Share common state for froala editor
 */
const module = {
  namespaced: true,
  state,
  getters,
  mutations,
  actions: {
    ...make.actions('config'),
    reset({ commit }) {
      commit('RESET');
    },
    async announcements(
      { commit },
      { page, query, order, direction = 'ASC', fromDate = null, toDate = null, type = null, status = null, extra = null },
    ) {
      commit('SET_LOADING', true);
      try {
        const url = new URL(window.location.origin + `/admin/announcements/${page}`);
        if (query.length > 0) url.searchParams.set('query', query);
        if (order && order.length > 0) {
          url.searchParams.set('order', order);
          url.searchParams.set('direction', direction);
        }

        if (fromDate && fromDate.length > 0) {
          url.searchParams.set('fromDate', fromDate);
        }

        if (toDate && toDate.length > 0) {
          url.searchParams.set('toDate', toDate);
        }

        if (type) url.searchParams.set('type', type);
        if (status && status.length > 0) url.searchParams.set('status', status);

        if (extra && typeof extra === 'object') {
          const extraParams = {};

          Object.keys(extra).forEach((key) => {
            if (Array.isArray(extra[key])) {
              extraParams[key] = extra[key];
            } else {
              extraParams[key] = extra[key];
            }
          });

          url.searchParams.set('extra', JSON.stringify(extraParams));
        }

        const result = await axios.get(url.toString());
        return result.data;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async downloadAnnouncementGroupReport({}, groupId) {
      axios
        .post(
          'admin/report-info/announcement/download-group-info',
          { groupId },
          {
            responseType: 'blob',
          },
        )
        .then((r) => {
          const objectUrl = window.URL.createObjectURL(
            new Blob([r.data], { type: 'application/zip' }),
          );
          let link = document.createElement('a');
          link.href = objectUrl;
          link.click();

          window.URL.revokeObjectURL(objectUrl);
        });
    },
    
    async participants(
      { commit },
      { page, query, order, direction = 'ASC', fromDate = null, toDate = null, type = null, status = null },
    ) {
      commit('SET_LOADING', true);
      try {
        const url = new URL(window.location.origin + `/admin/participants/${page}`);
        if (query.length > 0) url.searchParams.set('query', query);
        if (order && order.length > 0) {
          url.searchParams.set('order', order);
          url.searchParams.set('direction', direction);
        }

        if (fromDate && fromDate.length > 0) {
          url.searchParams.set('fromDate', fromDate);
        }

        if (toDate && toDate.length > 0) {
          url.searchParams.set('toDate', toDate);
        }

        if (type) url.searchParams.set('type', type);
        if (status && status.length > 0) url.searchParams.set('status', status);

        const result = await axios.get(url.toString());
        return result.data;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async availableFilters() {
      const result = await axios.get('/admin/announcements/available-filters');
      return result.data;
    },

    async loadPreSelectedCourse({ commit }, id) {
      const url = `/admin/announcements/pre-selected-course/${id}`;
      const result = await axios.get(url);
      return result.data;
    },

    async findCourses({ commit }, { page, query, typeCourse }) {
      try {
        let url = `/admin/announcements/courses/${page}`;
        if (query.length > 0) {
          url += `?query=${query}`;
        }
        if (query.length > 0 && typeCourse) {
          url += `&typeCourse=${typeCourse}`;
        } else {
          url += `?typeCourse=${typeCourse}`;
        }

        const result = await axios.get(url);
        return result.data;
      } finally {
      }
    },

    async loadTutors() {
      try {
        const result = await axios.get('/admin/announcements/tutors');
        return result.data;
      } finally {
      }
    },

    async loadSubsidizedData() {
      try {
        const result = await axios.get('/admin/announcements/subsidized-data');
        return result.data;
      } finally {
      }
    },

    async createAnnouncement({ commit }, formData) {
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const result = await axios.post('/admin/announcement', formData, {
        headers,
      });
      return result.data;
    },

    async updateAnnouncement({ commit, dispatch }, { id, formData }) {
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const url = `/admin/announcement/${id}/update`;
      const result = await axios.post(url, formData, {
        headers,
      });
      const { error } = result.data;
      if (!error) dispatch('refreshAction');
      return result.data;
    },

    async loadAnnouncement({ commit }, id) {
      commit('SET_LOADING', true);
      try {
        const result = await axios.get(`/admin/announcement/${id}`);
        const { data } = result.data;
        commit('SET_ANNOUNCEMENT', data);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    refreshAnnouncement({ commit, getters }) {
      commit('SET_LOADING', true);
      const { announcement } = getters;
      axios
        .get(`/admin/announcement/${announcement.id}`)
        .then((result) => {
          const { data } = result.data;
          commit('SET_ANNOUNCEMENT', data);
        })
        .finally(() => {
          commit('SET_LOADING', false);
        });
    },

    async loadAnnouncementFullData({ commit }, id) {
      try {
        const result = await axios.get(`/admin/announcement/${id}/update-data`);
        return result.data;
      } finally {
      }
    },

    async loadAnnouncementTutors({ commit }, id) {
      commit('SET_LOADING_ANNOUNCEMENT_TUTORS', true);
      try {
        const result = await axios.get(`/admin/announcement/${id}/tutors`);
        const { data } = result.data;
        commit('SET_ANNOUNCEMENT_TUTORS', data);
      } finally {
        commit('SET_LOADING_ANNOUNCEMENT_TUTORS', false);
      }
    },

    async loadAnnouncementTasks({ commit }, id) {
      commit('SET_LOADING_TASKS_COURSE', true);
      try {
        const result = await axios.get(`/admin/announcement/${id}/tasks`);
        const { data } = result.data;
        commit('SET_TASKS_COURSE', data);
        return result.data;
      } finally {
        commit('SET_LOADING_TASKS_COURSE', false);
      }
    },

    async loadAnnouncementCalledUsers({ commit, getters, dispatch }, id) {
      commit('SET_LOADING_CALLED_USERS', true);
      axios
        .get(`/admin/groups-announcement/${id}`)
        .then((result) => {
          const { data } = result.data;
          dispatch('checkNotSeenMessages', data);
        })
        .catch((e) => {
          console.log(e);
        });
    },

    async checkNotSeenMessages({ commit, getters, dispatch }, calledUsers) {
      const { chatChannel } = getters.announcement;
      commit('SET_LOADING_CALLED_USERS', true);
      const tutors = [];
      dispatch('chatModule/getUnseenMessages', { channelId: chatChannel }, { root: true })
        .then((result) => {
          const { data } = result;
          const { calledUsersPagination } = getters;
          calledUsers.forEach((group) => {
            group.users.forEach((user) => {
              if (user.channelId != null) {
                const index = data.findIndex((item) => item.channelId === user.channelId);
                if (index >= 0) {
                  user.unseenMessages = data[index].unseenMessages;
                  data.splice(index, 1);
                }
              }
            });
            tutors.push({ tutorId: group.groupInfo.tutor.id, groupId: group.groupInfo.id });
          });
          commit('SET_CALLED_USERS', calledUsers);
          commit('SET_TUTORS', tutors);
          commit('SET_CALLED_USERS_PAGINATION', {
            page: calledUsersPagination.page,
            totalItems: data['total-items'],
          });
        })
        .finally(() => {
          commit('SET_LOADING_CALLED_USERS', false);
        });
    },

    updateUserByID({ commit, getters }, data) {
      const calledUsers = getters['calledUsers'];
      commit(
        'SET_CALLED_USERS',
        (calledUsers || []).map((groups) => {
          return {
            ...groups,
            users: (groups.users || []).map((user) => {
              if (user.id === data.id) return { ...user, ...data };
              return user;
            }),
          };
        }),
      );
    },

    setUserSelected({ commit }, data = {}) {
      commit('SET_USER_SELECTED', data);
    },

    setFileSelected({ commit }, data = {}) {
      commit('SET_FILE_SELECTED', data);
    },

    async loadAnnouncementOpinions({ commit }, id) {
      const result = await axios.get(`/admin/announcement/${id}/opinions`);
      return result.data;
    },

    async loadAllManagers({ commit }, searchString = null) {
      const URL = searchString ? `/api/v2/admin/users/managers?search=${searchString}&page_size=10` : "/api/v2/admin/users/managers?page_size=10"
      const result = await axios.get(`${URL}&page=1`);
      const managers = result.data.users || [];
      commit('SET_MANAGERS', managers);
      return result.data;
    },

    async loadAllUsers({ commit }, searchString = null) {
      const URL = searchString ? `/api/v2/admin/users?search=${searchString}&page_size=10` : "/api/v2/admin/users?page_size=10"
      const result = await axios.get(`${URL}&page=1`);
      const managers = result.data.users || [];
      commit('SET_MANAGERS', managers);
      return result.data;
    },

    async addUserToGroup({ commit }, { announcementId, groupId, userId }) {
      try {
        await axios.post(`/api/v2/admin/announcements/${ announcementId }/groups/${groupId}/user/${ userId }`)
        return true
      } catch (e) {
        return false
      }
    },

    async loadAnnouncementManagers({ commit }, announcementId) {
      const result = await axios.get(`/api/v2/admin/announcements/${announcementId}/managers`);
      return result.data;
    },

    async addAnnouncementManager({ commit }, { announcementId, managerId }) {
      const result = await axios.put(`/api/v2/admin/announcements/${announcementId}/managers/${managerId}`);
      return result;
    },

    async removeAnnouncementManager({ commit }, { announcementId, managerId }) { 
      const result = await axios.delete(`/api/v2/admin/announcements/${announcementId}/managers/${managerId}`);
      return result.data
    },

    async notifyCalledUser({ commit }, { callId, announcementId }) {
      const url = `/admin/announcements/${announcementId}/notify/${callId}`;
      const result = await axios.post(url);
      return result.data;
    },

    async getUserChat({}, { announcementId, userId }) {
      const url = `/admin/announcement/${announcementId}/chat/${userId}`;
      const result = await axios.get(url);
      return result.data;
    },

    async sendUserChat({}, { announcementId, userId, formData }) {
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const url = `/admin/announcement/${announcementId}/send-message/${userId}`;
      const result = await axios.post(url, formData, { headers });
      return result.data;
    },

    async deleteCalledUser({}, { announcementId, callId }) {
      const url = `/admin/announcements/${announcementId}/uncall/${callId}`;
      const result = await axios.delete(url);
      return result.data;
    },

    async uploadAnnouncementMaterials({}, { id, formData }) {
      const url = `/admin/announcement/${id}/upload-materials`;
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const result = await axios.post(url, formData, {
        headers,
      });
      return result.data;
    },

    async deleteAnnouncement({}, id) {
      const url = `/admin/announcement/${id}`;
      const result = await axios.delete(url);
      return result.data;
    },

    async cloneAnnouncement({}, id) {
      const url = `/admin/clone-announcement/${id}`;
      const result = await axios.post(url);
      return result.data;
    },


    softDeleteAnnouncement({}, id) {
      const url = `/admin/announcement/${id}/soft`;
      return axios.delete(url).then(r => (r.data))
          .catch(e => ({
            error: true,
            data: e.message
          }))
    },

    deactivateAnnouncement({}, id) {
      const url = `/admin/announcement/${id}/deactivate`;
      return axios.delete(url).then(r => (r.data))
          .catch(e => ({
            error: true,
            data: e.message
          }))
    },

    async downloadAnnouncementReport({}, id) {
      const url = `/admin/report-announcement/${id}`;
      const result = await axios.get(url);
      return `data:application/pdf;base64,${result.data?.data}`;
    },

    async downloadDiploma({ commit }, data) {
      try {
        const result = await axios.get(
          `/admin/download-diploma-user/${data.idUser}/announcement/${data.idAnnouncement}`,
        );
        return {'data' : `data:application/pdf;base64,${result.data?.data}`, 'nombre': result.data?.nombre};
      } finally {
      }
    },

    async getAnnouncementCourseChapter({ commit }, id) {
      const result = await axios.get(`/admin/announcement/${id}/course-info`);
      const { data } = result.data;
      commit('SET_COURSE_CHAPTERS', data);
    },

    async getAnnouncementUserInfo({ commit }, { id, userId }) {
      const result = await axios.get(`/admin/announcement/${id}/user-info/${userId}`);
      return result.data;
    },

    async downloadAnnouncementUserReport({ commit }, { userId, announcementId }) {
      const result = await axios.get(
        `/admin/report-pdf-announcement-user/${userId}/announcement/${announcementId}`,
      );

      return `data:application/pdf;base64,${result.data?.data}`;
    },

    async getUserChapterResults({ commit }, userCourseChapterId) {
      const result = axios.get(`/admin/subsidizer/user-chapter-results/${userCourseChapterId}`);
      return (await result).data;
    },

    refreshAction({ commit }, action = null) {
      commit('SET_REFRESH', {
        refresh: true,
        action: action,
      });
    },

    async loadAnnouncementAssistanceUsers({ commit }, id) {
      commit('SET_LOADING_ASSISTANCE_USERS', true);
      try {
        const result = await axios.get(`/admin/groups-announcement/assistance/${id}`);
        const { data } = result.data;
        commit('SET_ASSISTANCE_USERS', data);
      } finally {
        commit('SET_LOADING_ASSISTANCE_USERS', false);
      }
    },

    setSessionSelected({ commit }, data = {}) {
      commit('SET_SESSION_SELECTED', data);
    },

    setGroupSelected({ commit }, data = {}) {
      commit('SET_GROUP_SELECTED', data);
    },

    updateGroupAssistance({}, payload) {
      return axios.post('/admin/group-announcement/save-assistance', payload).then((result) => {
        return result.data;
      });
    },

    activateAnnouncement({getters, commit}, payload) {
      return axios.post('/admin/announcement/activate-announcement', payload).then((result) => {
        const { error, data } = result.data;
        if (error) {
          if (typeof data === 'string' || data instanceof String) return result.data;
          if ('INCOMPLETE_PROFILE' in data && 'fields_fundae' in data.INCOMPLETE_PROFILE) {
            commit('SET_UNCOMPLETED_USER_PROFILE', data.INCOMPLETE_PROFILE.fields_fundae);
          }
        }
        return result.data;
      }).catch(e => ({
        error: true,
        exception: true,
        data: e
      }));
    },
    cancelAnnouncement({getters, commit}, payload) {
        return axios.post('/admin/announcement/cancel-announcement', payload).then((result) => {
            const { error, data } = result.data;
            if (error) {
                if (typeof data === 'string' || data instanceof String) return result.data;
                if ('INCOMPLETE_PROFILE' in data && 'fields_fundae' in data.INCOMPLETE_PROFILE) {
                    commit('SET_UNCOMPLETED_USER_PROFILE', data.INCOMPLETE_PROFILE.fields_fundae);
                }
            }
            return result.data;
        }).catch(e => ({
            error: true,
            exception: true,
            data: e
        }));
    },

    async checkAnnouncementReportStatus({}, groupId) {
      if (groupId < 0) return {};
      const result = await axios.post('/admin/report-info/announcement/group-info', {
        groupId,
      });
      return result.data;
    },

    downloadAnnouncementGroupSessionAssistance({ getters }, { sessionId }) {
      const { announcement } = getters;
      axios
        .post(
          `/admin/announcement/${announcement.id}/groups/session/download-assistance`,
          { sessionId },
          {
            responseType: 'blob',
          },
        )
        .then((r) => {
          const objectUrl = window.URL.createObjectURL(
            new Blob([r.data], { type: 'application/pdf' }),
          );
          let link = document.createElement('a');
          link.href = objectUrl;

          //link.target = '_blank';
          link.download = `assistance${sessionId}.pdf`;
          link.click();

          window.URL.revokeObjectURL(objectUrl);
        });
    },

    async uploadAssistanceFiles({ getters, dispatch }, {sessionId, formData}) {
      const { announcement } = getters;
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      return axios
          .post(`admin/announcement/group-session/${sessionId}/upload-assistance`, formData, {
            headers,
          }).then(r => {
            dispatch('loadAnnouncementAssistanceUsers', announcement.id);
            return r.data;
          });
    },

    async deleteSessionAssistanceFile({}, { sessionId, id }) {
      return axios.post(`admin/announcement/group-session/${sessionId}/delete-assistance-file`, { id })
          .then(r => (r.data))
          .catch(e => ({
            error: true,
            data: e
          }))
    },

    getFundaeCatalogs({ commit }) {
      axios.get('/admin/fundae-catalogs/full-data').then(r => {
        const { data } = r.data;
        commit('SET_FUNDAE_CATALOGS', data);
      })
    },

    async getUserFieldsFundae({}, userId) {
      return await axios.get(`/admin/user/${userId}/user-fields-fundae`).then(r => (r.data));
    },

    async saveUserFieldsFundae({}, { userId, formData }) {
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      return await axios.post(`/admin/user/${userId}/user-fields-fundae`, formData, {
        headers
      }).then(r => (r.data));
    },

    async sendEmailForValidationUser({},userId){
      try{
        console.log('sendEmailForValidationUser',userId);
        const { data } = await axios.get(`/admin/user/${userId}/send-email-for-validation`);
        return data;
      }
      catch (e) {
        console.log(e);
      }
    },

    async deleteUserFromGroup({}, payload){
      try{
        const { userId, groupId, announcementId } = payload
        await axios.delete(`/api/v2/admin/announcements/${announcementId}/groups/${groupId}/user/${userId}`);
        return true;
      }
      catch (e) {
        return false
      }
    }
  },
};

export default module;