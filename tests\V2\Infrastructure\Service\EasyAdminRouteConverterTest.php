<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Service;

use App\Controller\Admin\CourseCrudController;
use App\Entity\User;
use App\Service\SettingsService;
use App\V2\Application\Admin\LegacyAdminUrlGeneratorInterface;
use App\V2\Infrastructure\Security\FirewallException;
use App\V2\Infrastructure\Service\EasyAdminRouteConverter;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

class EasyAdminRouteConverterTest extends TestCase
{
    /**
     * This test demonstrates how to mock the LegacyAdminUrlGeneratorInterface
     * for unit testing classes that depend on it.
     *
     * @throws Exception|FirewallException
     */
    public function testGetEasyAdminUrlWithController(): void
    {
        // Mock the LegacyAdminUrlGeneratorInterface
        $adminUrlGenerator = $this->createMock(LegacyAdminUrlGeneratorInterface::class);

        // Set up expectations for the methods that will be called
        $adminUrlGenerator->expects($this->once())
            ->method('unsetAll')
            ->willReturnSelf();

        $adminUrlGenerator->expects($this->once())
            ->method('setController')
            ->with(CourseCrudController::class)
            ->willReturnSelf();

        $adminUrlGenerator->expects($this->once())
            ->method('setAction')
            ->with('index')
            ->willReturnSelf();

        $adminUrlGenerator->expects($this->once())
            ->method('addSignature')
            ->willReturnSelf();

        $adminUrlGenerator->expects($this->once())
            ->method('generateUrl')
            ->willReturn('/admin/courses');

        // Mock the AuthorizationChecker
        $authChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authChecker->method('isGranted')
            ->with(User::ROLE_CREATOR)
            ->willReturn(true);

        // Mock the SettingsService
        $settings = $this->createMock(SettingsService::class);

        // Create the EasyAdminRouteConverter with the mocked dependencies
        $converter = new EasyAdminRouteConverter($adminUrlGenerator, $authChecker, $settings);

        // Create a request with the necessary parameters
        $request = new Request();
        $request->query->set('ea-route', 'courses');
        $request->query->set('ea-action', 'index');

        // Call the method being tested
        $response = $converter->getEasyAdminUrl($request);

        // Assert the response is a RedirectResponse with the expected URL
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals('/admin/courses', $response->getTargetUrl());
    }

    /**
     * Test that an exception is thrown when the user doesn't have the required role.
     *
     * @throws Exception
     */
    public function testGetEasyAdminUrlWithForbiddenAccess(): void
    {
        // Mock the dependencies
        $adminUrlGenerator = $this->createMock(LegacyAdminUrlGeneratorInterface::class);
        $authChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authChecker->method('isGranted')
            ->willReturn(false);
        $settings = $this->createMock(SettingsService::class);

        // Create the EasyAdminRouteConverter
        $converter = new EasyAdminRouteConverter($adminUrlGenerator, $authChecker, $settings);

        // Create a request with the necessary parameters
        $request = new Request();
        $request->query->set('ea-route', 'courses');

        // Expect a FirewallException to be thrown
        $this->expectException(FirewallException::class);

        // Call the method being tested
        $converter->getEasyAdminUrl($request);
    }
}
