<template>
  <div class="ItemCard" :class="{ 'selected': isSelected }">
    <div class="body">
      <div class="item-name"><b>{{ item.name }}</b></div>
      <div class="item-description"> {{ item.description }}</div>
    </div>
    <div class="controls">
      <Spinner v-if="item.isLoading"/>
      <span v-else-if="isSelected" class="selected">
        {{ $t(selectedText) }}
      </span>
      <button v-else class="btn btn-sm btn-primary" @click="$emit('add')">
        {{ $t(buttonText) }}
      </button>
    </div>
  </div>
</template>

<script>
import Spinner from "../admin/components/base/Spinner.vue";
import SelectionModalItem from '../models/selectionModalItem.model'
export default {
  name: "ItemCard",
  components: { Spinner },
  props: {
    item: {
      type: [SelectionModalItem, Object],
      default: () => new SelectionModalItem()
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    buttonText: {
      type: String,
      default: ''
    },
    selectedText: {
      type: String,
      default: ''
    }
  },
}
</script>
<style scoped lang="scss">
.ItemCard {
  border: solid 1px #e2e2e2;
  background-color: #fff;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: .75rem;

  .body {
    display: flex;
    flex-direction: column;

    .item-description {
      color: #999;
    }
  }

  .controls {
    display: flex;
    justify-content: center;
    align-items: center;

    &:deep(.loader) {
      margin: 0 1.5rem;
      font-size: 20px;
    }
  }
}

.selected {
  opacity: .8;
}
</style>