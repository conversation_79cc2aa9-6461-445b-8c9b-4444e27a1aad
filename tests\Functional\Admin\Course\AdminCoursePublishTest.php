<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TypeCourse;
use App\Enum\ChapterContent;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class AdminCoursePublishTest extends FunctionalTestCase
{
    use ChapterHelperTrait;
    use SeasonHelperTrait;
    use ChapterTypeHelperTrait;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider publishCourseProvider
     */
    public function testPublishCourse(
        int $typeCourseId,
        bool $active,
        bool $incompleteChapters,
        int $statusCode,
        bool $error,
        string $message
    ): void {
        $typeCourse = $this->createAndGetTypeCourse(
            id: $typeCourseId,
        );

        $course = $this->createAndGetCourse(typeCourse: $typeCourse, active: $active);

        if ($incompleteChapters) {
            $chapterType = $this->createAndGetChapterTypeRevised(id: ChapterContent::CONTENT_TYPE);
            $season = $this->createAndGetSeason(course: $course);
            $chapter = $this->createAndGetChapter(course: $course, season: $season, chapterType: $chapterType);
            $this->getEntityManager()->refresh($chapter);
        }

        $response = $this->getCoursesListResponse($course->getId());
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals($statusCode, $responseData['status']);
        $this->assertEquals($error, $responseData['error']);
        $this->assertEquals($message, $responseData['message']);
    }

    public static function publishCourseProvider(): \Generator
    {
        yield 'On-site course publication' => [
            'typeCourseId' => TypeCourse::TYPE_PRESENCIAL,
            'active' => false,
            'incompleteChapters' => false,
            'statusCode' => 200,
            'error' => false,
            'message' => 'Curso publicado',
        ];

        yield 'On-site course deactivation' => [
            'typeCourseId' => TypeCourse::TYPE_PRESENCIAL,
            'active' => true,
            'incompleteChapters' => false,
            'statusCode' => 200,
            'error' => false,
            'message' => 'Curso marcado como no publicado',
        ];

        yield 'Online course deactivation' => [
            'typeCourseId' => TypeCourse::TYPE_TELEFORMACION,
            'active' => true,
            'incompleteChapters' => false,
            'statusCode' => 200,
            'error' => false,
            'message' => 'Curso marcado como no publicado',
        ];

        yield 'Online course activation with incomplete chapters' => [
            'typeCourseId' => TypeCourse::TYPE_TELEFORMACION,
            'active' => false,
            'incompleteChapters' => true,
            'statusCode' => 200,
            'error' => true,
            'message' => 'No se puede publicar porque el curso esta incompleto',
        ];

        yield 'Online course deactivation with incomplete chapters' => [
            'typeCourseId' => TypeCourse::TYPE_TELEFORMACION,
            'active' => true,
            'incompleteChapters' => true,
            'statusCode' => 200,
            'error' => false,
            'message' => 'Curso marcado como no publicado',
        ];
    }

    private function getCoursesListResponse($courseId): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::coursesPublishEndPoint($courseId),
            bearerToken: $userToken
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Chapter::class,
        ]);
        parent::tearDown();
    }
}
