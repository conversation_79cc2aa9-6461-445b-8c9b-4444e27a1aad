<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementInspectorAccess;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementUser;
use App\Entity\ChatChannel;
use App\Entity\ChatChannelUser;
use App\Entity\ChatServer;
use App\Entity\Course;
use App\Entity\TypeCourse;
use App\Entity\TypeIdentification;
use App\Entity\UserFieldsFundae;
use App\Entity\UserToken;
use App\Service\Annoucement\Email\AnnouncementEmailService;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\Course\Common\UserCourseService;
use App\Service\SettingsService;
use App\Utils\TimeZoneConverter\TimeZoneConverter;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementService
{
    private EntityManagerInterface $em;
    private RequestStack $requestStack;
    private TranslatorInterface $translator;
    private SettingsService $settings;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    private AnnouncementAprovedCriteriaService $announcementAprovedCriteriaService;
    private AnnouncementAlertTutorService $announcemementAlertTutorService;
    private TaskUserService $taskUserService;
    private AnnouncementEmailService $announcementEmailService;
    private AnnouncementNotificationService $announcementNotificationService;
    private UserCourseService $userCourseService;
    private Security $security;

    private const float MINIMUM_TOTAL_HOURS = 0.25;
    private const int MAX_PERSON_PER_GROUP = 80;

    private AnnouncementExtraService $announcementExtraService;

    public function __construct(
        EntityManagerInterface $em,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        SettingsService $settings,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        AnnouncementAprovedCriteriaService $announcementAprovedCriteriaService,
        AnnouncementAlertTutorService $announcemementAlertTutorService,
        TaskUserService $taskUserService,
        AnnouncementEmailService $announcementEmailService,
        AnnouncementNotificationService $announcementNotificationService,
        Security $security,
        AnnouncementExtraService $announcementExtraService,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->settings = $settings;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->announcementAprovedCriteriaService = $announcementAprovedCriteriaService;
        $this->announcemementAlertTutorService = $announcemementAlertTutorService;
        $this->taskUserService = $taskUserService;
        $this->announcementEmailService = $announcementEmailService;
        $this->announcementNotificationService = $announcementNotificationService;
        $this->security = $security;

        $this->announcementExtraService = $announcementExtraService;
        $this->userCourseService = $userCourseService;
    }

    public function getAnnouncementData(Announcement $announcement): array
    {
        $course = $announcement->getCourse();
        $typeCourse = $course->getTypeCourse();
        $guide = $announcement->getDidaticGuide();
        $createdAtTranslated = $this->generateCreatedAtText($announcement);

        /** @var ChatServer $chatServer */
        $chatServer = $this->em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_ANNOUNCEMENT, $announcement->getId(), true);
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        $chatChannel = $this->getChatChannel($chatServer);
        $channels = $this->getAnnouncementChatChannels($announcement, $chatServer);

        $numberOfSessions = $this->em->getRepository(AnnouncementGroupSession::class)->getTotalNumberSessionGroup($announcement);

        $allowedExtraSections = $this->announcementExtraService->getAllowedExtraSections();

        $filteredExtraSections = $this->announcementExtraService->filterExtraSections(
            $announcement->getExtra(),
            $allowedExtraSections
        );

        $locale = $this->security->getUser()->getLocale();

        $extra = $this->announcementExtraService->mapExtraDetailsFromRepo(
            $filteredExtraSections,
            $allowedExtraSections,
            $this->em,
            $locale,
            false
        );

        $announcementData = [
            'id' => $announcement->getId(),
            'course' => $this->collectCourseData($course, $announcement),
            'notifiedAt' => ($notifiedAt = $announcement->getNotifiedAt()) ? $notifiedAt : null,
            'createdBy' => $announcement->getCreatedBy()?->getId(),
            'createdAt' => $announcement->getCreatedAt(),
            'createdAtText' => $createdAtTranslated,
            'timezone' => $announcement->getTimezone(),
            'status' => $announcement->getStatus(),
            'type' => $typeCourse->getType(),
            'startAt' => $announcement->getStartAt(),
            'finishAt' => $announcement->getFinishAt(),
            'subsidized' => $announcement->getSubsidized(),
            'usersPerGroup' => $announcement->getUsersPerGroup(),
            'totalHours' => $announcement->getTotalHours(),
            'guideTitle' => $guide ? $guide->getFilename() : null,
            'guideURL' => $guide ? $host . '/' . $this->settings->get('app.announcement_didactic_guide') . $guide->getFilename() : null,
            'objectives' => $announcement->getObjectiveAndContents(),
            'temporization' => $announcement->getTemporalizationsData(),
            'aprovalCriteria' => $this->announcementAprovedCriteriaService->getAnnouncementAprovedCriteria($announcement),
            'hasAprovedCriteria' => $this->announcementAprovedCriteriaService->hasAprovedCriteria($announcement),
            'bonification' => $this->getBonificationData($announcement),
            'inspector' => $this->getInspectorData($announcement),
            'survey' => $this->getInformationSurveyData($announcement),
            'diploma' => $this->getInformationDiplomaData($announcement),
            'alertsTutor' => $this->announcemementAlertTutorService->getAnnouncementAlertsTutor($announcement),
            'hasTasks' => $this->settings->get('app.course.tasks.enabled') ? true : false,
            'hasMaterials' => $this->settings->get('app.course.materials.enabled') ? true : false,
            'percentageForAproved' => $this->announcementAprovedCriteriaService->getPercentageForAprovedAnnoucement($announcement),
            'server' => $chatServer ? $chatServer->getId() : null,
            'chatChannel' => $chatServer && $chatChannel ? $chatChannel->getId() : null,
            'channels' => $channels,
            'numberOfSessions' => (int) $numberOfSessions,
            'source' => $typeCourse->getDenomination(),
            'mainIdentification' => $this->em->getRepository(TypeIdentification::class)->getMainIdentificationForThePlatform($this->security->getUser()->getLocale()),
            'extra' => $extra,
            'shareEnabled' => $this->settings->get('app.announcement.managers.sharing'),
        ];

        $configurationAnnouncement = $this->announcementConfigurationsService->getConfigurationAnnouncement($announcement);

        return array_merge($announcementData, $configurationAnnouncement);
    }

    private function collectCourseData(Course $course, Announcement $announcement): array
    {
        $courseType = $course->getTypeCourse();
        $courseCategory = $course->getCategory();
        $courseData = [
            'id' => $course->getId(),
            'code' => $course->getCode(),
            'name' => $course->getName(),
            'description' => $course->getDescription(),
            'image' => $course->getImage(),
            'thumbnailUrl' => $course->getThumbnailUrl(),
            'type' => $courseType ? $courseType->getName() : 'Teleformación',
            'typeID' => $courseType ? $courseType->getId() : 1,
            'icon' => $courseType ? $courseType->getIcon() : 'fa fa-cloud',
            'hasBonification' => $this->announcementConfigurationsService->hasBonification($announcement),
            'source' => $courseType->getDenomination(),
            'category' => [
                'id' => $courseCategory ? $courseCategory->getId() : -1,
                'name' => $courseCategory ? $courseCategory->getName() : '--',
            ],
            'locale' => $course->getLocale() ?? $this->settings->get('app.defaultLocale'),
            'courseDataDetailStats' => $this->userCourseService->courseDataDetailsStats($course),
        ];

        return $courseData;
    }

    private function generateCreatedAtText(Announcement $announcement): string
    {
        $createdAtTranslated = $this->translator->trans('library.createdAtView', [], 'messages');
        $createdBy = $announcement->getCreatedBy();
        $createdAtFormatted = $announcement->getCreatedAt()->format('d-m-Y');
        $createdAtTime = $announcement->getCreatedAt()->format('H:i');

        return strtr($createdAtTranslated, [
            '{email}' => $createdBy->getEmail(),
            '{date}' => $createdAtFormatted,
            '{time}' => $createdAtTime,
        ]);
    }

    public function getBonificationData(Announcement $announcement): array
    {
        $hasBonification = $this->announcementConfigurationsService->hasBonification($announcement);
        $course = $announcement->getCourse();
        $courseType = $course->getTypeCourse();

        return [
            'hasBonification' => $hasBonification,
            'typeAction' => $announcement->getActionType() ?? '--',
            'codeAction' => $announcement->getActionCode() ?? '--',
            'modalidad' => $courseType ? $courseType->getName() : 'Teleformación',
            'denomination' => $announcement->getDenomination() ?? '--',
            'maxByGroup' => $announcement->getUsersPerGroup() ?? '--',
            'contactPerson' => $announcement->getContactPerson() ?? '--',
            'contactPersonEmail' => $announcement->getContactPersonEmail() ?? '--',
            'contactPersonTelephone' => $announcement->getContactPersonTelephone() ?? '--',
        ];
    }

    public function getInformationDiplomaData(Announcement $announcement): array
    {
        $hasDiploma = $this->announcementConfigurationsService->hasDiploma($announcement);

        if (!$hasDiploma) {
            return [];
        }

        return [
            'active' => $hasDiploma,
            'confirmation_obtaining' => $announcement->isIsConfirmationRequiredDiploma() ?? false,
            'preview' => $this->announcementConfigurationsService->getPreviewDiploma($announcement),
            'complete_survey' => $this->announcementConfigurationsService->hasSurvey($announcement),
        ];
    }

    public function getInspectorData(Announcement $announcement): array
    {
        $announcementInspectorAccess = $this->em->getRepository(AnnouncementInspectorAccess::class)->findOneBy(['announcement' => $announcement]);

        if (!$announcementInspectorAccess) {
            return [];
        }

        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        // Weird error
        $tokenUser = $announcementInspectorAccess->getToken();
        $tokenUser = $this->em->getRepository(UserToken::class)->find($tokenUser?->getId());

        $token = $tokenUser ? $tokenUser->getToken() : null;
        $urlAccess = $token ? $host . '/inspector/view?token=' . $token : null;

        $extraToken = $tokenUser ? $tokenUser->getExtra() : [];

        $user = $extraToken['user'] ?? '--';
        $password = $extraToken['password'] ?? '--';

        return [
            'urlAccess' => $urlAccess,
            'user' => $user,
            'password' => $password,
        ];
    }

    public function getInformationSurveyData(Announcement $announcement): array
    {
        $hasSurvey = $this->announcementConfigurationsService->hasSurvey($announcement);

        if (!$hasSurvey) {
            return [];
        }

        return [
            'sastifaction' => $this->announcementConfigurationsService->hasSurvey($announcement),
            'typeSurvey' => $this->announcementConfigurationsService->getSurveyName($announcement),
            'preview' => 'assets_announcement/preview_survey/fundae.png',
        ];
    }

    private function hasAnnouncementTasks(Announcement $announcement)
    {
        $taskAnnouncement = $this->taskUserService->getAnnouncementTasks($announcement);
        $taskCourse = $this->taskUserService->getAnnouncementCourse($announcement);

        $tasks = array_merge($taskCourse, $taskAnnouncement);

        return \count($tasks) > 0;
    }

    private function getForumChannel(ChatServer $server)
    {
        $channelRepository = $this->em->getRepository(ChatChannel::class);

        $forumChannel = $channelRepository->getChannelByType($server, Announcement::CHAT_CHANNEL_FORUM);
        if (!$forumChannel) {
            $forumChannel = $channelRepository->createNormalChannel($server, null, 'FORUM', Announcement::CHAT_CHANNEL_FORUM);
        }

        return $forumChannel;
    }

    private function getAnnouncementChatChannels(Announcement $announcement, ChatServer $server): array
    {
        $chatChannel = $this->getChatChannel($server);
        $channels = [];
        foreach ($announcement->getAnnouncementGroups() as $g) {
            $forumChannel = $this->em->getRepository(ChatChannel::class)->getChannelByEntityType(
                $server,
                Announcement::CHAT_CHANNEL_FORUM,
                $g->getId(),
                AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP,
                true
            );
            $publicChannel = $this->em->getRepository(ChatChannel::class)->getChannelByEntityType(
                $server,
                Announcement::CHAT_CHANNEL_GROUP_CHAT,
                $g->getId(),
                AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP,
                true
            );

            $channels[] = [
                'group' => $g->getId(),
                'channels' => [
                    'forum' => [
                        'id' => $forumChannel->getId(),
                    ],
                    'public' => [
                        'id' => $publicChannel->getId(),
                    ],
                    'groups' => $this->checkPublicChannelGroup($g, $publicChannel),
                    'chat' => [
                        'id' => $chatChannel->getId(),
                        'name' => $chatChannel->getName(),
                        'type' => $chatChannel->getType(),
                        'entityId' => $g->getId(),
                        'entityType' => AnnouncementGroup::TYPE_ANNOUNCEMENT_DIRECT,
                    ],
                ],
            ];
        }

        return $channels;
    }

    private function getChatChannel(ChatServer $server)
    {
        $channelRepository = $this->em->getRepository(ChatChannel::class);
        $chatChannel = $channelRepository->getChannelByType($server, Announcement::CHAT_CHANNEL_DIRECT);
        if (!$chatChannel) {
            $chatChannel = $channelRepository->createDirectChannel($server, null, 'CHAT');
        }

        return $chatChannel;
    }

    /**
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function checkPublicChannelGroup(AnnouncementGroup $announcementGroup, ChatChannel $channel): array
    {
        $groupChannels = [];

        $usersInChannel = $this->em->getRepository(ChatChannelUser::class)
            ->createQueryBuilder('ccu')
            ->select('count(ccu.id) as total')
            ->where('ccu.channel = :channel')
            ->setParameter('channel', $channel)
            ->getQuery()
            ->getSingleScalarResult();

        $usersInAnnouncement = $this->em->getRepository(AnnouncementUser::class)
            ->createQueryBuilder('au')
            ->select('count(au.id) as total')
            ->where('au.announcementGroup =:group')
            ->setParameter('group', $announcementGroup)
            ->getQuery()
            ->getSingleScalarResult();

        if ($usersInChannel != $usersInAnnouncement) { // Fill all users to public channel
            $this->em->getRepository(AnnouncementUser::class)->addUsersToChannel($announcementGroup, $channel);
        }

        foreach ($channel->getChatChannels() as $childChannel) { // Get all groups
            $groupChannels[] = [
                'id' => $childChannel->getId(),
                'name' => $childChannel->getName(),
                'type' => $childChannel->getType(),
            ];
        }

        return $groupChannels;
    }

    public function isValidateCancelAnnouncement(Announcement $announcement): array
    {
        $valid = true;
        $status = [
            'GENERAL' => [
                'i18n' => true,
                'errors' => [],
            ],
        ];

        if (null == $announcement->getNotifiedAt()) {
            $valid = false;
            $status['GENERAL']['errors'][] = 'ANNOUNCEMENT.MODALS.CANCEL_NOTIFICATED';
        }

        $currentDate = date('Y-m-d H:i:s');
        if ($currentDate > $announcement->getStartAt()) {
            $valid = false;
            $status['GENERAL']['errors'][] = 'ANNOUNCEMENT.MODALS.CANCEL_ERROR';
        }

        return [
            'valid' => $valid,
            'data' => $status,
        ];
    }

    public function cancelAnnouncement(Announcement $announcement): array
    {
        $status = $this->isAnnouncementValid($announcement);
        if (!$status['valid']) {
            return $status;
        }
        $status = $this->isValidateCancelAnnouncement($announcement);
        if (!$status['valid']) {
            return $status;
        }

        try {
            $timezone = $announcement->getTimezone();
            if (empty($timezone)) {
                $timezone = $this->settings->get('app.defaultTimezone');
            }
            $tz = new \DateTimeZone($timezone);
            $currentDate = new \DateTime('now', $tz);
            $sendNotification = $currentDate < $announcement->getFinishAt();
            if ($sendNotification && $this->announcementConfigurationsService->hasEmailNotificationOnAnnouncement()) {
                $this->announcementEmailService->sendEmailAnnouncement($announcement);
            }

            $announcement->setStatus(Announcement::STATUS_CONFIGURATION);
            $this->em->persist($announcement);
            $this->em->flush();

            $this->verifyStatusCourse($announcement->getCourse());

            if ($sendNotification && $this->announcementConfigurationsService->hasNotificationOnAnnouncement()) {
                $this->announcementNotificationService->createNoticationAboutAnnoucement($announcement);
            }
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'data' => 'Failed to send notification: ' . $e->getMessage(),
            ];
        }

        return ['valid' => true, 'data' => $status];
    }

    public function activateAnnouncement(Announcement $announcement): array
    {
        $status = $this->isAnnouncementValid($announcement);
        if (!$status['valid']) {
            return $status;
        }
        try {
            $timezone = $announcement->getTimezone();
            if (empty($timezone)) {
                $timezone = $this->settings->get('app.defaultTimezone');
            }
            $tz = new \DateTimeZone($timezone);
            $currentDate = new \DateTime('now', $tz);
            $sendNotification = $currentDate < $announcement->getFinishAt();
            if ($sendNotification && $this->announcementConfigurationsService->hasEmailNotificationOnAnnouncement()) {
                $this->announcementEmailService->sendEmailAnnouncement($announcement);
            }

            $dt = new \DateTimeImmutable('now', new \DateTimeZone($announcement->getTimezone()));
            $announcement->setNotifiedAt($dt)
                ->setStatus(Announcement::STATUS_ACTIVE);
            $this->em->persist($announcement);
            $this->em->flush();

            $this->verifyStatusCourse($announcement->getCourse());

            if ($sendNotification && $this->announcementConfigurationsService->hasNotificationOnAnnouncement()) {
                $this->announcementNotificationService->createNoticationAboutAnnoucement($announcement);
            }
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'data' => 'Failed to send notification: ' . $e->getMessage(),
            ];
        }

        return ['valid' => true, 'data' => $status];
    }

    private function verifyStatusCourse(Course $course)
    {
        if ($course->getActive()) {
            return;
        }

        $course->setActive(true);
        $this->em->persist($course);
        $this->em->flush();
    }

    public function isAnnouncementValid(Announcement $announcement): array
    {
        $valid = true;
        $status = [
            'GENERAL' => [
                'i18n' => true,
                'errors' => [],
            ],
        ];

        if ($announcement->getStartAt() > $announcement->getFinishAt()) {
            $valid = false;
            $status['GENERAL']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.INVALID_START_AT_FINISH_AT';
        }

        if ($announcement->getTotalHours() < self::MINIMUM_TOTAL_HOURS) {
            $valid = false;
            $status['GENERAL']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.TOTAL_HOURS_REQUIRED';
        }

        /** @var AnnouncementConfigurationType[] $announcementConfigurations */
        $announcementConfigurations = $this->em->getRepository(AnnouncementConfigurationType::class)
            ->createQueryBuilder('cca')
            ->select('cca')
            ->join('cca.announcementConfigurations', 'ac')
            ->where('ac.announcement =:announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();

        foreach ($announcementConfigurations as $conf) {
            switch ($conf->getId()) {
                case AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION:
                    $result = $this->validateAnnouncementTemporalization($announcement);
                    if (!$result['valid']) {
                        $valid = false;
                        $status = array_merge($status, $result['errors']);
                    }
                    break;
                case AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE:
                    if (null === $announcement->getDidaticGuide()) {
                        // Announcement is subsidized, didactic guide is required
                        $valid = false;
                        $status['GENERAL']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE_REQUIRED';
                    }

                    $result = $this->validateAnnouncementSubsidizedStatus($announcement);
                    if (!$result['valid']) {
                        $valid = false;
                        $status = array_merge($status, $result['errors']);
                    }
                    break;
            }
        }

        $result = $this->validateAnnouncementGroups($announcement);
        if (!$result['valid']) {
            $valid = false;
            $status = array_merge($status, $result['errors']);
        }

        return [
            'valid' => $valid,
            'data' => $status,
        ];
    }

    private function validateAnnouncementTemporalization(Announcement $announcement): array
    {
        $valid = true;
        $error = [
            'TIMING' => [
                'i18n' => true,
                'errors' => [],
            ],
        ];
        $timing = $this->em->getRepository(AnnouncementTemporalization::class)
            ->createQueryBuilder('at')
            ->select('at.startedAt', 'at.finishedAt')
            ->addSelect('c.id as chapterId', 'c.title')
            ->join('at.chapter', 'c')
            ->where('at.announcement =:announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();

        foreach ($timing as $timingValue) {
            $t = $timingValue;
            $t['timezone'] = $announcement->getTimezone();
            $t = TimeZoneConverter::checkTimezone($t);

            $timingErrors = [];
            if (
                $t['startedAt'] < $announcement->getStartAt() || $t['startedAt'] > $announcement->getFinishAt()
            ) {
                $timingErrors[] = 'startedAt must be between announcement start and finish';
            }
            if ($t['finishedAt'] < $announcement->getStartAt() || $t['finishedAt'] > $announcement->getFinishAt()) {
                $timingErrors[] = 'finishedAt must be between announcement start and finish';
            }
            if (\count($timingErrors) > 0) {
                $error['TIMING']['errors'][] = [
                    $t['chapterId'] => $timingErrors,
                ];
                $valid = false;
            }
        }

        return [
            'valid' => $valid,
            'errors' => $error,
        ];
    }

    private function validateAnnouncementSubsidizedStatus(Announcement $announcement): array
    {
        $valid = true;
        $error = [
            'SUBSIDIZED' => [
                'i18n' => true,
                'errors' => [],
            ],
        ];
        if (empty($announcement->getActionType())) {
            $error['SUBSIDIZED']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.ACTION_TYPE_REQUIRED';
        }
        if (empty($announcement->getActionCode())) {
            $error['SUBSIDIZED']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.ACTION_CODE_REQUIRED';
        }
        if ($announcement->getUsersPerGroup() < 1 || $announcement->getUsersPerGroup() > self::MAX_PERSON_PER_GROUP) {
            $error['SUBSIDIZED']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.USERS_PER_GROUP_MISMATCH';
        }

        if (empty($announcement->getContactPerson())) {
            $error['SUBSIDIZED']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_REQUIRED';
        }
        if (empty($announcement->getContactPersonEmail())) {
            $error['SUBSIDIZED']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL_REQUIRED';
        }
        if (empty($announcement->getContactPersonTelephone())) {
            $error['SUBSIDIZED']['errors'][] = 'ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_TELEPHONE_REQUIRED';
        }

        /**
         * Validate all users.
         */
        $invalidUsers = [];
        $invalidUsersIds = [];
        foreach ($announcement->getCalled() as $announcementUser) {
            $user = $announcementUser->getUser();
            $fieldsFundae = $user->getUserFieldsFundae();
            if (!$fieldsFundae) {
                $fieldsFundae = new UserFieldsFundae();
                $fieldsFundae->setUser($user);
                $user->setUserFieldsFundae($fieldsFundae);
                $this->em->persist($user);
                $invalidUsers[] = $user->getFullName();
                $invalidUsersIds[] = $user->getId();
            } elseif (!$fieldsFundae->getAllFieldCompleted()) {
                $invalidUsers[] = $user->getFullName();
                $invalidUsersIds[] = $user->getId();
            }
        }

        $this->em->flush();
        $isProfileValid = true;
        if (\count($invalidUsers) > 0) {
            $error['INCOMPLETE_PROFILE'] = [
                'i18n' => false,
                'errors' => $invalidUsers,
                'fields_fundae' => $invalidUsersIds,
            ];
            $isProfileValid = \count($invalidUsers) > 0;
        }

        $valid = 0 === \count($error['SUBSIDIZED']['errors']) && $isProfileValid;

        return [
            'valid' => $valid,
            'errors' => $error,
        ];
    }

    public function validateAnnouncementGroups(Announcement $announcement): array
    {
        $valid = true;
        $errors['GROUPS'] = [
            'i18n' => true,
            'errors' => [],
        ];
        /** @var AnnouncementGroup[] $announcementGroups */
        $announcementGroups = $announcement->getAnnouncementGroups()->toArray();
        if (\count($announcementGroups) < 1) {
            $valid = false;
            $errors['GROUPS']['errors'][] = 'No groups defined';

            return $errors;
        }

        $type = $announcement->getCourse()->getTypeCourse();

        foreach ($announcementGroups as $group) {
            $groupName = $this->translator->trans('announcements.common.group', [], 'messages') . ' ' . $group->getGroupNumber();
            $sessionsFailed = false;
            $tutor = $group->getAnnouncementTutor();
            $error = [];
            if (!$tutor) {
                $error[] = 'Tutor is required';
            }
            $users = $group->getAnnouncementUsers();
            if ($users->count() < 1) {
                $valid = false;
                $error[] = 'ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY';
            }

            if ('online' !== $type->getType()) {
                // Validate the sessions
                $sessions = $group->getAnnouncementGroupSessions();
                $sessionError = false;
                foreach ($sessions as $session) {
                    if ($session->getStartAt() < $announcement->getStartAt() || $session->getStartAt() > $announcement->getFinishAt()) {
                        $sessionError = true;
                    }
                    if ($session->getFinishAt() < $announcement->getStartAt() || $session->getFinishAt() > $announcement->getFinishAt()) {
                        $sessionError = true;
                    }
                }
                if ($sessionError) {
                    $error[] = 'ANNOUNCEMENT.FORM.GROUP.ERROR_SESSION_BETWEEN_ANNOUNCEMENT';
                    $valid = false;
                }

                if ((TypeCourse::CODE_ONSITE === $type->getType() || TypeCourse::CODE_VIRTUAL_CLASSROOM === $type->getType()) && 0 === \count($sessions)) {
                    $valid = false;
                    $sessionsFailed = true;
                }
            }

            if ($sessionsFailed) {
                $error[] = 'ANNOUNCEMENT.FORM.GROUP.SESSIONS_REQUIRED';
            }
            $errors['GROUPS']['errors'][$groupName] = $error;
        }

        return [
            'valid' => $valid,
            'errors' => $errors,
        ];
    }
}
