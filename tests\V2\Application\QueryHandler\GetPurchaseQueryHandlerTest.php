<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\Entity\User;
use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Hydrator\Purchase\PurchaseHydratorCollection;
use App\V2\Application\Query\GetPurchase;
use App\V2\Application\QueryHandler\GetPurchaseQueryHandler;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetPurchaseQueryHandlerTest extends TestCase
{
    private PurchaseRepository&MockObject $purchaseRepository;
    private PurchaseHydratorCollection&MockObject $purchaseHydratorCollection;
    private GetPurchaseQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchaseRepository = $this->createMock(PurchaseRepository::class);
        $this->purchaseHydratorCollection = $this->createMock(PurchaseHydratorCollection::class);
        $this->handler = new GetPurchaseQueryHandler(
            $this->purchaseRepository,
            $this->purchaseHydratorCollection
        );
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws Exception
     * @throws \Exception
     */
    public function testOk(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $purchase = PurchaseMother::create(
            id: $purchaseId,
            userId: $userId
        );

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId, $userId) {
                return $criteria->getId()->equals($purchaseId)
                    && $criteria->getUserId()->equals($userId);
            }))
            ->willReturn($purchase);

        $result = $this->handler->handle($query);

        $this->assertSame($purchase, $result);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws \Exception
     */
    public function testThrowsPurchaseNotFoundExceptionWhenUserIsNotOwner(): void
    {
        $otherUserId = IdMother::create(456);
        $purchaseId = UuidMother::create();

        $otherUser = $this->createMock(User::class);
        $otherUser->method('getId')->willReturn($otherUserId->value());

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $otherUser
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId, $otherUserId) {
                return $criteria->getId()->equals($purchaseId)
                    && $criteria->getUserId()->equals($otherUserId);
            }))
            ->willThrowException(new PurchaseNotFoundException());

        $this->expectException(PurchaseNotFoundException::class);
        $this->handler->handle($query);
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function testThrowsPurchaseNotFoundExceptionWhenPurchaseDoesNotExist(): void
    {
        $userIdInt = 123;
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userIdInt);

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new PurchaseNotFoundException());

        $this->expectException(PurchaseNotFoundException::class);

        $this->handler->handle($query);
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws PurchaseNotFoundException
     * @throws \Exception
     * @throws Exception
     */
    public function testThrowsRepositoryExceptionWhenRepositoryFails(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new PurchaseRepositoryException('Database error'));

        $this->expectException(PurchaseRepositoryException::class);
        $this->expectExceptionMessage('Database error');

        $this->handler->handle($query);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws \Exception
     * @throws Exception
     */
    public function testThrowsCriteriaExceptionWhenCriteriaIsInvalid(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new CriteriaException('Criteria error'));

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Criteria error');

        $this->handler->handle($query);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws Exception
     * @throws \Exception
     */
    public function testHandleWithItemsTrue(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $purchase = PurchaseMother::create(
            id: $purchaseId,
            userId: $userId
        );

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user,
            withItems: true
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId, $userId) {
                return $criteria->getId()->equals($purchaseId)
                    && $criteria->getUserId()->equals($userId);
            }))
            ->willReturn($purchase);

        $purchaseItem1 = PurchaseItemMother::create(purchaseId: $purchaseId);
        $purchaseItem2 = PurchaseItemMother::create(purchaseId: $purchaseId);
        $purchaseItems = new PurchaseItemCollection([$purchaseItem1, $purchaseItem2]);

        $this->purchaseHydratorCollection
            ->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(function (PurchaseCollection $collection, PurchaseHydrationCriteria $criteria) use ($purchaseItems) {
                foreach ($collection->all() as $purchase) {
                    $purchase->setPurchaseItems($purchaseItems);
                }
            });

        $result = $this->handler->handle($query);

        $this->assertSame($purchase, $result);
        $this->assertNotNull($result->getPurchaseItems());
        $this->assertCount(2, $result->getPurchaseItems()->all());
        $this->assertSame($purchaseItems, $result->getPurchaseItems());
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws Exception
     * @throws \Exception
     */
    public function testHandleWithItemsFalse(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $purchase = PurchaseMother::create(
            id: $purchaseId,
            userId: $userId
        );

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user,
            withItems: false
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId, $userId) {
                return $criteria->getId()->equals($purchaseId)
                    && $criteria->getUserId()->equals($userId);
            }))
            ->willReturn($purchase);

        $this->purchaseHydratorCollection
            ->expects($this->never())
            ->method('hydrate');

        $result = $this->handler->handle($query);

        $this->assertSame($purchase, $result);
    }
}
