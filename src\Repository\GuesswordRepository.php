<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Guessword;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Guessword>
 *
 * @method Guessword|null find($id, $lockMode = null, $lockVersion = null)
 * @method Guessword|null findOneBy(array $criteria, array $orderBy = null)
 * @method Guessword[]    findAll()
 * @method Guessword[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class GuesswordRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Guessword::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(Guessword $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(Guessword $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * Find a Guessword entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?Guessword
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $guessword = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $guessword;
    }
}
