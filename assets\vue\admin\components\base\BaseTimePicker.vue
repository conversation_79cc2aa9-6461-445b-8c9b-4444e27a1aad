<template>
  <div class="BaseTimePicker" :class="[variationClass, sizeClass, typeClass]" v-click-outside="handleClickOutside">

    <transition name="fade">
      <div class="controls" v-show="showControls">

        <div v-if="showPresets" class="presets">
          <button 
            v-for="preset in commonPresets" 
            :key="preset.value"
            @click="selectPreset(preset.value)"
            class="preset-btn"
            :class="{ active: isPresetActive(preset.value) }"
            :disabled="disabled"
          >
            {{ preset.label }}
          </button>
        </div>
      
        <div class="time-input-container">
          <div class="input-group">
            <label class="input-label" @click="focusHoursInput">{{ $t("HOURS") }}</label>
            <input
              ref="hoursInput"
              v-model="hours"
              type="number"
              :min="0"
              :max="maxHours"
              :disabled="disabled"
              class="time-input hours-input"
              @input="handleHoursChange"
              @focus="onInputFocus"
              @blur="onInputBlur"
            />
          </div>
          
          <span class="separator" @click="focusHoursInput">:</span>
          
          <div class="input-group">
            <label class="input-label" @click="focusMinutesInput">{{ $t("MINUTES") }}</label>
            <input
              ref="minutesInput"
              v-model="minutes"
              type="number"
              min="0"
              max="59"
              step="5"
              :disabled="disabled"
              class="time-input minutes-input"
              @input="handleMinutesChange"
              @focus="onInputFocus"
              @blur="onInputBlur"
            />
          </div>
        </div>
      </div>
    </transition>
    
    <div class="form-control" @click="toggleControlVisibility">
      {{ formatTotalTime($t("HOURS"), $t("MINUTES"), $t("TIME.NO_TIME")) }} <i class="icon fa fa-clock"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseTimePicker',
  
  props: {
    maxHours: {
      type: Number,
      default: Number.MAX_SAFE_INTEGER,
    },
    currentTime: {
      type: Number,
      default: 0,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showPresets: {
      type: Boolean,
      default: true,
    },
    variation: {
      type: String,
      default: 'primary',
      validator: (v) => ['primary', 'secondary', 'tertiary', 'error'].includes(v),
    },
    size: {
      type: String,
      default: 'm',
      validator: (s) => ['xs', 's', 'm', 'l'].includes(s),
    },
    type: {
      type: String,
      default: 'default',
      validator: (t) => ['default', 'rounded'].includes(t),
    },
  },
  
  data() {
    const initialHours = this.currentTime ? Math.floor(this.currentTime) : 0;
    const initialMinutes = this.currentTime ? Math.round((this.currentTime - initialHours) * 60) : 0;
    
    return {
      showControls: false,
      hours: initialHours,
      minutes: initialMinutes,
      commonPresets: [
        { label: 'X', value: 0 },
        { label: '15m', value: 0.25 },
        { label: '30m', value: 0.5 },
        { label: '1h', value: 1 },
        { label: '2h', value: 2 },
        { label: '4h', value: 4 },
        { label: '8h', value: 8 },
      ],
    };
  },
  
  computed: {
    variationClass() {
      return `variation--${this.variation}`;
    },
    sizeClass() {
      return `size--${this.size}`;
    },
    typeClass() {
      return `type--${this.type}`;
    },
    totalHours() {
      return this.hours + this.minutes / 60;
    },
  },
  
  watch: {
    currentTime: {
      immediate: true,
      handler(newVal) {
        if (newVal !== null && newVal !== this.totalHours) {
          this.hours = Math.floor(newVal);
          this.minutes = Math.round((newVal - this.hours) * 60);
        }
      },
    },
    totalHours() {
      this.$emit('change', this.totalHours || null);
    },
  },
  
  methods: {
    selectPreset(value) {
      this.hours = Math.floor(value);
      this.minutes = Math.round((value - this.hours) * 60);
    },
    
    isPresetActive(value) {
      return Math.abs(this.totalHours - value) < 0.01;
    },
    
    handleHoursChange() {
      this.hours = Math.max(0, Math.min(this.maxHours, parseInt(this.hours) || 0));
    },
    
    handleMinutesChange() {
      this.minutes = Math.max(0, Math.min(59, parseInt(this.minutes) || 0));
    },

    formatTotalTime(hourTag, minuteTag, noTimeTag) {
      // USE NATIVE LOCALISE NUMERAL CONTROL
      // {0} hora | {0} horas
      // {{ $t(itineraryId ? "ITINERARY.ITINERARY_SECTION.STATS_RESUMEN_AVG" :  "COURSE.COURSE_SECTION.STATS_RESUMEN_AVG", [totalAvg]) }}
      const h = this.hours;
      const m = this.minutes;
      if (h === 0 && m === 0) return noTimeTag;
      if (h === 0) return `${m} ${minuteTag.substring(0, minuteTag.length - 1)}${m !== 1 ? 's' : ''}`;
      if (m === 0) return `${h} ${hourTag.substring(0, hourTag.length - 1)}${h !== 1 ? 's' : ''}`;
      return `${h}h ${m}m`;
    },
    
    focusHoursInput() {
      if (!this.disabled) {
        this.$refs.hoursInput.focus();
      }
    },

    focusMinutesInput() {
      if (!this.disabled) {
        this.$refs.minutesInput.focus();
      }
    },
    
    onInputFocus() {
      // Add focus styling or behavior if needed
    },
    
    onInputBlur() {
      // Remove focus styling or behavior if needed
    },

    toggleControlVisibility() {
      this.showControls = !this.showControls;
    },

    handleClickOutside() {
      this.showControls = false;
    }
  },
};
</script>

<style scoped lang="scss">
.BaseTimePicker {
  position: relative;
  border-radius: var(--border-radius, 8px);
  background: var(--color-background, white);
  transition: all 0.3s ease;
  font-family: var(--font-family, system-ui, -apple-system, sans-serif);
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--color-neutral-light, #f9fafb);
  }

  .controls {
    position: absolute;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    top: 100%;
    background-color: white;
    z-index: 10;
    box-shadow: $shadow-elevation-2;
    .presets {
      display: flex;
      gap: var(--spacing-xs, 8px);
      padding: var(--spacing-m, 16px);
      flex-wrap: wrap;
      justify-content: center;
      
      .preset-btn {
        padding: var(--spacing-xs, 8px) var(--spacing-s, 12px);
        border: 2px solid var(--color-neutral, #e5e7eb);
        border-radius: var(--border-radius, 6px);
        background: var(--color-background, white);
        color: var(--color-text, #374151);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: var(--font-size-sm, 14px);
        font-weight: 500;
        min-width: 44px;
        
        &:hover:not(:disabled) {
          color: $color-primary;
          background: var(--color-primary-lightest, #eff6ff);
          border-color: var(--color-primary, #3b82f6);
          transform: translateY(-1px);
        }
        
        &.active {
          background: var(--color-primary, #3b82f6);
          color: white;
          border-color: var(--color-primary, #3b82f6);
          box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
    
    .time-input-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-m, 16px);
      background: var(--color-neutral-lightest, #d8eef8);
      border-radius: var(--border-radius, 6px);
      
      .input-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs, 8px);
        
        .input-label {
          font-size: var(--font-size-xs, 12px);
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin: 0;
          padding: 0;
        }
        
        .time-input {
          width: 80px;
          height: 48px;
          padding: var(--spacing-s, 12px);
          border: 2px solid var(--color-neutral, #e5e7eb);
          border-radius: var(--border-radius, 6px);
          text-align: center;
          font-size: var(--font-size-lg, 18px);
          font-weight: 600;
          color: var(--color-text, #374151);
          background: white;
          transition: all 0.2s ease;
          
          &:focus {
            outline: none;
            border-color: var(--color-primary, #3b82f6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: white;
          }
          
          &:disabled {
            background: var(--color-neutral-light, #f3f4f6);
            color: var(--color-text-disabled, #9ca3af);
            cursor: not-allowed;
          }
          
          // Remove spinner arrows
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
          
          &[type=number] {
            -moz-appearance: textfield;
          }
        }
      }
      
      .separator {
        font-size: var(--font-size-2xl, 24px);
        font-weight: bold;
        color: var(--color-primary, #3b82f6);
        margin-top: 20px;
      }
    }
  }

  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
  }

  .fade-enter, .fade-leave-to {
    opacity: 0;
  }
  
  .form-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  // Variation styles
  &.variation--primary {
    border-color: var(--color-primary, #3b82f6);
    
    .separator {
      color: var(--color-primary, #3b82f6);
    }
  }
  
  &.variation--secondary {
    border-color: var(--color-secondary, #6b7280);
    
    .separator {
      color: var(--color-secondary, #6b7280);
    }
  }
  
  &.variation--error {
    border-color: var(--color-error, #ef4444);
    
    .separator {
      color: var(--color-error, #ef4444);
    }
    
    .time-input:focus {
      border-color: var(--color-error, #ef4444);
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }
  
  // Size variations
  &.size--xs {
    padding: var(--spacing-xs, 8px);
    max-width: 280px;
    
    .preset-btn {
      padding: var(--spacing-2xs, 4px) var(--spacing-xs, 8px);
      font-size: var(--font-size-xs, 12px);
      min-width: 36px;
    }
    
    .time-input {
      width: 60px;
      height: 36px;
      font-size: var(--font-size-sm, 14px);
    }
  }
  
  &.size--s {
    padding: var(--spacing-s, 12px);
    max-width: 300px;
    
    .time-input {
      width: 70px;
      height: 42px;
      font-size: var(--font-size-base, 16px);
    }
  }
  
  &.size--l {
    padding: var(--spacing-l, 20px);
    max-width: 360px;
    
    .preset-btn {
      padding: var(--spacing-s, 12px) var(--spacing-m, 16px);
      font-size: var(--font-size-base, 16px);
      min-width: 52px;
    }
    
    .time-input {
      width: 90px;
      height: 56px;
      font-size: var(--font-size-xl, 20px);
    }
  }
  
  // Type variations
  &.type--rounded {
    border-radius: var(--border-radius-full, 24px);
    
    .preset-btn,
    .time-input,
    .time-input-container {
      border-radius: var(--border-radius-full, 24px);
    }
  }
}
</style>