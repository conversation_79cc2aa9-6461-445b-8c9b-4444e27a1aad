<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\DeleteCourseCreatorCommand;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Validator\Admin\DeleteCourseCreatorValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class DeleteCourseCreatorController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(Request $request, int $courseId, int $userId): Response
    {
        DeleteCourseCreatorValidator::validateDeleteCourseCreatorRequest([
            'course_id' => $courseId,
            'user_id' => $userId,
        ]);

        $user = RequestAttributeExtractor::extractUser($request);

        $this->execute(
            new DeleteCourseCreatorCommand(
                courseId: new Id($courseId),
                userId: new Id($userId),
                requestUser: $user,
            )
        );

        return new JsonResponse(
            data: null,
            status: Response::HTTP_NO_CONTENT,
        );
    }
}
