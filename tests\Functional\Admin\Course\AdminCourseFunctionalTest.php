<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\VcmsProject;
use App\Enum\ChapterContent;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\HelperTrait\VcmsProjectHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class AdminCourseFunctionalTest extends FunctionalTestCase
{
    use VcmsProjectHelperTrait;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUrlPreDataCourse(): void
    {
        $response = $this->getCoursePreDataResponse();

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('categories', $responseData);
        $this->assertArrayHasKey('multilingual', $responseData);
        $this->assertArrayHasKey('setCoursePoints', $responseData);
        $this->assertArrayHasKey('points', $responseData);
        $this->assertArrayHasKey('useSegment', $responseData);
        $this->assertArrayHasKey('setCourseLevel', $responseData);
        $this->assertArrayHasKey('courseDocumentation', $responseData);
        $this->assertArrayHasKey('typesCourse', $responseData);
        $this->assertArrayHasKey('surveysCourse', $responseData);
        $this->assertArrayHasKey('typeDiplomas', $responseData);
        $this->assertArrayHasKey('typeIndexDiploma', $responseData);

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testDetailCourse(): void
    {
        $course = $this->createAndGetCourse();
        $this->assertNotNull($course, 'Course should not be null after creation.');
        $this->assertInstanceOf(Course::class, $course, 'Course should be an instance of Course.');
        if (!$course instanceof Course) {
            $this->fail('Expected $course to be an instance of Course.');
        }

        $response = $this->getCoursesDetailResponse($course);
        $responseData = $this->extractResponseData($response);

        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('id', $responseData);
        $this->assertArrayHasKey('name', $responseData);
        $this->assertArrayHasKey('category', $responseData);
        $this->assertArrayHasKey('updatedBy', $responseData);
        $this->assertArrayHasKey('createdBy', $responseData);
        $this->assertArrayHasKey('createdAt', $responseData);
        $this->assertArrayHasKey('updatedAt', $responseData);
        $this->assertArrayHasKey('description', $responseData);
        $this->assertArrayHasKey('image', $responseData);
        $this->assertArrayHasKey('typeCourseId', $responseData);
        $this->assertArrayHasKey('typeCourse', $responseData);
        $this->assertArrayHasKey('icon', $responseData);
        $this->assertArrayHasKey('totalChapters', $responseData);
        $this->assertArrayHasKey('locale', $responseData);
        $this->assertArrayHasKey('languages', $responseData);
        $this->assertArrayHasKey('active', $responseData);
        $this->assertArrayHasKey('open', $responseData);
        $this->assertArrayHasKey('open_visible', $responseData);
        $this->assertArrayHasKey('isNew', $responseData);
        $this->assertArrayHasKey('translation', $responseData);
        $this->assertArrayHasKey('averageRating', $responseData);
        $this->assertArrayHasKey('usersStartCourse', $responseData);
        $this->assertArrayHasKey('usersFinishCourse', $responseData);
        $this->assertArrayHasKey('totalTimeCourse', $responseData);

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testCreatorAndUpdaterAreRetrieveEvenWhenAreDeleted(): void
    {
        $creator = $this->createAndGetUser(
            email: '<EMAIL>',
            password: self::DEFAULT_USER_PASSWORD,
        );

        $this->loginAndGetToken(
            email: $creator->getEmail(),
            password: self::DEFAULT_USER_PASSWORD
        );

        $course = $this->createAndGetCourse(
            name: 'Test Course',
            code: 'TEST-CODE-123',
        );

        $this->getEntityManager()->remove($creator);
        $this->getEntityManager()->flush();

        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCourseEndpoints::coursesDetailEndpoint($course->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('createdBy', $responseData);
        $this->assertArrayHasKey('updatedBy', $responseData);
        $this->assertEquals($creator->getEmail(), $responseData['createdBy']);
        $this->assertEquals($creator->getEmail(), $responseData['updatedBy']);

        $this->hardDeleteUsersByIds([$creator->getId()]);
    }

    private function getCoursesDetailResponse(Course $course): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminCourseEndpoints::coursesDetailEndpoint($course->getId()),
            [],
            [],
            [],
            $userToken
        );
    }

    private function getCoursePreDataResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCourseEndpoints::preDataEndpoint(),
            bearerToken: $userToken
        );
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([Course::class]);
        $this->truncateEntities([CourseCategory::class]);
        $this->truncateEntities([VcmsProject::class]);
        $this->truncateEntities([Chapter::class]);
        parent::tearDown();
    }

    private function getCoursesDeleteResponse(Course $course): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'DELETE',
            AdminCourseEndpoints::coursesDeleteEndPoint($course->getId()),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testDeleteCourse(): void
    {
        $course = $this->createAndGetCourse();
        $this->assertNotNull($course, 'Course should not be null after creation.');
        $this->assertInstanceOf(Course::class, $course, 'Course should be an instance of Course.');
        if (!$course instanceof Course) {
            $this->fail('Expected $course to be an instance of Course.');
        }

        $response = $this->getCoursesDeleteResponse($course);

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testVcmsChapterContentValidationTest(): void
    {
        $course = $this->createAndGetCourse();
        $season = $this->createAndGetSeason($course);

        $existingChapterType = $this->getEntityManager()->getRepository(ChapterType::class)->find(ChapterContent::VCMS_TYPE);
        if ($existingChapterType) {
            $this->getEntityManager()->remove($existingChapterType);
            $this->getEntityManager()->flush();
        }

        $chapterTypeVcms = $this->createAndGetChapterTypeRevised(
            id: ChapterContent::VCMS_TYPE,
            name: 'VCMS',
            type: 'content',
            code: ChapterContent::VCMS_CODE,
            icon: 'vcms.svg'
        );

        $vcmsProject1 = $this->createAndGetVcmsProject();
        $courseChapterVcms1 = $this->createAndGetChapter(course: $course, season: $season, chapterType: $chapterTypeVcms);
        $courseChapterVcms1->setVcmsProject($vcmsProject1);
        $this->getEntityManager()->persist($vcmsProject1);

        $courseChapterVcms2 = $this->createAndGetChapter(course: $course, season: $season, chapterType: $chapterTypeVcms);
        $vcmsProject2 = $this->createAndGetVcmsProject(
            title: 'Vcms Project 2',
            slides: [
                [
                    'id' => 1,
                    'template' => [
                        'value' => '"A"',
                        'gap' => 0,
                    ],
                    'elements' => [
                        [
                            'id' => 1,
                            'type' => 'text',
                            'content' => 'Hello World!',
                        ],
                    ],
                ],
            ],
        );

        $courseChapterVcms2->setVcmsProject($vcmsProject2);

        $courseChapterVcms3 = $this->createAndGetChapter(course: $course, season: $season, chapterType: $chapterTypeVcms);
        $vcmsProject3 = $this->createAndGetVcmsProject(
            title: 'Vcms Project 3',
            slides: [
                [
                    'id' => 1,
                    'template' => [
                        'value' => '"A"',
                        'gap' => 0,
                    ],
                    'elements' => [
                        [
                            'id' => 1,
                            'type' => 'text',
                            'content' => 'Hello World!',
                        ],
                    ],
                ],
                [
                    'id' => 2,
                    'template' => [
                        'value' => '"A"',
                        'gap' => 0,
                    ],
                    'elements' => [
                        [
                            'id' => 1,
                            'type' => 'text',
                            'content' => 'Hello World!',
                        ],
                    ],
                ],
            ]
        );

        $courseChapterVcms3->setVcmsProject($vcmsProject3);
        $this->getEntityManager()->flush();

        $this->assertEquals(3, $course->getChapters()->count(), 'Course should have 3 chapters');

        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::getChaptersCourse($course->getId()),
            body: ['url' => 'route-easyadmin'],
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $this->assertCount(3, $responseData['courseChapters']);

        $expectedChapters = [
            [
                'id' => $courseChapterVcms1->getId(),
                'name' => $courseChapterVcms1->getTitle(),
                'description' => $courseChapterVcms1->getDescription(),
                'typeId' => $courseChapterVcms1->getType()->getId(),
                'typeName' => $courseChapterVcms1->getType()->getName(),
                'order' => $courseChapterVcms1->getPosition(),
                'seasonId' => $courseChapterVcms1->getSeason()->getId(),
                'seasonName' => $courseChapterVcms1->getSeason()->getName(),
                'seasonOrder' => $courseChapterVcms1->getSeason()->getSort(),
                'icon' => $courseChapterVcms1->getType()->getIcon(),
                'hasContentCompleted' => false,
                'image' => null,
                'thumbnail-url' => null,
                'editUrl' => $responseData['courseChapters'][0]['editUrl'],
                'projectEditUrl' => $responseData['courseChapters'][0]['projectEditUrl'],
                'projectViewUrl' => $responseData['courseChapters'][0]['projectViewUrl'],
            ],
            [
                'id' => $courseChapterVcms2->getId(),
                'name' => $courseChapterVcms2->getTitle(),
                'description' => $courseChapterVcms2->getDescription(),
                'typeId' => $courseChapterVcms2->getType()->getId(),
                'typeName' => $courseChapterVcms2->getType()->getName(),
                'order' => $courseChapterVcms2->getPosition(),
                'seasonId' => $courseChapterVcms2->getSeason()->getId(),
                'seasonName' => $courseChapterVcms2->getSeason()->getName(),
                'seasonOrder' => $courseChapterVcms2->getSeason()->getSort(),
                'icon' => $courseChapterVcms2->getType()->getIcon(),
                'hasContentCompleted' => true,
                'image' => null,
                'thumbnail-url' => null,
                'editUrl' => $responseData['courseChapters'][1]['editUrl'],
                'projectEditUrl' => $responseData['courseChapters'][1]['projectEditUrl'],
                'projectViewUrl' => $responseData['courseChapters'][1]['projectViewUrl'],
            ],
            [
                'id' => $courseChapterVcms3->getId(),
                'name' => $courseChapterVcms3->getTitle(),
                'description' => $courseChapterVcms3->getDescription(),
                'typeId' => $courseChapterVcms3->getType()->getId(),
                'typeName' => $courseChapterVcms3->getType()->getName(),
                'order' => $courseChapterVcms3->getPosition(),
                'seasonId' => $courseChapterVcms3->getSeason()->getId(),
                'seasonName' => $courseChapterVcms3->getSeason()->getName(),
                'seasonOrder' => $courseChapterVcms3->getSeason()->getSort(),
                'icon' => $courseChapterVcms3->getType()->getIcon(),
                'hasContentCompleted' => true,
                'image' => null,
                'thumbnail-url' => null,
                'editUrl' => $responseData['courseChapters'][2]['editUrl'],
                'projectEditUrl' => $responseData['courseChapters'][2]['projectEditUrl'],
                'projectViewUrl' => $responseData['courseChapters'][2]['projectViewUrl'],
            ],
        ];

        $this->assertEquals($expectedChapters, $responseData['courseChapters']);
    }
}
