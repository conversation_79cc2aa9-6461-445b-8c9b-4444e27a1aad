<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Financial;

final class TaxRate
{
    private float $rate;

    public function __construct(float $rate)
    {
        if ($rate < 0 || $rate > 1) {
            throw new \InvalidArgumentException('Tax rate must be between 0.0 and 1.0');
        }

        $this->rate = round($rate, 4);
    }

    public function value(): float
    {
        return $this->rate;
    }

    public static function fromPercentage(float $percentage): self
    {
        return new self($percentage / 100);
    }

    public function asPercentage(): float
    {
        return $this->rate * 100;
    }

    public function asDecimal(): float
    {
        return $this->rate;
    }

    public function equals(TaxRate $other): bool
    {
        return $this->rate === $other->rate;
    }
}
