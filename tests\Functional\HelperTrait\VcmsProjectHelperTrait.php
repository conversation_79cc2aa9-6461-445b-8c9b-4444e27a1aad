<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\VcmsProject;
use App\Tests\Mother\Entity\VcmsProjectMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait VcmsProjectHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetVcmsProject(
        ?int $id = null,
        ?string $title = null,
        ?array $slides = null,
        ?string $view = null,
        ?bool $progressive = null,
    ): VcmsProject {
        $entity = VcmsProjectMother::create(
            title: $title,
            slides: $slides,
            view: $view,
            progressive: $progressive,
        );

        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();

        return $entity;
    }
}
