<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Export;
use App\Entity\Task;
use App\Entity\User;

class ExportMother
{
    public const string DEFAULT_TYPE = 'test-type';

    public static function create(
        ?int $id = null,
        ?User $createdBy = null,
        ?User $updatedBy = null,
        ?User $deletedBy = null,
        ?Task $task = null,
        ?string $type = null,
        ?array $meta = null,
        ?string $filename = null,
        ?\DateTimeInterface $finishedAt = null,
        ?\DateTime $availableUntil = null,
    ): Export {
        $export = new Export();

        if (null !== $id) {
            $export->setId($id);
        }

        $export->setCreate($createdBy);
        $export->setType($type ?? self::DEFAULT_TYPE);
        $export->setTask($task);
        $export->setUpdate($updatedBy);
        $export->setMeta($meta);
        $export->setFilename($filename);
        $export->setFinishedAt($finishedAt);
        $export->setAvailableUntil($availableUntil ?? new \DateTime('+1 day'));

        return $export;
    }
}
