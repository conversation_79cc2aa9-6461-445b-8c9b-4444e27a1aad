<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PatchPurchasableItemCommand;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Purchase\PatchPurchasableItemInputTransformer;
use App\V2\Infrastructure\Validator\Admin\PatchPurchasableItemValidator;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PatchPurchasableItemController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUuidException
     * @throws InvalidCurrencyCodeException
     */
    public function __invoke(string $purchasableItemId, Request $request, ParameterBagInterface $parameterBag): Response
    {
        UuidValidator::validateUuid($purchasableItemId);
        $payload = json_decode($request->getContent(), true) ?? [];

        PatchPurchasableItemValidator::validatePatchPurchasableItemRequest($payload);

        if (isset($payload['price_amount']) && !isset($payload['price_currency'])) {
            $payload['price_currency'] = $parameterBag->get('app.currency_code');
        }

        $this->execute(
            new PatchPurchasableItemCommand(
                purchasableItemId: new Uuid($purchasableItemId),
                input: PatchPurchasableItemInputTransformer::fromPayload($payload),
            )
        );

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }
}
