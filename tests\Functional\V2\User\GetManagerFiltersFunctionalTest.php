<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use App\Tests\Functional\V2\Fixtures\FilterFixtureTrait;
use App\Tests\Functional\V2\Fixtures\ManagerFilterFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetManagerFiltersFunctionalTest extends FunctionalTestCase
{
    use FilterFixtureTrait;
    use ManagerFilterFixtureTrait;

    private const string EMAIL_MANAGER = '<EMAIL>';
    private ?User $manager;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->manager = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: self::EMAIL_MANAGER);
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1)
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(-1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ], $content['metadata']);
    }

    public function testGetManagerFilters(): void
    {
        $filter1 = $this->setAndGetFilterInRepository(
            id: new Id(1),
            filterCategoryId: new Id(1),
            name: 'Filter 1',
        );
        $filter2 = $this->setAndGetFilterInRepository(
            id: new Id(2),
            filterCategoryId: new Id(1),
            name: 'Filter 2',
        );
        $filter3 = $this->setAndGetFilterInRepository(
            id: new Id(3),
            filterCategoryId: new Id(2),
            name: 'Filter 3',
        );

        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->manager->getId()),
            filterId: new Id(1),
        );

        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->manager->getId()),
            filterId: new Id(2),
        );

        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->manager->getId()),
            filterId: new Id(3),
        );

        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(3, $data);
        foreach ($data as $filterData) {
            $expected = match ($filterData['id']) {
                1 => [
                    'id' => $filter1->getId()->value(),
                    'name' => 'Filter 1',
                    'category_id' => 1,
                ],
                2 => [
                    'id' => $filter2->getId()->value(),
                    'name' => 'Filter 2',
                    'category_id' => 1,
                ],
                3 => [
                    'id' => $filter3->getId()->value(),
                    'name' => 'Filter 3',
                    'category_id' => 2,
                ],
                default => $this->fail('Unexpected filter result')
            };

            $this->assertEquals($expected, $filterData);
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([$this->manager->getId()]);
        parent::tearDown();
    }
}
