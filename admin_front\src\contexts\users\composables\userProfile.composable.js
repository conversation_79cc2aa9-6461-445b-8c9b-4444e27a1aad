import { computed, inject, onMounted, reactive, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import UserStatsView from '@/contexts/users/components/profile/UserStatsView.vue'
import UserFiltersView from '@/contexts/users/components/profile/UserFiltersView.vue'
import TabModel from '@/contexts/shared/models/tab.model.js'
import { useUserProfileStore } from '@/contexts/users/stores/userProfile.store.js'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'
import { USER_PERMISSION_LIST } from '@/contexts/users/constants/userPermission.constants.js'
import { UserProfileFilterModel } from '@/contexts/users/models/userProfileFilter.model.js'
import { CategoryFilterModel } from '@/contexts/shared/models/categoryFilter.model.js'

export function useUserProfileComposable() {
  const links = ref([])
  const { isLoading, loadingError, userData } = storeToRefs(useUserProfileStore())
  const { loadProfile } = useUserProfileStore()
  const i18n = useI18n()
  const route = useRoute()

  const tabsConfig = reactive({
    tabs: [],
    current: 'stats',
  })

  const currentTabView = computed(
    () =>
      ({
        stats: UserStatsView,
        filters: UserFiltersView,
        management: UserFiltersView,
      })[tabsConfig.current] || UserStatsView
  )

  const sampleCategoryList = [
    new UserProfileFilterModel({ id: 1, category: 'Pais', filters: [{ id: 1, name: 'lorem' }] }),
    new UserProfileFilterModel({
      id: 2,
      category: 'Departamento',
      filters: [
        { id: 2, name: 'Departamento 1' },
        { id: 3, name: 'Departamento 2' },
      ],
    }),
    new UserProfileFilterModel({ id: 3, category: 'Filtro Ipsum Lorem', filters: [{ id: 4, name: 'lorem' }] }),
    new UserProfileFilterModel({ id: 4, category: 'Filtro Lorem Ipsum', filters: [{ id: 5, name: 'lorem' }] }),
  ]

  const tabContent = computed(
    () =>
      ({
        stats: {},
        filters: {
          filterModal: filterModal.value,
          filterList: sampleCategoryList,
        },
        management: {
          title: 'ANNOUNCEMENT_OBSERVATION.MANAGEMENT_INFO',
          modalTitle: 'USER.LABEL.MANAGEMENT',
          description: 'USERS.FORM.FILTER2_DESCRIPTION',
          filterModal: filterModal.value,
          filterList: sampleCategoryList,
        },
      })[tabsConfig.current] || {}
  )

  const tabEmitsHandler = computed(
    () =>
      ({
        stats: {},
        filters: {
          open: openFilterModal,
          close: closeFilterModal,
        },
        management: {
          open: openManagementFilterModal,
          close: closeFilterModal,
        },
      })[tabsConfig.current] || {}
  )

  const filterModal = ref({
    open: false,
    options: new CategoryFilterModel(),
  })

  function openFilterModal() {
    const categories = sampleCategoryList.map((category) => ({ id: category.id, name: category.category }))
    const filters = new Array(10).fill({}).map((_, index) => ({ id: index + 1, name: `filter ${index + 1}` }))
    filterModal.value.open = true
    filterModal.value.options.setCategoryList(categories).setFilterList(filters)
  }

  function openManagementFilterModal() {
    filterModal.value.open = true
    filterModal.value.options.setCategoryList([]).setFilterList([])
  }

  function closeFilterModal() {
    filterModal.value.open = false
  }

  const user = inject('user')
  onMounted(async () => {
    await loadProfile(+route.params?.id || 0)
    links.value = [
      new PageTitleModel({ id: 1, title: i18n.t('USER.LABEL_IN_PLURAL'), name: 'users' }),
      new PageTitleModel({ id: 1, title: i18n.t('ANNOUNCEMENT.ERROR_TITLES.USER_PROFILES') }),
    ]

    tabsConfig.tabs = [
      new TabModel({ key: 'stats', title: i18n.t('ANNOUNCEMENT.STATS') }),
      new TabModel({ key: 'filters', title: i18n.t('USER.LABEL.FILTERS') }),
    ]

    if (user.isGranted(USER_PERMISSION_LIST.PROFILE_MANAGEMENT)) {
      tabsConfig.tabs.push(new TabModel({ key: 'management', title: i18n.t('USER.LABEL.MANAGEMENT') }))
    }
  })

  return {
    tabsConfig,
    currentTabView,
    tabContent,
    tabEmitsHandler,
    links,
    isLoading,
    loadingError,
    userData,
    filterModal,
  }
}
