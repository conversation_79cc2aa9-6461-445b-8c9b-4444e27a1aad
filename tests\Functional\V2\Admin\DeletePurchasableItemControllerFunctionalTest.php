<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\PurchasableItemEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\PurchasableItemFixtureTrait;
use App\Tests\Functional\V2\Fixtures\SubscriptionFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class DeletePurchasableItemControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use CourseHelperTrait;
    use SubscriptionFixtureTrait;
    use PurchasableItemFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        $this->truncateEntities([
            'purchasable_item',
            'course',
        ]);
        parent::tearDown();
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testDeletePurchasableItemWithCourseSuccessfully(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Test Course to Delete',
            description: 'Test Course Description'
        );

        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Test Purchasable Item',
            description: 'Test Description',
            priceAmount: 5000,
            priceCurrency: 'EUR',
            resourceType: ResourceType::Course,
            resourceId: new Id($course->getId()),
            isActive: true,
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testDeletePurchasableItemWithSubscriptionSuccessfully(): void
    {
        $subscription = $this->setAndGetSubscriptionInRepository(
            name: 'Premium Subscription to Delete',
            description: 'Premium subscription with full access'
        );

        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Test Subscription Item',
            description: 'Test Subscription Description',
            priceAmount: 9900,
            priceCurrency: 'USD',
            resourceType: ResourceType::Subscription,
            resourceId: $subscription->getId(),
            isActive: true,
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testUnauthorizedAccess(): void
    {
        $purchasableItemId = UuidMother::create();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItemId->value())
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testPurchasableItemNotFound(): void
    {
        $token = $this->loginAndGetToken();
        $nonExistentId = UuidMother::create();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($nonExistentId->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('Purchasable item not found', $responseData['message']);
    }

    public function testInvalidUuidFormat(): void
    {
        $token = $this->loginAndGetToken();
        $invalidUuid = 'invalid-uuid-format';

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($invalidUuid),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals('Validation failed', $responseData['message']);

        $this->assertArrayHasKey('metadata', $responseData);
        $this->assertArrayHasKey('violations', $responseData['metadata']);
        $this->assertArrayHasKey('[uuid]', $responseData['metadata']['violations']);
        $this->assertStringContainsString('This is not a valid UUID', $responseData['metadata']['violations']['[uuid]']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testAccessDeniedForManagersOrCreators(): void
    {
        $user = $this->createAndGetUser(
            firstName: 'Regular',
            lastName: 'User',
            roles: [User::ROLE_USER, User::ROLE_MANAGER, User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user->getId();

        $course = $this->createAndGetCourse();
        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            priceAmount: 1000,
            priceCurrency: 'EUR',
            resourceType: ResourceType::Course,
            resourceId: new Id($course->getId()),
        );

        $userToken = $this->loginAndGetTokenForUser($user);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testDeleteInactivePurchasableItem(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Inactive Course',
            description: 'Course with inactive purchasable item'
        );

        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Inactive Item',
            description: 'This item is inactive',
            priceAmount: 1000,
            priceCurrency: 'EUR',
            resourceType: ResourceType::Course,
            resourceId: new Id($course->getId()),
            isActive: false,
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testDeleteFreePurchasableItem(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Free Course',
            description: 'Free course for everyone'
        );

        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Free Item',
            description: 'This item is free',
            priceAmount: 0,
            priceCurrency: 'EUR',
            resourceType: ResourceType::Course,
            resourceId: new Id($course->getId()),
            isActive: true,
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testDeletedPurchasableItemDoesNotAppearInSubsequentQueries(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Course for Deletion Test',
            description: 'This course will be used to test deletion'
        );

        $purchasableItemToDelete = $this->setAndGetPurchasableItemInRepository(
            name: 'Item to Delete',
            description: 'This item will be deleted',
            priceAmount: 3000,
            priceCurrency: 'EUR',
            resourceType: ResourceType::Course,
            resourceId: new Id($course->getId()),
            isActive: true,
        );

        $course2 = $this->createAndGetCourse(
            name: 'Course to Keep',
            description: 'This course should remain'
        );

        $purchasableItemToKeep = $this->setAndGetPurchasableItemInRepository(
            name: 'Item to Keep',
            description: 'This item should remain',
            priceAmount: 4000,
            priceCurrency: 'EUR',
            resourceType: ResourceType::Course,
            resourceId: new Id($course2->getId()),
            isActive: true,
        );

        $token = $this->loginAndGetToken();

        // First, verify both items exist in the list
        $getResponse = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(page: 1, pageSize: 10),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $getResponse->getStatusCode());
        $getResponseData = json_decode($getResponse->getContent(), true);

        $this->assertArrayHasKey('data', $getResponseData);

        // Find both items in the response
        $items = $getResponseData['data'];
        $itemToDeleteFound = false;
        $itemToKeepFound = false;

        foreach ($items as $item) {
            if ($item['id'] === $purchasableItemToDelete->getId()->value()) {
                $itemToDeleteFound = true;
            }
            if ($item['id'] === $purchasableItemToKeep->getId()->value()) {
                $itemToKeepFound = true;
            }
        }

        $this->assertTrue($itemToDeleteFound, 'Item to delete should be found before deletion');
        $this->assertTrue($itemToKeepFound, 'Item to keep should be found before deletion');

        // Now delete the first item
        $deleteResponse = $this->makeRequest(
            method: 'DELETE',
            uri: PurchasableItemEndpoints::deletePurchasableItemEndpoint($purchasableItemToDelete->getId()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $deleteResponse->getStatusCode());

        // Verify the deleted item no longer appears in subsequent queries
        $getResponseAfterDelete = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(page: 1, pageSize: 10),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $getResponseAfterDelete->getStatusCode());
        $getResponseAfterDeleteData = json_decode($getResponseAfterDelete->getContent(), true);

        $this->assertArrayHasKey('data', $getResponseAfterDeleteData);

        // Verify the deleted item is not in the response
        $itemsAfterDelete = $getResponseAfterDeleteData['data'];
        $itemToDeleteFoundAfterDelete = false;
        $itemToKeepFoundAfterDelete = false;

        foreach ($itemsAfterDelete as $item) {
            if ($item['id'] === $purchasableItemToDelete->getId()->value()) {
                $itemToDeleteFoundAfterDelete = true;
            }
            if ($item['id'] === $purchasableItemToKeep->getId()->value()) {
                $itemToKeepFoundAfterDelete = true;
            }
        }

        $this->assertFalse($itemToDeleteFoundAfterDelete, 'Deleted item should NOT be found after deletion');
        $this->assertTrue($itemToKeepFoundAfterDelete, 'Item to keep should still be found after deletion');

        // Verify the total count has decreased
        $this->assertArrayHasKey('total', $getResponseAfterDeleteData['metadata']);
        $totalAfterDelete = $getResponseAfterDeleteData['metadata']['total'];
        $totalBefore = $getResponseData['metadata']['total'];

        $this->assertEquals($totalBefore - 1, $totalAfterDelete, 'Total count should decrease by 1 after deletion');
    }
}
