<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\VcmsProject;

class VcmsProjectMother
{
    public const string DEFAULT_TITLE = 'Vcms Project 1';
    public const string DEFAULT_VIEW = 'vertical';
    public const false DEFAULT_PROGRESSIVE = false;

    public static function create(
        ?string $title = null,
        ?array $slides = null,
        ?string $view = null,
        ?bool $progressive = null,
    ): VcmsProject {
        $project = new VcmsProject();

        $project->setTitle($title ?? self::DEFAULT_TITLE);
        $project->setView($view ?? self::DEFAULT_VIEW);
        $project->setProgressive($progressive ?? self::DEFAULT_PROGRESSIVE);

        foreach ($slides ?? [] as $slide) {
            $project->addSlide($slide);
        }

        return $project;
    }
}
