<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Service\Announcement;

use App\Entity\Announcement;
use App\Entity\User;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class AnnouncementAuthorizationServiceTest extends TestCase
{
    private function getService(
        ?AnnouncementManagerRepository $announcementManagerRepository = null,
        ?CourseManagerRepository $courseManagerRepository = null,
        ?SettingsService $settingsService = null,
    ): AnnouncementAuthorizationService {
        return new AnnouncementAuthorizationService(
            announcementManagerRepository: $announcementManagerRepository
                ?? $this->createMock(AnnouncementManagerRepository::class),
            courseManagerRepository: $courseManagerRepository ?? $this->createMock(CourseManagerRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class)
        );
    }

    /**
     * @throws InfrastructureException
     * @throws ManagerNotAuthorizedException
     */
    public function testAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    /**
     * @throws InfrastructureException
     * @throws ManagerNotAuthorizedException
     */
    public function testAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));

        $service = $this->getService();
        $this->expectExceptionObject(
            ManagerNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    public static function provideAsManager(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);
        $announcement1 = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1), createdBy: $user1);
        yield 'is the creator' => [
            'user' => $user1,
            'announcement' => $announcement1,
            'sharing' => null,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => null,
        ];

        $announcement2 = AnnouncementMother::create(
            id: 1,
            course: CourseMother::create(id: 1),
            createdBy: UserMother::create(id: 2)
        );
        yield 'not the creator and sharing disabled' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => false,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => ManagerNotAuthorizedException::userNotAuthorized(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];

        yield 'not the creator and sharing enabled with access' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => AnnouncementManagerMother::create(
                userId: new Id(1),
                announcementId: new Id(2),
            ),
            'exception' => null,
        ];

        yield 'not the creator and sharing enabled without access' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => throw new AnnouncementManagerNotFoundException(),
            'exception' => ManagerNotAuthorizedException::userNotAuthorized(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws ManagerNotAuthorizedException
     */
    #[DataProvider('provideAsManager')]
    public function testAsManager(
        User $user,
        Announcement $announcement,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    /**
     * @throws ManagerNotAuthorizedException
     * @throws InfrastructureException
     */
    public function testEnsureUserCanManageAnnouncementAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $service = $this->getService();

        $service->ensureUserCanCreateAnnouncement(
            user: UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            course: CourseMother::create(id: 1),
        );
    }

    /**
     * @throws ManagerNotAuthorizedException
     * @throws CollectionException
     * @throws InfrastructureException
     */
    public function testEnsureUserCanManageAnnouncementAsManagerWithoutSharedCourses(): void
    {
        $courseManagerRepository = $this->createMock(CourseManagerRepository::class);
        $courseManagerRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new CourseManagerCollection([]));

        $service = $this->getService(
            courseManagerRepository: $courseManagerRepository,
        );

        $service->ensureUserCanCreateAnnouncement(
            user: UserMother::create(id: 1, roles: [User::ROLE_MANAGER]),
            course: CourseMother::create(id: 1),
        );
    }

    /**
     * @throws ManagerNotAuthorizedException
     * @throws CollectionException
     * @throws InfrastructureException
     */
    public function testEnsureUserCanManageAnnouncementAsManagerWithSharedCourses(): void
    {
        $course1 = CourseMother::create(id: 1);
        $course2 = CourseMother::create(id: 2);
        $user = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);

        $courseManagerRepository = $this->createMock(CourseManagerRepository::class);
        $courseManagerRepository->expects($this->exactly(2))
            ->method('findBy')
            ->willReturn(new CourseManagerCollection([
                CourseManagerMother::create(
                    userId: new Id(1),
                    courseId: new Id(1),
                ),
            ]));

        $service = $this->getService(
            courseManagerRepository: $courseManagerRepository,
        );

        // Try shared course
        $service->ensureUserCanCreateAnnouncement(
            user: $user,
            course: $course1,
        );

        $this->expectExceptionObject(
            ManagerNotAuthorizedException::userNoPermissionsToCreateAnnouncementForCourse(
                course: $course2,
                email: $user->getEmail(),
            )
        );

        // Try not shared course
        $service->ensureUserCanCreateAnnouncement(
            user: $user,
            course: $course2,
        );
    }
}
