<?php

declare(strict_types=1);

namespace App\Tests\Functional\Command;

use App\Command\DeleteStatusZipFilesTaskOrTaskFilesCommand;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\Export;
use App\Entity\Task;
use App\Entity\ZipFileTask;
use App\Service\Annoucement\Report\AnnouncementReportService;
use App\Service\Task\TaskService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\ZipFileTask\ZipFileTaskService;
use App\Tests\Functional\FunctionalTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\Mailer\MailerInterface;

class DeleteStatusZipFilesTaskOrTaskFilesCommandTest extends FunctionalTestCase
{
    private const DEFAULT_TIMEOUT = 1200;
    private const DEFAULT_LONG_RUNNING_TYPE_TASKS = [];
    private const DEFAULT_SLOT_QUANTITY = 3;

    private EntityManagerInterface $em;
    private TemplatedEmailService $emailService;
    private ?int $testAnnouncementId = null;
    private ?int $testCourseId = null;
    private $zipFileTaskService;
    private $taskService;
    private $templatedEmailService;
    private $logger;
    private $command;

    protected function setUp(): void
    {
        parent::setUp();

        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->zipFileTaskService = $this->createMock(ZipFileTaskService::class);
        $this->taskService = $this->createMock(TaskService::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $settings = $this->getService('App\Service\SettingsService');
        $settings->setSetting('app.export.task.timeout', self::DEFAULT_TIMEOUT);
        $settings->setSetting('app.export.zip_task.timeout', self::DEFAULT_TIMEOUT);

        $this->command = new DeleteStatusZipFilesTaskOrTaskFilesCommand(
            $this->em,
            $this->zipFileTaskService,
            $this->taskService,
            $this->templatedEmailService,
            $this->logger
        );

        $typeCourse = $this->getTypeCourse();

        $course = $this->createAndGetCourse(
            'Test Course', // name
            'courseCode-1', // code
            $typeCourse, // typeCourse
            'Test course description', // description
            'es', // locale
            true, // active
            true, // open
            true, // isNew
            true, // openVisible
            null // CourseCategory
        );

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable('-2 days'),
            finishAt: new \DateTimeImmutable('+2 days'),
            status: Announcement::STATUS_ACTIVE,
            subsidized: false,
            code: 'TEST-ANN-1'
        );

        $this->createAndGetAnnouncementUser(
            $announcement, // announcement
            $this->getDefaultUser() // user
        );

        $this->testCourseId = $course->getId();
        $this->testAnnouncementId = $announcement->getId();
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            Task::class,
            ZipFileTask::class,
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
            Export::class,
        ]);

        parent::tearDown();
    }

    public function testExecuteDeletesExpiredEntities(): void
    {
        $em = $this->getEntityManager();

        $taskWithoutExport = new Task();
        $taskWithoutExport->setTask('export-file');
        $taskWithoutExport->setStatus(Task::TASK_INPROGRESS); // 1
        $taskWithoutExport->setStartedAt(new \DateTimeImmutable('-50 minutes'));
        $em->persist($taskWithoutExport);

        $taskWithExport = new Task();
        $taskWithExport->setTask('export-file');
        $taskWithExport->setStatus(Task::TASK_INPROGRESS);
        $taskWithExport->setStartedAt(new \DateTimeImmutable('-50 minutes'));
        $em->persist($taskWithExport);

        $export = new Export();
        $export->setCreate($this->getDefaultUser());
        $export->setType('stats-export');
        $export->setTask($taskWithExport);
        $export->setFilename('example-file.xlsx');
        $export->setUpdate($this->getDefaultUser());
        $export->setMeta([]);
        $em->persist($export);

        $zipTask = new ZipFileTask();
        $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
        ->setEntityId((string) $this->testAnnouncementId)
        ->setParams(json_decode('{}', true))
        ->setCreatedBy($this->getDefaultUser())
        ->setStartedAt(new \DateTimeImmutable('-50 minutes'))
        ->setTask('announcement-report')
        ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
        ->setOriginalName('announcement-report' . (new \DateTimeImmutable())->format('YmdHis'));
        $em->persist($zipTask);

        $em->flush();

        $application = new Application(self::$kernel);
        $command = $application->find('delete:status:zip-excel');
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);
        $this->assertEquals(0, $exitCode);

        $reloadedtaskWithoutExport = $em->getRepository(Task::class)->find($taskWithExport->getId());
        $reloadedTaskFailed = $em->getRepository(Task::class)->find($taskWithoutExport->getId());
        $reloadedZipFile = $em->getRepository(ZipFileTask::class)->find($zipTask->getId());

        $this->assertSame(Task::TASK_TIMEOUT, $reloadedtaskWithoutExport->getStatus());
        $this->assertSame(Task::TASK_TIMEOUT, $reloadedTaskFailed->getStatus());
        $this->assertSame(ZipFileTask::STATUS_TIMEOUT, $reloadedZipFile->getStatus());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();

        if (empty($messages)) {
            $this->fail('No Timeout messages found in the collection.');
        }

        foreach ($messages as $message) {
            if ($message instanceof TemplatedEmail) {
                $subject = $message->getSubject();
                $this->assertStringContainsString(
                    'ZOMBIE',
                    mb_strtoupper($subject),
                    "El subject del email no contiene 'ZOMBIE' (sin distinguir mayúsculas/minúsculas). Subject actual: $subject"
                );
            }
        }
    }

    public function testExecuteRespectsTimeoutSettings(): void
    {
        $em = $this->getEntityManager();

        // Tarea que está dentro del límite de tiempo
        $taskWithinTimeout = new Task();
        $taskWithinTimeout->setTask('export-file');
        $taskWithinTimeout->setStatus(Task::TASK_INPROGRESS);
        $taskWithinTimeout->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT - 60) . ' seconds'));
        $em->persist($taskWithinTimeout);

        // Tarea que excede el límite de tiempo
        $taskExceedingTimeout = new Task();
        $taskExceedingTimeout->setTask('export-file');
        $taskExceedingTimeout->setStatus(Task::TASK_INPROGRESS);
        $taskExceedingTimeout->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT + 60) . ' seconds'));
        $em->persist($taskExceedingTimeout);

        // ZipTask dentro del límite de tiempo
        $zipTaskWithinTimeout = new ZipFileTask();
        $zipTaskWithinTimeout->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode('{}', true))
            ->setCreatedBy($this->getDefaultUser())
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT - 60) . ' seconds'))
            ->setTask('announcement-report')
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setOriginalName('announcement-report' . (new \DateTimeImmutable())->format('YmdHis'));
        $em->persist($zipTaskWithinTimeout);

        // ZipTask que excede el límite de tiempo
        $zipTaskExceedingTimeout = new ZipFileTask();
        $zipTaskExceedingTimeout->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode('{}', true))
            ->setCreatedBy($this->getDefaultUser())
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT + 60) . ' seconds'))
            ->setTask('announcement-report')
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setOriginalName('announcement-report' . (new \DateTimeImmutable())->format('YmdHis'));
        $em->persist($zipTaskExceedingTimeout);

        $em->flush();

        $application = new Application(self::$kernel);
        $command = $application->find('delete:status:zip-excel');
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);
        $this->assertEquals(0, $exitCode);

        // Verificar que las tareas dentro del límite de tiempo mantienen su estado
        $reloadedTaskWithinTimeout = $em->getRepository(Task::class)->find($taskWithinTimeout->getId());
        $reloadedZipTaskWithinTimeout = $em->getRepository(ZipFileTask::class)->find($zipTaskWithinTimeout->getId());

        $this->assertSame(Task::TASK_INPROGRESS, $reloadedTaskWithinTimeout->getStatus());
        $this->assertSame(ZipFileTask::STATUS_IN_PROGRESS, $reloadedZipTaskWithinTimeout->getStatus());

        // Verificar que las tareas que exceden el límite de tiempo cambian a timeout
        $reloadedTaskExceedingTimeout = $em->getRepository(Task::class)->find($taskExceedingTimeout->getId());
        $reloadedZipTaskExceedingTimeout = $em->getRepository(ZipFileTask::class)->find($zipTaskExceedingTimeout->getId());

        $this->assertSame(Task::TASK_TIMEOUT, $reloadedTaskExceedingTimeout->getStatus());
        $this->assertSame(ZipFileTask::STATUS_TIMEOUT, $reloadedZipTaskExceedingTimeout->getStatus());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();

        if (empty($messages)) {
            $this->fail('No Timeout messages found in the collection.');
        }

        foreach ($messages as $message) {
            if ($message instanceof TemplatedEmail) {
                $subject = $message->getSubject();
                $this->assertStringContainsString(
                    'ZOMBIE',
                    mb_strtoupper($subject),
                    "El subject del email no contiene 'ZOMBIE' (sin distinguir mayúsculas/minúsculas). Subject actual: $subject"
                );
            }
        }
    }
}
