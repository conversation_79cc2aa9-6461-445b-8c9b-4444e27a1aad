<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Filter\FilterCategory;

use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Filter\FilterCategory\FilterCategoryCriteriaTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class FilterCategoryCriteriaTransformerTest extends TestCase
{
    #[DataProvider('paramsProvider')]
    public function testFromArray(
        array $input,
        ?string $expectedName,
        ?Id $expectedParentId
    ): void {
        $criteria = FilterCategoryCriteriaTransformer::fromArray($input);

        $this->assertInstanceOf(FilterCategoryCriteria::class, $criteria);
        $this->assertSame($expectedName, $criteria->getName());
        $this->assertEquals($expectedParentId, $criteria->getParentId());
    }

    public static function paramsProvider(): \Generator
    {
        yield 'name and parent_id' => [
            'input' => ['name' => 'Test Category', 'parent_id' => '123'],
            'expectedName' => 'Test Category',
            'expectedParentId' => new Id(123),
        ];

        yield 'only name' => [
            'input' => ['name' => 'Only Name'],
            'expectedName' => 'Only Name',
            'expectedParentId' => null,
        ];

        yield 'only parent_id' => [
            'input' => ['parent_id' => '456'],
            'expectedName' => null,
            'expectedParentId' => new Id(456),
        ];

        yield 'empty params' => [
            'input' => [],
            'expectedName' => null,
            'expectedParentId' => null,
        ];
    }
}
