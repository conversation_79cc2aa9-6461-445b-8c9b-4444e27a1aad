<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course\Manager;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;

class CourseManagerCollectionTest extends CollectionTestCase
{
    protected function getExpectedType(): string
    {
        return CourseManager::class;
    }

    protected function getItem(): object
    {
        return CourseManagerMother::create(
            userId: new Id(1),
            courseId: new Id(1)
        );
    }

    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new CourseManagerCollection($items);
    }
}
