<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;

class PurchaseCriteria extends CriteriaWithUuid
{
    private ?Id $userId = null;
    private ?PurchaseStatus $status = null;
    private ?Money $minAmount = null;
    private ?Money $maxAmount = null;
    private ?\DateTimeImmutable $startDate = null;
    private ?\DateTimeImmutable $endDate = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->userId
            && null === $this->status
            && null === $this->minAmount
            && null === $this->maxAmount
            && null === $this->startDate
            && null === $this->endDate;
    }

    public function filterByUserId(Id $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getUserId(): ?Id
    {
        return $this->userId;
    }

    public function filterByStatus(PurchaseStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStatus(): ?PurchaseStatus
    {
        return $this->status;
    }

    public function filterByMinAmount(Money $minAmount): self
    {
        $this->minAmount = $minAmount;

        return $this;
    }

    public function getMinAmount(): ?Money
    {
        return $this->minAmount;
    }

    public function filterByMaxAmount(Money $maxAmount): self
    {
        $this->maxAmount = $maxAmount;

        return $this;
    }

    public function getMaxAmount(): ?Money
    {
        return $this->maxAmount;
    }

    public function filterByStartDate(\DateTimeImmutable $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getStartDate(): ?\DateTimeImmutable
    {
        return $this->startDate;
    }

    public function filterByEndDate(\DateTimeImmutable $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeImmutable
    {
        return $this->endDate;
    }
}
