<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Course;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementDidaticGuide;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\ChatChannel;
use App\Entity\ChatServer;
use App\Entity\Content;
use App\Entity\Course;
use App\Entity\MaterialCourse;
use App\Entity\Season;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserToken;
use App\Enum\ChapterContent;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChannelHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\ContentHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendCourseEndpoints;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use App\Tests\Mother\Entity\UserTokenMother;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class CourseFunctionalTest extends FunctionalTestCase
{
    use ChapterHelperTrait;
    use SeasonHelperTrait;
    use ChannelHelperTrait;
    use ChapterTypeHelperTrait;
    use ContentHelperTrait;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUrlCourse(): void
    {
        $course = $this->createAndGetCourse();

        $response = $this->getCourseResponse($course);

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function testValidateTokenCourse(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider courseAccessProvider
     */
    public function testCourseAccess($roles, $courseActive, $expectedStatusCode): void
    {
        $user = $this->getDefaultUser();
        $user->setRoles($roles);
        $this->persistEntity($user);
        $course = $this->createAndGetCourse(
            'Test Course Name',
            'TestCourseCode-1',
            null,
            'Test Course Description',
            'es',
            $courseActive
        );

        $response = $this->getCourseResponse($course);

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function courseAccessProvider(): \Generator
    {
        yield 'User with user role and course deactivated' => [
            'roles' => ['ROLE_USER'],
            'courseActive' => false,
            'expectedStatusCode' => 401,
        ];

        yield 'User with user role and course activated' => [
            'roles' => ['ROLE_USER'],
            'courseActive' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'User with admin role and course deactivated' => [
            'roles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
            'courseActive' => false,
            'expectedStatusCode' => 200,
        ];
    }

    public static function courseAccessWithAnnouncementProvider(): \Generator
    {
        yield 'Announcement set with user' => [
            'withAnnouncementId' => true,
            'withAnnouncementInToken' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Announcement set by token' => [
            'withAnnouncementId' => false,
            'withAnnouncementInToken' => true,
            'expectedStatusCode' => 200,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider courseDataWithAnnouncementProvider
     */
    public function testCourseDataWithAnnouncement(
        $announcementStartAt,
        $announcementFinishAt,
        $isApproved,
        $dataApproved,
        $isReadDidacticGuide,
        $announcementTimezone,
        $expectedStatusCode
    ): void {
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $announcementStartAt,
            finishAt: $announcementFinishAt,
            status: Announcement::STATUS_ACTIVE,
            subsidized: false,
            code: 'DemoAnnouncementCode-1',
            totalHours: 10,
            timezone: $announcementTimezone
        );
        $user = $this->getDefaultUser();
        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);
        $announcementTutor = $this->createAndGetAnnouncementTutor($announcement, $announcementGroup);
        $announcementDidacticGuide = $this->createAndGetAnnouncementDidacticGuide($announcement);
        $announcementUser = $this->createAndGetAnnouncementUser($announcement, $user, $isApproved, $dataApproved, $announcementGroup, $isReadDidacticGuide);
        $this->createAndGetAnnouncementConfiguration($announcement);    // for evaluate 'valued' field.
        $this->createMaterial($announcement);
        $chatServer = $this->createandgetchatserver();
        $this->createAndGetChatChannel($chatServer);

        $response = $this->getCourseResponse($course, $announcement->getId());

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $this->checkCourseArray($responseData);
        $this->assertEquals($dataApproved->format('Y-m-d\TH:i:sP'), $responseData['finished']);
        $this->assertEquals($announcement->getId(), $responseData['announcement']);
        $this->assertEquals('Test material', $responseData['materials'][0]['name']);
        $this->assertEquals($announcementTutor->getId(), $responseData['tutor']['id']);
        $this->assertNotEmpty($responseData['infoAnnouncement']);
        $this->assertStringContainsString($announcementDidacticGuide->getFilename(), $responseData['infoAnnouncement']['guideURL']);
        $this->assertEquals($announcement->getTotalHours(), $responseData['infoAnnouncement']['totalHours']);
        $this->assertEquals($announcementUser->isReadDidacticGuide(), $responseData['isReadDidacticGuide']);
        $this->assertFalse($responseData['hasDigitalSignature']);   // Has unit test already in ApiAnnouncementServiceTest.
        $this->assertEquals(null, $responseData['dateDigitalSignature']);
        $this->assertTrue(filter_var($responseData['canStart'], FILTER_VALIDATE_BOOLEAN));
    }

    public static function courseDataWithAnnouncementProvider(): \Generator
    {
        yield 'Course with announcement' => [
            'announcementStartAt' => new \DateTimeImmutable('today'),
            'announcementFinishAt' => new \DateTimeImmutable('tomorrow'),
            'isApproved' => true,
            'dataApproved' => new \DateTimeImmutable('yesterday'),
            'isReadDidacticGuide' => true,
            'announcementTimezone' => 'America/Mexico_City',
            'expectedStatusCode' => 200,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider courseDataWithoutAnnouncementProvider
     */
    public function testCourseDataWithoutAnnouncement(
        $courseName,
        $userPoints,
        $startAt,
        $finishedAt,
        $expectedStatusCode
    ): void {
        $user = $this->getDefaultUser();
        $this->setUserPoints($user, $userPoints);
        $course = $this->createAndGetCourse($courseName);
        $userCourse = $this->createAndGetUserCourse($user, $course, $startAt, $finishedAt);

        $response = $this->getCourseResponse($course);

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $this->assertEquals($responseData['course']['name'], $courseName);
        $this->assertEquals($responseData['points'], $userPoints);
        $this->assertEquals($responseData['finished'], $finishedAt->format('Y-m-d\TH:i:sP'));
        $this->assertEquals($userCourse->getFinishedAt()->format('Y-m-d\TH:i:sP'), $responseData['finished']);
        $this->assertEquals(null, $responseData['announcement']);
        $this->assertNotEmpty($responseData['infoAnnouncement']);
        $this->assertEquals(null, $responseData['infoAnnouncement']['id']);
        $this->assertEquals(null, $responseData['dateDigitalSignature']);
        $this->assertTrue(filter_var($responseData['canStart'], FILTER_VALIDATE_BOOLEAN));
    }

    public static function courseDataWithoutAnnouncementProvider(): \Generator
    {
        yield 'Course without announcement' => [
            'courseName' => 'Course without announcement',
            'userPoints' => 1000,
            'startAt' => new \DateTimeImmutable('today'),
            'finishedAt' => new \DateTimeImmutable('tomorrow'),
            'expectedStatusCode' => 200,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider courseChapterDataProvider
     */
    public function testCourseChapterData(
        ?int $totalMinutesSpent,
        \DateTimeImmutable $startAt,
        $finishedAt,
        int $progress
    ): void {
        $user = $this->getDefaultUser();

        // For testing course time spent.
        $user->setRoles(['ROLE_USER']);
        $this->getEntityManager()->flush();

        $course = $this->createAndGetCourse();
        $season = $this->createAndGetSeason(course: $course);
        $chapterType = $this->getRepository(ChapterType::class)->findOneBy(['id' => '1']);
        $chapter = $this->createAndGetChapter(course: $course, chapterType: $chapterType, season: $season);
        $userCourse = $this->createAndGetUserCourse(
            $user,
            $course,
            $startAt,
            $finishedAt
        );
        $this->createAndGetUserCourseChapter(
            $userCourse,
            $chapter,
            $startAt,
            $finishedAt,
            $totalMinutesSpent * 60
        );
        $response = $this->getCourseResponse($course);
        $responseData = $this->extractResponseData($response);
        $this->assertEquals($responseData['averageTime']['minutos'], $totalMinutesSpent);

        $this->assertEquals($responseData['progress'], $progress);
    }

    public static function courseChapterDataProvider(): \Generator
    {
        yield 'Course with 1 chapter not finished' => [
            'totalMinutesSpent' => null,
            'startAt' => new \DateTimeImmutable('today'),
            'finishedAt' => null,
            'progress' => 0,
        ];

        yield 'Course with 1 chapter finished' => [
            'totalMinutesSpent' => 60,
            'startAt' => new \DateTimeImmutable('today'),
            'finishedAt' => new \DateTimeImmutable('today'),
            'progress' => 100,
        ];
    }

    public function testCourseSeasons(): void
    {
        $this->getDefaultUser();
        $course = $this->createAndGetCourse(name: 'Course test');
        $season1 = $this->createAndGetSeason(course: $course, name: 'Season 1');
        $season2 = $this->createAndGetSeason(course: $course, name: 'Second 2');

        $response = $this->getCourseResponse($course);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals($response->getStatusCode(), Response::HTTP_OK);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertArrayHasKey('seasons', $data);
        $this->assertCount(0, $data['seasons']);

        $chapterTypeContent = $this->createAndGetChapterTypeRevised(id: ChapterContent::CONTENT_TYPE);
        $chapter1 = $this->createAndGetChapter(
            course: $course,
            season: $season1,
            title: 'Chapter 1 Season 1',
            description: 'Description',
            chapterType: $chapterTypeContent
        );

        $chapterContent = $this->createAndGetContent(
            title: 'Chapter 1 Content',
            chapter: $chapter1,
        );

        $this->createAndGetChapter(
            course: $course,
            season: $season1,
            title: 'Deleted chapter 3',
            active: true,
            chapterType: $chapterTypeContent,
            deletedAt: new \DateTimeImmutable(),
        );

        $this->createAndGetChapter(
            course: $course,
            season: $season2,
            title: 'Disabled chapter 2',
            active: false,
            chapterType: $chapterTypeContent,
        );

        $response = $this->getCourseResponse($course);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals($response->getStatusCode(), Response::HTTP_OK);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertArrayHasKey('seasons', $data);
        $this->assertCount(1, $data['seasons']);
        $this->assertEquals([
            [
                'id' => $season1->getId(),
                'name' => $season1->getName(),
                'type' => Season::TYPE_FREE,
                'chapters' => [
                    [
                        'id' => $chapter1->getId(),
                        'title' => 'Chapter 1 Season 1',
                        'description' => 'Description',
                        'type' => [
                            'id' => ChapterContent::CONTENT_TYPE,
                            'name' => 'Contents',
                        ],
                        'playerUrl' => '/contents/' . $chapterContent->getId(),
                        'status' => false,
                        'image' => null,
                        'imageUrl' => null,
                        'thumbnail' => null,
                        'thumbnails' => [],
                        'order' => 1,
                    ],
                ],
            ],
        ], $data['seasons']);
    }

    public function testCourseValued(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testCourseOpinions(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testMaterials($announcement = null): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testCourseAnnouncementTask(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testCourseAnnouncementSeasons(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testCourseChannels(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testCourseConfigurations(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    public function testCourseItineraries(): void
    {
        $this->markTestIncomplete('Not implemented yet');
    }

    /**
     * @throws ORMException
     * @throws Exception
     */
    #[DataProvider('courseChapterSerializationDataProvider')]
    public function testCourseChapterSerializationData(
        \DateTimeImmutable $announcementStartDate,
        \DateTimeImmutable $announcementFinishDate,
        ?\DateTimeImmutable $chapterStartDate,
        ?\DateTimeImmutable $chapterFinishDate,
        bool $temporalizationConfigurationActive = true,
        bool $temporalizationActiveInAnnouncement = true,
        ?\DateTimeImmutable $expectedChapterStartDate = null,
        ?\DateTimeImmutable $expectedChapterFinishDate = null,
    ): void {
        $user = $this->getDefaultUser();

        $dateTimeFormat = 'en' === $user->getLocale() ? 'Y-m-d H:i:s' : 'd-m-Y H:i:s';

        $announcement = $this->createAndGetAnnouncement(
            startAt: $announcementStartDate,
            finishAt: $announcementFinishDate,
            notifiedAt: new \DateTimeImmutable(),
            timezone: 'UTC',
        );

        $chapter = $this->createAndGetChapter(
            course: $announcement->getCourse(),
        );

        $this->createAndGetContent(
            title: 'Chapter 1 Content',
            chapter: $chapter,
        );

        $announcementGroup = $this->createAndGetAnnouncementGroup(
            announcement: $announcement,
            withSessions: false,
        );

        $announcementConfigurationTypeRepository = $this->getRepository(AnnouncementConfigurationType::class);
        $temporalizationConfigurationType = $announcementConfigurationTypeRepository
            ->find(AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION);
        $defaultActive = $temporalizationConfigurationType->isActive();

        $temporalizationConfigurationType->setActive($temporalizationConfigurationActive);

        if ($temporalizationActiveInAnnouncement) {
            $announcementConfiguration = new AnnouncementConfiguration();
            $announcementConfiguration->setAnnouncement($announcement);
            $announcementConfiguration->setConfiguration($temporalizationConfigurationType);
            $this->getEntityManager()->persist($announcementConfiguration);
        }

        if ($chapterStartDate) {
            $chapterTemporalization = new AnnouncementTemporalization();
            $chapterTemporalization->setChapter($chapter);
            $chapterTemporalization->setAnnouncement($announcement);
            $chapterTemporalization->setStartedAt($chapterStartDate);
            $chapterTemporalization->setFinishedAt($chapterFinishDate);
            $this->getEntityManager()->persist($chapterTemporalization);
        }

        $this->createAndGetAnnouncementUser(
            announcement: $announcement,
            user: $user,
            announcementGroup: $announcementGroup,
        );

        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendCourseEndpoints::courseEndpoint(
                courseId: $announcement->getCourse()->getId(),
                announcementId: $announcement->getId()
            ),
            [],
            [],
            [],
            $this->loginAndGetToken()
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('seasons', $responseData);
        $this->assertIsArray($responseData['seasons']);
        $this->assertCount(1, $responseData['seasons']);
        $this->assertArrayHasKey('chapters', $responseData['seasons'][0]);
        $this->assertIsArray($responseData['seasons'][0]['chapters']);
        $this->assertCount(1, $responseData['seasons'][0]['chapters']);

        if ($expectedChapterStartDate) {
            $this->assertEquals(
                $expectedChapterStartDate->format($dateTimeFormat),
                $responseData['seasons'][0]['chapters'][0]['startAt']
            );
        } else {
            $this->assertArrayNotHasKey(
                'startAt',
                $responseData['seasons'][0]['chapters'][0]
            );
        }

        if ($expectedChapterFinishDate) {
            $this->assertEquals(
                $expectedChapterFinishDate->format($dateTimeFormat),
                $responseData['seasons'][0]['chapters'][0]['finishAt']
            );
        } else {
            $this->assertArrayNotHasKey(
                'finishAt',
                $responseData['seasons'][0]['chapters'][0]
            );
        }

        $temporalizationConfigurationType->setActive($defaultActive);

        $this->getEntityManager()->flush();

        $this->truncateEntities([
            AnnouncementTemporalization::class,
        ]);
    }

    public static function courseChapterSerializationDataProvider(): \Generator
    {
        $announcementStartDate = new \DateTimeImmutable('yesterday');
        $announcementFinishDate = new \DateTimeImmutable('tomorrow');
        $chapterStartDate = (new \DateTimeImmutable('today'))
            ->setTime(10, 0, 0);
        $chapterFinishDate = (new \DateTimeImmutable('today'))
            ->setTime(12, 0, 0);

        yield 'temporalization setting active but not in announcement' => [
            'announcementStartDate' => $announcementStartDate,
            'announcementFinishDate' => $announcementFinishDate,
            'chapterStartDate' => null,
            'chapterFinishDate' => null,
            'temporalizationConfigurationActive' => true,
            'temporalizationActiveInAnnouncement' => false,
            'expectedChapterStartDate' => null,
            'expectedChapterFinishDate' => null,
        ];

        yield 'temporalization setting not active' => [
            'announcementStartDate' => $announcementStartDate,
            'announcementFinishDate' => $announcementFinishDate,
            'chapterStartDate' => null,
            'chapterFinishDate' => null,
            'temporalizationConfigurationActive' => false,
            'temporalizationActiveInAnnouncement' => false,
            'expectedChapterStartDate' => null,
            'expectedChapterFinishDate' => null,
        ];

        yield 'temporalization setting active and in announcement, without chapter start and finish dates' => [
            'announcementStartDate' => $announcementStartDate,
            'announcementFinishDate' => $announcementFinishDate,
            'chapterStartDate' => null,
            'chapterFinishDate' => null,
            'temporalizationConfigurationActive' => true,
            'temporalizationActiveInAnnouncement' => true,
            'expectedChapterStartDate' => $announcementStartDate,
            'expectedChapterFinishDate' => $announcementFinishDate,
        ];

        yield 'temporalization setting active and in announcement, with chapter start and finish dates' => [
            'announcementStartDate' => $announcementStartDate,
            'announcementFinishDate' => $announcementFinishDate,
            'chapterStartDate' => $chapterStartDate,
            'chapterFinishDate' => $chapterFinishDate,
            'temporalizationConfigurationActive' => true,
            'temporalizationActiveInAnnouncement' => true,
            'expectedChapterStartDate' => $chapterStartDate,
            'expectedChapterFinishDate' => $chapterFinishDate,
        ];
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    private function setCourseBaseData(
        &$announcement,
        &$announcementUser,
        &$announcementTutor,
        $announcementStartAt = null,
        $announcementFinishAt = null,
        $isApproved = null,
        $dataApproved = null,
        $isReadDidacticGuide = null,
        $announcementTimezone = null
    ): Course {
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $announcementStartAt,
            finishAt: $announcementFinishAt,
            status: Announcement::STATUS_ACTIVE,
            subsidized: false,
            code: 'DemoAnnouncementCode-1',
            totalHours: 10,
            timezone: $announcementTimezone
        );
        $user = $this->getDefaultUser();
        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);
        $announcementUser = $this->createAndGetAnnouncementUser($announcement, $user, $isApproved, $dataApproved, $announcementGroup, $isReadDidacticGuide);
        $announcementTutor = $this->createAndGetAnnouncementTutor($announcement, $announcementGroup);

        return $course;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getCourseResponse($course, $announcementId = null, $overrideUserToken = false): Response
    {
        $userToken = $overrideUserToken ? $this->loginAndGetCustomToken() : $this->loginAndGetToken();

        return $this->makeFrontendApiRequest(
            'GET',
            FrontendCourseEndpoints::courseEndpoint($course->getId(), $course->isNew(), $announcementId),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws ORMException
     */
    private function createUserTokenWithAnnouncement(
        array $extraParameters
    ) {
        $defaultUser = $this->getDefaultUser();
        $validUntil = new \DateTimeImmutable('+1 day');
        $tokenType = UserToken::TYPE_INSPECTOR_IMPERSONATE_USER;
        $userToken = UserTokenMother::create(
            $defaultUser,
            $tokenType,
            'token',
            false,
            false,
            null,
            $validUntil,
            $extraParameters
        );

        $this->persistEntity($userToken);
    }

    private function loginAndGetCustomToken(): string
    {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'token' => 'token',
        ]);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('token', $responseData);

        return $responseData['token'];
    }

    private function checkCourseArray($responseData, $courseIsNew = true): void
    {
        $this->assertArrayHasKey('course', $responseData);
        $this->assertArrayHasKey('points', $responseData);
        $this->assertArrayHasKey('userCourseId', $responseData);
        $this->assertArrayHasKey('finished', $responseData);
        $this->assertArrayHasKey('valued', $responseData);
        $this->assertArrayHasKey('totalOpinions', $responseData);
        $this->assertArrayHasKey('averageTime', $responseData);
        $this->assertArrayHasKey('progress', $responseData);
        $this->assertArrayHasKey('announcement', $responseData);
        $this->assertArrayHasKey('materials', $responseData);
        $this->assertArrayHasKey('tasks', $responseData);
        $this->assertArrayHasKey('tutor', $responseData);
        $this->assertArrayHasKey('seasons', $responseData);
        $this->assertArrayHasKey('channels', $responseData);
        $this->assertArrayHasKey('infoAnnouncement', $responseData);
        $this->assertArrayHasKey('isReadDidacticGuide', $responseData);
        $this->assertArrayHasKey('configurations', $responseData);
        $this->assertArrayHasKey('hasDigitalSignature', $responseData);
        $this->assertArrayHasKey('dateDigitalSignature', $responseData);
        $this->assertArrayHasKey('canStart', $responseData);
        if (!$courseIsNew) {
            $this->assertArrayHasKey('itineraries', $responseData);
            $this->assertArrayHasKey('messages', $responseData);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function createMaterial($announcement = null)
    {
        $material = new MaterialCourse();
        $material->setAnnouncement($announcement);
        $material->setName('Test material');
        $material->setTypeMaterial('pdf');
        $material->setCreatedAt($createdAt ?? new \DateTimeImmutable());
        $material->setUpdatedAt($updatedAt ?? new \DateTimeImmutable());
        $material->setFilename('test.pdf');
        $material->setIsActive(true);

        $this->getEntityManager()->persist($material);
        $this->getEntityManager()->flush();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function setUserPoints(User $user, $userPoints)
    {
        $user->setPoints($userPoints);
        $this->persistEntity($user);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function persistEntity($entity): void
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            UserCourse::class,
            UserCourseChapter::class,
            Chapter::class,
            Content::class,
            Season::class,
            Announcement::class,
            AnnouncementUser::class,
            MaterialCourse::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            AnnouncementDidaticGuide::class,
            AnnouncementGroupSession::class,
            ChatChannel::class,
            ChatServer::class,
            UserToken::class,
            AnnouncementConfiguration::class,
        ]);

        parent::tearDown();
    }
}
