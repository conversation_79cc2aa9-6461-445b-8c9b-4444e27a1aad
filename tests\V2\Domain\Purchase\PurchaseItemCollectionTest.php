<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class PurchaseItemCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new PurchaseItemCollection($items);
    }

    protected function getExpectedType(): string
    {
        return PurchaseItem::class;
    }

    /**
     * @throws InvalidUuidException
     */
    protected function getItem(): object
    {
        return PurchaseItemMother::create();
    }
}
