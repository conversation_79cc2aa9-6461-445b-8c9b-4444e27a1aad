<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250703172230 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add audit fields (created_by_id, updated_by_id, deleted_by_id, created_at, updated_at, deleted_at) to adivina_imagen table for soft delete support';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME NOT NULL, ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen ADD CONSTRAINT FK_EF1B569EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen ADD CONSTRAINT FK_EF1B569E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen ADD CONSTRAINT FK_EF1B569EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EF1B569EB03A8386 ON adivina_imagen (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EF1B569E896DBBDE ON adivina_imagen (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EF1B569EC76F1F52 ON adivina_imagen (deleted_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen DROP FOREIGN KEY FK_EF1B569EB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen DROP FOREIGN KEY FK_EF1B569E896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen DROP FOREIGN KEY FK_EF1B569EC76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_EF1B569EB03A8386 ON adivina_imagen
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_EF1B569E896DBBDE ON adivina_imagen
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_EF1B569EC76F1F52 ON adivina_imagen
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE adivina_imagen DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP updated_at, DROP deleted_at
        SQL);
    }
}
