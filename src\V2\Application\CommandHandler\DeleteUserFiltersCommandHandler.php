<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\Filter as LegacyFilter;
use App\Repository\FilterRepository as LegacyFilterRepository;
use App\Service\SettingsService;
use App\V2\Application\Command\DeleteUserFiltersCommand;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\DeleteUserFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCriteria;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Domain\User\UserRepository;
use Doctrine\DBAL\ArrayParameterType;

readonly class DeleteUserFiltersCommandHandler
{
    public function __construct(
        private UserFilterRepository $userFilterRepository,
        private ManagerFilterRepository $managerFilterRepository,
        private UserRepository $userRepository,
        private LegacyFilterRepository $legacyFilterRepository,
        private SettingsService $settingsService,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws DeleteUserFiltersCommandHandlerException
     * @throws CollectionException
     * @throws CriteriaException
     * @throws UserNotFoundException
     */
    public function handle(DeleteUserFiltersCommand $command): void
    {
        if (!$this->settingsService->get('app.user.useFilters')) {
            throw UserForbiddenAction::filtersNotEnabled();
        }

        $this->userRepository->findOneBy(
            UserCriteria::createById($command->getUserId()),
        );

        $userFilterIds = array_unique($command->getFilterIds()->all());
        $filters = $this->legacyFilterRepository->createQueryBuilder('f')
            ->where('f.id in (:userFilters)')
            ->setParameter('userFilters', $userFilterIds, ArrayParameterType::STRING)
            ->getQuery()
            ->getResult();
        $existingFilters = array_filter(
            $filters,
            fn (LegacyFilter $filter) => \in_array("{$filter->getId()}", $userFilterIds),
        );
        if (\count($userFilterIds) !== \count($existingFilters)) {
            throw DeleteUserFiltersCommandHandlerException::filterDoesNotExist();
        }

        if (!$command->getRequestedBy()->isAdmin() && $command->getRequestedBy()->isManager()) {
            $managerFilters = $this->managerFilterRepository->findBy(
                ManagerFilterCriteria::createEmpty()
                    ->filterByUserId(new Id($command->getRequestedBy()->getId()))
                    ->filterByFilterIds(new IdCollection($userFilterIds))
            );

            $managedFilters = $managerFilters->filter(
                fn (ManagerFilter $managerFilter) => \in_array($managerFilter->getFilterId(), $userFilterIds)
            );
            if ($managedFilters->count() < \count($userFilterIds)) {
                throw DeleteUserFiltersCommandHandlerException::cannotRemoveUnmanagedFilter();
            }
        }

        $assignedFilters = $this->userFilterRepository->findBy(
            UserFilterCriteria::createEmpty()
                ->filterByFilterIds($command->getFilterIds()),
        );

        if ($assignedFilters->isEmpty()) {
            throw DeleteUserFiltersCommandHandlerException::userFilterNotAssignedToUser();
        }

        $filtersToDelete = $assignedFilters->filter(
            callback: fn (UserFilter $filter) => \in_array($filter->getFilterId(), $userFilterIds)
        );
        if ($filtersToDelete->count() != \count($userFilterIds)) {
            throw DeleteUserFiltersCommandHandlerException::userFilterNotAssignedToUser();
        }

        foreach ($filtersToDelete as $userFilter) {
            $this->userFilterRepository->delete($userFilter);
        }
    }
}
