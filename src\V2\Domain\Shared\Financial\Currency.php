<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Financial;

final class Currency
{
    private CurrencyCode $code;

    public function __construct(CurrencyCode $code)
    {
        $this->code = $code;
    }

    public function __toString(): string
    {
        return $this->code->name;
    }

    public function code(): CurrencyCode
    {
        return $this->code;
    }

    public function equals(Currency $other): bool
    {
        return $this->code === $other->code;
    }

    public static function EUR(): self
    {
        return new self(CurrencyCode::EUR);
    }

    public static function USD(): self
    {
        return new self(CurrencyCode::USD);
    }

    public static function fromCode(CurrencyCode $code): self
    {
        return new self($code);
    }
}
