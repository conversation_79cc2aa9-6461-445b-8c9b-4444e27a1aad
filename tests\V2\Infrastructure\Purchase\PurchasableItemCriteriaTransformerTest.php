<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase;

use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Infrastructure\Purchase\PurchasableItemCriteriaTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PurchasableItemCriteriaTransformerTest extends TestCase
{
    #[DataProvider('criteriaProvider')]
    public function testFromArray(array $data, PurchasableItemCriteria $expectedCriteria): void
    {
        $result = PurchasableItemCriteriaTransformer::fromArray($data);

        $this->assertInstanceOf(PurchasableItemCriteria::class, $result);
        $this->assertEquals($expectedCriteria->getSearch(), $result->getSearch());
        $this->assertEquals($expectedCriteria->getIsActive(), $result->getIsActive());

        $this->assertMoneyEquals($expectedCriteria->getMinPrice(), $result->getMinPrice());
        $this->assertMoneyEquals($expectedCriteria->getMaxPrice(), $result->getMaxPrice());
    }

    public function testFromArrayWithEmptyDataReturnsEmptyCriteria(): void
    {
        $data = [];

        $result = PurchasableItemCriteriaTransformer::fromArray($data);

        $this->assertInstanceOf(PurchasableItemCriteria::class, $result);
        $this->assertTrue($result->isEmpty());
        $this->assertNull($result->getSearch());
        $this->assertNull($result->getMinPrice());
        $this->assertNull($result->getMaxPrice());
        $this->assertNull($result->getIsActive());
    }

    #[DataProvider('returnValueConsistencyProvider')]
    public function testReturnValueConsistency(
        array $inputData,
        callable $assertionCallback
    ): void {
        $result = PurchasableItemCriteriaTransformer::fromArray($inputData);

        $this->assertInstanceOf(PurchasableItemCriteria::class, $result);
        $assertionCallback($this, $result);
    }

    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'returns PurchasableItemCriteria instance' => [
            'inputData' => ['search' => 'test'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertInstanceOf(PurchasableItemCriteria::class, $criteria);
                $test->assertSame('test', $criteria->getSearch());
            },
        ];

        yield 'maintains data integrity' => [
            'inputData' => [
                'search' => 'course name',
                'type' => 'course',
                'price_min' => 1000,
                'price_max' => 5000,
                'price_currency' => 'EUR',
                'is_active' => 'true',
            ],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame('course name', $criteria->getSearch());
                $test->assertSame(1000, $criteria->getMinPrice()->value());
                $test->assertSame(5000, $criteria->getMaxPrice()->value());
                $test->assertTrue($criteria->getIsActive());
            },
        ];
    }

    /**
     * Test boundary conditions and edge cases.
     */
    #[DataProvider('edgeCasesProvider')]
    public function testEdgeCases(
        string $scenario,
        array $inputData,
        callable $assertionCallback
    ): void {
        // Act
        $result = PurchasableItemCriteriaTransformer::fromArray($inputData);

        // Assert
        $assertionCallback($this, $result);
    }

    public static function edgeCasesProvider(): \Generator
    {
        yield 'empty string search' => [
            'scenario' => 'empty string search',
            'inputData' => ['search' => ''],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame('', $criteria->getSearch());
                $test->assertFalse($criteria->isEmpty());
            },
        ];

        yield 'zero price values' => [
            'scenario' => 'zero price values',
            'inputData' => ['price_min' => 0, 'price_max' => 0, 'price_currency' => 'EUR'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame(0, $criteria->getMinPrice()->value());
                $test->assertSame(0, $criteria->getMaxPrice()->value());
            },
        ];

        yield 'large price values' => [
            'scenario' => 'large price values',
            'inputData' => ['price_min' => 999999, 'price_max' => 9999999, 'price_currency' => 'EUR'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame(999999, $criteria->getMinPrice()->value());
                $test->assertSame(9999999, $criteria->getMaxPrice()->value());
            },
        ];

        yield 'is_active false string' => [
            'scenario' => 'is_active false string',
            'inputData' => ['is_active' => 'false'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertFalse($criteria->getIsActive());
            },
        ];

        yield 'is_active non-true string' => [
            'scenario' => 'is_active non-true string',
            'inputData' => ['is_active' => 'yes'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertFalse($criteria->getIsActive());
            },
        ];

        yield 'only min price' => [
            'scenario' => 'only min price',
            'inputData' => ['price_min' => 2500, 'price_currency' => 'EUR'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame(2500, $criteria->getMinPrice()->value());
                $test->assertNull($criteria->getMaxPrice());
            },
        ];

        yield 'only max price' => [
            'scenario' => 'only max price',
            'inputData' => ['price_max' => 7500, 'price_currency' => 'EUR'],
            'assertionCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertNull($criteria->getMinPrice());
                $test->assertSame(7500, $criteria->getMaxPrice()->value());
            },
        ];
    }

    /**
     * Test how the transformer works with MoneyTransformer integration.
     */
    public function testIntegrationWithMoneyTransformer(): void
    {
        $data = [
            'price_min' => 1500,
            'price_max' => 3000,
            'price_currency' => 'EUR',
        ];

        $result = PurchasableItemCriteriaTransformer::fromArray($data);

        $this->assertInstanceOf(Money::class, $result->getMinPrice());
        $this->assertInstanceOf(Money::class, $result->getMaxPrice());
        $this->assertSame(1500, $result->getMinPrice()->value());
        $this->assertSame(3000, $result->getMaxPrice()->value());

        $this->assertTrue($result->getMinPrice()->currency()->equals(Currency::EUR()));
        $this->assertTrue($result->getMaxPrice()->currency()->equals(Currency::EUR()));
    }

    /**
     * Test that the transformer properly handles all supported filter combinations.
     */
    #[DataProvider('filterCombinationsProvider')]
    public function testFilterCombinations(
        array $inputData,
        callable $verificationCallback
    ): void {
        $result = PurchasableItemCriteriaTransformer::fromArray($inputData);

        $verificationCallback($this, $result);
    }

    public static function filterCombinationsProvider(): \Generator
    {
        yield 'all filters combined' => [
            'inputData' => [
                'search' => 'premium course',
                'price_min' => 2000,
                'price_max' => 8000,
                'price_currency' => 'EUR',
                'is_active' => 'true',
            ],
            'verificationCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame('premium course', $criteria->getSearch());
                $test->assertSame(2000, $criteria->getMinPrice()->value());
                $test->assertSame(8000, $criteria->getMaxPrice()->value());
                $test->assertTrue($criteria->getIsActive());
                $test->assertFalse($criteria->isEmpty());
            },
        ];

        yield 'search and price filters' => [
            'inputData' => [
                'search' => 'advanced',
                'price_min' => 1000,
                'price_max' => 5000,
                'price_currency' => 'EUR',
            ],
            'verificationCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame('advanced', $criteria->getSearch());
                $test->assertSame(1000, $criteria->getMinPrice()->value());
                $test->assertSame(5000, $criteria->getMaxPrice()->value());
                $test->assertNull($criteria->getIsActive());
            },
        ];

        yield 'search and active filters' => [
            'inputData' => [
                'search' => 'beginner',
                'is_active' => 'false',
            ],
            'verificationCallback' => function (self $test, PurchasableItemCriteria $criteria) {
                $test->assertSame('beginner', $criteria->getSearch());
                $test->assertFalse($criteria->getIsActive());
                $test->assertNull($criteria->getMinPrice());
                $test->assertNull($criteria->getMaxPrice());
            },
        ];
    }

    /**
     * @throws CollectionException
     */
    #[DataProvider('invalidCurrencyProvider')]
    public function testInvalidCurrencyThrowsException(array $inputData): void
    {
        $this->expectException(InvalidCurrencyCodeException::class);

        PurchasableItemCriteriaTransformer::fromArray($inputData);
    }

    public static function invalidCurrencyProvider(): \Generator
    {
        yield 'invalid currency in min price' => [
            'inputData' => [
                'price_min' => 1000,
                'price_currency' => 'GBP', // Not supported
            ],
        ];

        yield 'invalid currency in max price' => [
            'inputData' => [
                'price_max' => 5000,
                'price_currency' => 'JPY', // Not supported
            ],
        ];
    }

    /**
     * Data provider for comprehensive criteria testing.
     */
    public static function criteriaProvider(): \Generator
    {
        yield 'empty data' => [
            'data' => [],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty(),
        ];

        yield 'with search only' => [
            'data' => ['search' => 'Python Course'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Python Course'),
        ];

        yield 'with type only' => [
            'data' => ['type' => 'course'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByResourceType(ResourceType::Course),
        ];

        yield 'with min price only' => [
            'data' => ['price_min' => 1500, 'price_currency' => 'EUR'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 1500)),
        ];

        yield 'with max price only' => [
            'data' => ['price_max' => 4500, 'price_currency' => 'EUR'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMaxPrice(MoneyMother::create(amount: 4500)),
        ];

        yield 'with is_active true' => [
            'data' => ['is_active' => 'true'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(true),
        ];

        yield 'with is_active false' => [
            'data' => ['is_active' => 'false'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(false),
        ];

        yield 'with price range' => [
            'data' => ['price_min' => 2000, 'price_max' => 6000, 'price_currency' => 'EUR'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 2000))
                ->filterByMaxPrice(MoneyMother::create(amount: 6000)),
        ];

        yield 'with search and prices' => [
            'data' => [
                'search' => 'Advanced JavaScript',
                'price_min' => 3000,
                'price_max' => 8000,
                'price_currency' => 'EUR',
            ],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Advanced JavaScript')
                ->filterByMinPrice(MoneyMother::create(amount: 3000))
                ->filterByMaxPrice(MoneyMother::create(amount: 8000)),
        ];

        yield 'with all filters' => [
            'data' => [
                'search' => 'Full Stack Development',
                'type' => 'course',
                'price_min' => 2500,
                'price_max' => 7500,
                'price_currency' => 'EUR',
                'is_active' => 'true',
            ],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Full Stack Development')
                ->filterByResourceType(ResourceType::Course)
                ->filterByMinPrice(MoneyMother::create(amount: 2500))
                ->filterByMaxPrice(MoneyMother::create(amount: 7500))
                ->filterByIsActive(true),
        ];

        yield 'with special characters in search' => [
            'data' => ['search' => 'C++ & C# Programming: Advanced Techniques'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('C++ & C# Programming: Advanced Techniques'),
        ];

        yield 'with unicode characters in search' => [
            'data' => ['search' => 'Programación en Español: Técnicas Avanzadas'],
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Programación en Español: Técnicas Avanzadas'),
        ];
    }

    /**
     * Helper method to compare Money objects.
     */
    private function assertMoneyEquals(?Money $expected, ?Money $actual): void
    {
        if (null === $expected && null === $actual) {
            return;
        }

        if (null === $expected || null === $actual) {
            $this->fail('One Money object is null while the other is not');
        }

        $this->assertTrue(
            $expected->equals($actual),
            \sprintf(
                'Expected Money(%d %s) but got Money(%d %s)',
                $expected->value(),
                $expected->currency()->code()->name,
                $actual->value(),
                $actual->currency()->code()->name
            )
        );
    }
}
