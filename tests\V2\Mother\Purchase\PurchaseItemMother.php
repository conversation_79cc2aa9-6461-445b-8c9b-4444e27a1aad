<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Purchase;

use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class PurchaseItemMother
{
    /**
     * @throws InvalidUuidException
     */
    public static function create(
        ?Uuid $id = null,
        ?Uuid $purchaseId = null,
        ?Uuid $purchasableItemId = null,
        ?Money $price = null,
    ): PurchaseItem {
        return new PurchaseItem(
            id: $id ?? UuidMother::create(),
            purchaseId: $purchaseId ?? UuidMother::create(),
            purchasableItemId: $purchasableItemId ?? UuidMother::create(),
            price: $price ?? MoneyMother::create(),
        );
    }
}
