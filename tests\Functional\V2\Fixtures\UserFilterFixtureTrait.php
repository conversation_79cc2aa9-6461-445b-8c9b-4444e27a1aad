<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserFilter\UserFilter;

trait UserFilterFixtureTrait
{
    private function setAndGetUserFilterInRepository(
        Id $userId,
        Id $filterId,
    ): UserFilter {
        $userFilter = UserFilterMother::create(
            userId: $userId,
            filterId: $filterId
        );

        $this->client->getContainer()->get('App\V2\Domain\User\UserFilter\UserFilterRepository')
            ->insert($userFilter);

        return $userFilter;
    }
}
