<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\Parejas;
use App\Entity\ParejasImagen;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class MemoryMatch implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;
    private const UPLOADS_PATH = 'app.gameParejasImagen_uploads_path';

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $path = $this->settings->get(self::UPLOADS_PATH) . '/';

        if (!empty($userCourseChapter->getData()['attempts'])) {
            $questionsPairs = $this->em->getRepository(Parejas::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $totalQuestions = \count($questionsPairs) ?? 0;
            $timeTotal = 0;
            $lastDateInQuestions = null;

            if (!$questionsPairs) {
                return [];
            }

            foreach ($questionsPairs as $questionPair) {
                $timeTotal += $questionPair->getTiempo();
            }

            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $memMatchTextsIfNotCards = null;
                    $pairImage = null;
                    if (isset($question['cards'])) {
                        $pairImage = $this->em->getRepository(ParejasImagen::class)->findWithDeleted($question['cards'][1]);
                    } else {
                        $memMatchTextsIfNotCards = $this->em->getRepository(ParejasImagen::class)->getMemMatchTextsByChapterId($userCourseChapter->getChapter()->getId());
                    }
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    $questions[] = [
                        'id' => $pairImage ? $pairImage->getId() : null,
                        'question' => $pairImage ? $pairImage->getTexto() : (!\is_null($memMatchTextsIfNotCards) ? $memMatchTextsIfNotCards['pairTexts'] : null),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswers($pairImage, $question),
                    ];
                    $timeTotalAttempt += $timeInQuestion ?? 0;
                    $attemptForCalculateScore = [
                        'answers' => $attempt,
                        'timeTotal' => $timeTotal,
                        'totalQuestions' => $totalQuestions,
                    ];
                }
                if (!$questions) {
                    continue;
                }

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'attempts' => $attemptForCalculateScore,
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers(?ParejasImagen $pairImagen, $questionUser): array
    {
        if (empty($questionUser)) {
            return [];
        }

        $answers = [];
        $path = $this->settings->get(self::UPLOADS_PATH) . '/';

        $answers[] = [
            // 'id' => $pairImagen->getId(),
            'answer' => !\is_null($pairImagen) ? $pairImagen->getTexto() : null,
            'userAnswer' => !\is_null($pairImagen) ? $pairImagen->getTexto() : null,
            // 'imagen' => $pairImagen->getImagen() ? $path.$pairImagen->getImagen() : null,
            'correct' => $questionUser['correct'] ?? false,
            'incorrect' => $questionUser['incorrect'] ?? false,
        ];

        return $answers;
    }
}
