<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Exception;

class DeleteAnnouncementGroupUserCommandHandlerException extends \Exception
{
    public static function announcementUserNotFound(): self
    {
        return new self('Announcement user not found', 422);
    }

    public static function userNotInGroup(): self
    {
        return new self('User not in group', 422);
    }
}
