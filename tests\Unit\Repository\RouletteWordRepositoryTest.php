<?php

declare(strict_types=1);

namespace App\Tests\Unit\Repository;

use App\Entity\RouletteWord;
use App\Repository\RouletteWordRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\FilterCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

/**
 * Unit test for RouletteWordRepository::findWithDeleted method.
 * Tests soft-delete filter management following established pattern.
 */
class RouletteWordRepositoryTest extends TestCase
{
    private const int VALID_ENTITY_ID = 123;
    private const int INVALID_ENTITY_ID = 999;
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    private MockObject|EntityManagerInterface $entityManager;
    private MockObject|FilterCollection $filterCollection;
    private MockObject|RouletteWordRepository $repository;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->filterCollection = $this->createMock(FilterCollection::class);

        $this->repository = $this->createPartialMock(RouletteWordRepository::class, ['getEntityManager', 'findOneBy']);
        $this->repository->method('getEntityManager')->willReturn($this->entityManager);
    }

    public function testFindWithDeletedReturnsEntityWhenFound(): void
    {
        $rouletteWordId = self::VALID_ENTITY_ID;
        $expectedEntity = new RouletteWord();

        $this->entityManager->expects($this->once())
            ->method(constraint: 'getFilters')
            ->willReturn($this->filterCollection);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'disable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->repository->expects($this->once())
            ->method(constraint: 'findOneBy')
            ->with(criteria: ['id' => $rouletteWordId])
            ->willReturn($expectedEntity);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'enable')
            ->with(self::SOFT_DELETE_FILTER);

        $result = $this->repository->findWithDeleted(id: $rouletteWordId);

        $this->assertSame(expected: $expectedEntity, actual: $result);
    }

    public function testFindWithDeletedReturnsNullWhenNotFound(): void
    {
        $rouletteWordId = self::INVALID_ENTITY_ID;

        $this->entityManager->expects($this->once())
            ->method(constraint: 'getFilters')
            ->willReturn($this->filterCollection);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'disable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->repository->expects($this->once())
            ->method(constraint: 'findOneBy')
            ->with(criteria: ['id' => $rouletteWordId])
            ->willReturn(null);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'enable')
            ->with(self::SOFT_DELETE_FILTER);

        $result = $this->repository->findWithDeleted(id: $rouletteWordId);

        $this->assertNull(actual: $result);
    }

    public function testFindWithDeletedEnsuresFilterIsReenabledOnException(): void
    {
        $rouletteWordId = self::VALID_ENTITY_ID;

        $this->entityManager->expects($this->once())
            ->method(constraint: 'getFilters')
            ->willReturn($this->filterCollection);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'disable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->repository->expects($this->once())
            ->method(constraint: 'findOneBy')
            ->with(criteria: ['id' => $rouletteWordId])
            ->willThrowException(new \Exception('Database error'));

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'enable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->repository->findWithDeleted(id: $rouletteWordId);
    }
}
