<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Entity\User;
use App\Repository\FilterRepository as LegacyFilterRepository;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\FilterMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Application\Command\PostUserFiltersCommand;
use App\V2\Application\CommandHandler\PostUserFiltersCommandHandler;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\PostUserFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserFilterRepositoryException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Domain\User\UserRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PostUserFiltersCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?UserFilterRepository $userFilterRepository = null,
        ?ManagerFilterRepository $managerFilterRepository = null,
        ?UserRepository $userRepository = null,
        ?LegacyFilterRepository $legacyFilterRepository = null,
        ?SettingsService $settingsService = null,
    ): PostUserFiltersCommandHandler {
        return new PostUserFiltersCommandHandler(
            userFilterRepository: $userFilterRepository ?? $this->createMock(UserFilterRepository::class),
            managerFilterRepository: $managerFilterRepository ?? $this->createMock(ManagerFilterRepository::class),
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            legacyFilterRepository: $legacyFilterRepository ?? $this->createMock(LegacyFilterRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class),
        );
    }

    public static function provideHandleAsManager(): \Generator
    {
        yield '3 filters' => [
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'filterIds' => new IdCollection([new Id(1), new Id(2), new Id(3)]),
            'exception' => null,
        ];

        yield '4 filters set 2' => [
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'filterIds' => new IdCollection([new Id(2), new Id(3)]),
            'exception' => null,
        ];

        yield '4 filters set 3 one not exists' => [
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'filterIds' => new IdCollection([new Id(2), new Id(3), new Id(5)]),
            'exception' => PostUserFiltersCommandHandlerException::filterDoesNotExist(),
        ];

        yield '4 filters set 3 one not managed' => [
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'filterIds' => new IdCollection([new Id(2), new Id(3), new Id(1)]),
            'exception' => PostUserFiltersCommandHandlerException::cannotAssignUnmanagedFilter(),
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws UserFilterRepositoryException
     * @throws PostUserFiltersCommandHandlerException
     * @throws CollectionException
     * @throws CriteriaException
     * @throws UserNotFoundException
     */
    #[DataProvider('provideHandleAsManager')]
    public function testHandleAsManager(
        array $filters,
        ManagerFilterCollection $managerFilterCollection,
        IdCollection $filterIds,
        $exception = null,
    ): void {
        $user = UserMother::create(id: 1, roles: [User::ROLE_MANAGER]);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->method('findOneBy')
            ->willReturn($user);

        $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(Query::class);

        $query->method('getResult')
            ->willReturn($filters);
        $queryBuilder->method('where')
            ->willReturn($queryBuilder);
        $queryBuilder->method('setParameter')
            ->willReturn($queryBuilder);
        $queryBuilder->method('getQuery')
            ->willReturn($query);
        $legacyFilterRepository->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        $userFilterRepository
            ->method('findBy')
            ->willReturn(
                new UserFilterCollection([])
            );

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $managerFilterRepository
            ->method('findBy')
            ->willReturn(
                $managerFilterCollection
            );

        $userFilterRepository
            ->method('insert')
            ->willReturnCallback(function (UserFilter $userFilter) use ($user, $filterIds) {
                $this->assertEquals($user->getId(), $userFilter->getUserId()->value());
                $this->assertTrue($filterIds->contains($userFilter->getFilterId()));
            });

        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturn(true);

        $handler = $this->getHandler(
            userFilterRepository: $userFilterRepository,
            managerFilterRepository: $managerFilterRepository,
            userRepository: $userRepository,
            legacyFilterRepository: $legacyFilterRepository,
            settingsService: $settingsService,
        );

        if (null !== $exception) {
            $this->expectExceptionObject($exception);
        }

        $handler->handle(
            new PostUserFiltersCommand(
                userId: new Id(1),
                filterIds: $filterIds,
                requestedBy: $user,
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws PostUserFiltersCommandHandlerException
     * @throws UserFilterRepositoryException
     * @throws UserNotFoundException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testHandleAsAdmin(): void
    {
        $user = UserMother::create(id: 1, roles: [User::ROLE_ADMIN]);
        $filterIds = new IdCollection([new Id(1), new Id(2), new Id(3)]);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->method('findOneBy')
            ->willReturn($user);

        $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(Query::class);

        $query->method('getResult')
            ->willReturn([
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
            ]);
        $queryBuilder->method('where')
            ->willReturn($queryBuilder);
        $queryBuilder->method('setParameter')
            ->willReturn($queryBuilder);
        $queryBuilder->method('getQuery')
            ->willReturn($query);
        $legacyFilterRepository->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        $userFilterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(
                new UserFilterCollection([])
            );

        $userFilterRepository->expects($this->exactly(3))
            ->method('insert')
            ->willReturnCallback(function (UserFilter $userFilter) use ($user, $filterIds) {
                $this->assertEquals($user->getId(), $userFilter->getUserId()->value());
                $this->assertTrue($filterIds->contains($userFilter->getFilterId()));
            });

        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturn(true);

        $handler = $this->getHandler(
            userFilterRepository: $userFilterRepository,
            userRepository: $userRepository,
            legacyFilterRepository: $legacyFilterRepository,
            settingsService: $settingsService,
        );

        $handler->handle(
            new PostUserFiltersCommand(
                userId: new Id(1),
                filterIds: $filterIds,
                requestedBy: $user,
            )
        );
    }

    /**
     * @throws UserForbiddenAction
     * @throws InfrastructureException
     * @throws Exception
     * @throws PostUserFiltersCommandHandlerException
     * @throws UserFilterRepositoryException
     * @throws CriteriaException
     * @throws UserNotFoundException
     * @throws CollectionException
     */
    #[DataProvider('provideExceptions')]
    public function testExceptions(
        User $requestedBy,
        callable $settingServiceCallback,
        ?callable $userFindOneBy,
        ?callable $legacyFilterGetResult,
        ?callable $managerFilterFindBy,
        ?callable $userFilterFindBy,
        $expectedException
    ): void {
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturnCallback($settingServiceCallback);

        $filterIds = new IdCollection([new Id(1)]);

        $userRepository = $this->createMock(UserRepository::class);
        if (null !== $userFindOneBy) {
            $userRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($userFindOneBy);
        }

        $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
        if (null !== $legacyFilterGetResult) {
            $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
            $queryBuilder = $this->createMock(QueryBuilder::class);
            $query = $this->createMock(Query::class);

            $query->expects($this->once())->method('getResult')
                ->willReturnCallback($legacyFilterGetResult);
            $queryBuilder->expects($this->once())->method('where')
                ->willReturn($queryBuilder);
            $queryBuilder->expects($this->once())->method('setParameter')
                ->willReturn($queryBuilder);
            $queryBuilder->expects($this->once())->method('getQuery')
                ->willReturn($query);
            $legacyFilterRepository->expects($this->once())
                ->method('createQueryBuilder')
                ->willReturn($queryBuilder);
        }

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        if (null !== $userFilterFindBy) {
            $userFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($userFilterFindBy);
        }

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        if (null !== $managerFilterFindBy) {
            $managerFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($managerFilterFindBy);
        }

        $handler = $this->getHandler(
            userFilterRepository: $userFilterRepository,
            managerFilterRepository: $managerFilterRepository,
            userRepository: $userRepository,
            legacyFilterRepository: $legacyFilterRepository,
            settingsService: $settingsService,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle(
            new PostUserFiltersCommand(
                userId: new Id(1),
                filterIds: $filterIds,
                requestedBy: $requestedBy,
            )
        );
    }

    public static function provideExceptions(): \Generator
    {
        yield 'filters disabled' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => false,
            'userFindOneBy' => null,
            'legacyFilterGetResult' => null,
            'managerFilterFindBy' => null,
            'userFilterFindBy' => null,
            'expectedException' => UserForbiddenAction::filtersNotEnabled(),
        ];
        yield 'user not found' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => throw new UserNotFoundException(),
            'legacyFilterGetResult' => null,
            'managerFilterFindBy' => null,
            'userFilterFindBy' => null,
            'expectedException' => new UserNotFoundException(),
        ];

        yield 'filter does not exists' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'legacyFilterGetResult' => fn () => [],
            'managerFilterFindBy' => null,
            'userFilterFindBy' => null,
            'expectedException' => PostUserFiltersCommandHandlerException::filterDoesNotExist(),
        ];

        yield 'manager unmanaged filter' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_MANAGER]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 2),
            'legacyFilterGetResult' => fn () => [FilterMother::create(id: 1)],
            'managerFilterFindBy' => fn () => new ManagerFilterCollection([]),
            'userFilterFindBy' => null,
            'expectedException' => PostUserFiltersCommandHandlerException::cannotAssignUnmanagedFilter(),
        ];

        yield 'filter already assigned' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 2),
            'legacyFilterGetResult' => fn () => [FilterMother::create(id: 1)],
            'managerFilterFindBy' => null,
            'userFilterFindBy' => fn () => new UserFilterCollection([
                UserFilterMother::create(
                    userId: new Id(2),
                    filterId: new Id(1)
                ),
            ]),
            'expectedException' => PostUserFiltersCommandHandlerException::userHasFilterAssigned(),
        ];
    }
}
