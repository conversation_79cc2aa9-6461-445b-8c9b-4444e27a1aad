<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\Export;
use App\Entity\Task;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetAnnouncementParticipantReportsFunctionalTest extends FunctionalTestCase
{
    private ?Course $course = null;
    private ?Announcement $announcement = null;
    private ?User $user = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->course = $this->createAndGetCourse();
        $this->announcement = $this->createAndGetAnnouncement(
            course: $this->course,
            startAt: new \DateTimeImmutable('-1 day'),
            finishAt: new \DateTimeImmutable('+1 day')
        );
        $this->em = $this->getEntityManager();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('announcementParticipantsReportsProvider')]
    public function testGetAnnouncementParticipantReports(string $taskName, ?string $filename)
    {
        $task = $this->createAndGetTask(
            taskName: $taskName,
            params: [
                'announcementId' => $this->announcement->getId(),
            ],
            createdBy: $this->user,
        );
        $export = $this->createAndGetExport(
            filename: $filename,
            createdBy: $this->user,
            task: $task,
        );

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::announcementParticipantReportsEndpoint(),
            bearerToken: $this->loginAndGetToken()
        );

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        if (!empty($responseData)) {
            $this->assertEquals($filename, $responseData[0]['filename']);
        }
    }

    public static function announcementParticipantsReportsProvider(): \Generator
    {
        yield 'participants report' => [
            'taskName' => 'export-stats-file',
            'filename' => 'announcement-participants-export',
        ];

        yield 'no participants report' => [
            'taskName' => 'course-catalog-export',
            'filename' => null,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('announcementParticipantsReportsPaginatedProvider')]
    public function testGetAnnouncementParticipantReportsPaginated(string $taskName, ?string $filename, int $page, int $pageSize)
    {
        $task = $this->createAndGetTask(
            taskName: $taskName,
            params: [
                'announcementId' => $this->announcement->getId(),
            ],
            createdBy: $this->user,
        );
        $export = $this->createAndGetExport(
            filename: $filename,
            createdBy: $this->user,
            task: $task,
        );

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::announcementParticipantReportsEndpoint(),
            queryParams: [
                'page' => $page,
                'pageSize' => $pageSize,
            ],
            bearerToken: $this->loginAndGetToken()
        );

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        if ('export-stats-file' === $taskName) {
            $this->assertCount(1, $responseData['items']);
            $this->assertEquals(1, $responseData['totalItems']);
            $this->assertEquals($filename, $responseData['items'][0]['filename']);
        } else {
            $this->assertCount(0, $responseData['items']);
            $this->assertEquals(0, $responseData['totalItems']);
        }
    }

    public static function announcementParticipantsReportsPaginatedProvider(): \Generator
    {
        yield 'participants report paginated' => [
            'taskName' => 'export-stats-file',
            'filename' => 'announcement-participants-export',
            'page' => 1,
            'pageSize' => 10,
        ];

        yield 'no participants report' => [
            'taskName' => 'course-catalog-export',
            'filename' => null,
            'page' => 1,
            'pageSize' => 10,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Announcement::class,
            Course::class,
            Export::class,
            Task::class,
        ]);

        parent::tearDown();
    }
}
