<?php

namespace App\Tests\Functional\Listerner;

use App\Entity\Chapter;
use App\Entity\Content;
use App\Entity\Course;
use App\Entity\UserCourse;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use App\V2\Domain\Shared\Filter\FilterCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

class LoggedUserRequestAttributeNotInterfereFunctionalTest extends FunctionalTestCase
{
    private $userIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testLoggedUserRequestAttributeNotInterfere(): void
    {
        $defaultUser = $this->getDefaultUser();

        $filter1 = $this->createAndGetFilter(
            name: 'Filter 1',
        );
        $filter2 = $this->createAndGetFilter(
            name: 'Filter 2',
        );

        $user1 = $this->createAndGetUser(
            roles: ['ROLE_USER'],
            email: '<EMAIL>',
            userFilters: new FilterCollection([
                $filter1,
                $filter2,
            ]),
        );
        $this->userIds[] = $user1->getId();

        $user2 = $this->createAndGetUser(
            roles: ['ROLE_USER'],
            email: '<EMAIL>',
            userFilters: new FilterCollection([
                $filter1,
            ]),
        );
        $this->userIds[] = $user2->getId();

        $course1 = $this->createAndGetCourse(
            courseFilters: new FilterCollection([$filter1])
        );
        $chapter1 = $this->createAndGetChapter(
            course: $course1,
            title: 'Chapter 1 Season 1',
            description: 'Description',
        );
        $this->createAndGetContent(
            title: 'Chapter 1 Content',
            chapter: $chapter1,
        );

        $course2 = $this->createAndGetCourse(
            courseFilters: new FilterCollection([$filter2])
        );
        $chapter2 = $this->createAndGetChapter(
            course: $course2,
            title: 'Chapter 2 Season 1',
            description: 'Description',
        );
        $this->createAndGetContent(
            title: 'Chapter 1 Content',
            chapter: $chapter2,
        );

        $this->createAndGetUserCourse(
            user: $user1,
            course: $course1,
        );
        $this->createAndGetUserCourse(
            user: $user1,
            course: $course2,
        );
        $this->createAndGetUserCourse(
            user: $user2,
            course: $course1,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::userCoursesEndpoint($user1->getId()),
            bearerToken: $this->loginAndGetTokenForUser($defaultUser),
        );

        $responseDecoded = $this->extractResponseData($response);
        $this->assertEquals(2, $responseDecoded['courses'][1]['total']);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::userCoursesEndpoint($user2->getId()),
            bearerToken: $this->loginAndGetTokenForUser($defaultUser),
        );

        $responseDecoded = $this->extractResponseData($response);
        $this->assertEquals(1, $responseDecoded['courses'][1]['total']);

        $this->truncateEntities([
            UserCourse::class,
            'course_filter',
            'user_filter',
            Chapter::class,
            Content::class,
            Course::class,
        ]);

        $this->hardDeleteUsersByIds([$user1->getId(), $user2->getId()]);
    }

    public function tearDown(): void
    {
        $this->truncateEntities([
            UserCourse::class,
            'course_filter',
            'user_filter',
            Chapter::class,
            Content::class,
            Course::class,
        ]);

        if (!empty($this->userIds)) {
            $this->hardDeleteUsersByIds($this->userIds);
        }

        parent::tearDown();
    }
}
