<template>
  <div class="TableContainer">
    <p
      v-show="showTitle && (pagination?.totalPages > 1 || pagination.totalItems > pagination.pageSize)"
      class="paginationInfo"
    >
      {{
        pagination.totalItems
          ? $t('COMMON.PAGINATION_INFO', [total, pagination.totalItems])
          : $t('COMMON.PAGINATION_INFO_BY_PAGE', [pagination.currentPage, pagination.totalPages])
      }}
    </p>
    <div class="tableContainer">
      <slot />
    </div>
    <BasePagination
      v-if="pagination?.totalPages > 1"
      :disabled="disabled"
      :pagination="pagination"
      @page-change="(page) => emit('pageChange', page)"
    />
  </div>
</template>

<script setup>
import { Pagination } from '@/contexts/shared/models/pagination.model.js'
import BasePagination from '@/contexts/shared/components/BasePagination.vue'

const emit = defineEmits(['pageChange'])
const props = defineProps({
  pagination: { type: [Pagination, Object], default: () => ({}) },
  total: { type: Number, default: 0 },
  disabled: { type: Boolean, default: false },
  showTitle: { type: Boolean, default: true },
})
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.TableContainer {
  .paginationInfo {
    text-align: right;
    font-weight: bold;
  }

  .tableContainer {
    width: 100%;
    max-width: calc(100svw - 10rem);
    overflow-x: scroll;
    overflow-y: hidden;
    margin-top: -10rem;
    padding: 10rem 0 0;

    :deep(table) {
      width: 100%;
      border-collapse: collapse;

      td,
      th {
        border: solid var(--color-neutral-mid);
        border-width: 1px 0;
      }

      th {
        padding: 1rem 0;
      }
    }

    @media #{breakpoint.$breakpoint-sm} {
      max-width: unset;
    }
  }
}
</style>
