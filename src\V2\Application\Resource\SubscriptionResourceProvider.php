<?php

declare(strict_types=1);

namespace App\V2\Application\Resource;

use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Subscription\Exception\SubscriptionNotFoundException;
use App\V2\Domain\Subscription\Exception\SubscriptionRepositoryException;
use App\V2\Domain\Subscription\Subscription;
use App\V2\Domain\Subscription\SubscriptionCriteria;
use App\V2\Domain\Subscription\SubscriptionRepository;

readonly class SubscriptionResourceProvider implements ResourceProvider
{
    public function __construct(
        private SubscriptionRepository $subscriptionRepository,
    ) {
    }

    public function supports(ResourceType $type): bool
    {
        return ResourceType::Subscription === $type;
    }

    /**
     * @throws SubscriptionRepositoryException
     * @throws ResourceNotFoundException
     * @throws CriteriaException
     */
    public function getEntity(Resource $resource): Subscription
    {
        try {
            $subscription = $this->subscriptionRepository->findOneBy(
                SubscriptionCriteria::createById($resource->getId())
            );
        } catch (SubscriptionNotFoundException) {
            throw new ResourceNotFoundException($resource);
        }

        return $subscription;
    }
}
