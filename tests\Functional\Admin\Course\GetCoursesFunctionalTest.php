<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetCoursesFunctionalTest extends FunctionalTestCase
{
    use CourseCreatorFixtureTrait;

    private ?Course $course = null;
    private ?Announcement $announcement = null;
    private ?User $user = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->course1 = $this->createAndGetCourse(
            name: 'Test Course 1',
        );
        $this->course2 = $this->createAndGetCourse(
            name: 'Test Course 2',
        );
        $this->em = $this->getEntityManager();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('courseUsersCreatedDataProvider')]
    public function testCourseCreatorGetCreatedCourse(array $userRoles, int $expectedCourses): void
    {
        $this->user->setRoles($userRoles);
        $this->course1->setCreatedBy($this->user);
        $this->getEntityManager()->flush();

        $response = $this->getApiRequestResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals($expectedCourses, $responseData['totalCourses']);

        $this->user->setRoles(['ROLE_USER', 'ROLE_ADMIN']);
        $this->getEntityManager()->flush();
    }

    public static function courseUsersCreatedDataProvider(): \Generator
    {
        yield 'User as creator' => [
            'userRoles' => ['ROLE_CREATOR'],
            'expectedCourses' => 1,
        ];

        yield 'User as admin & creator' => [
            'userRoles' => ['ROLE_ADMIN', 'ROLE_CREATOR'],
            'expectedCourses' => 2,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('courseCreatedUsersAssignedDataProvider')]
    public function testCourseCreatorGetAssignedCourse(array $userRoles, int $expectedCourses): void
    {
        $this->user->setRoles($userRoles);
        $this->course1->setCreatedBy($this->user);
        $course3 = $this->createAndGetCourse(
            name: 'Created Course for assign to creator',
            createdBy: $this->user,
        );
        $this->setAndGetCourseCreatorInRepository(
            userId: new Id($this->user->getId()),
            courseId: new Id($course3->getId()),
        );
        $this->getEntityManager()->flush();

        $response = $this->getApiRequestResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals($expectedCourses, $responseData['totalCourses']);

        $this->user->setRoles(['ROLE_USER', 'ROLE_ADMIN']);
        $this->getEntityManager()->flush();
    }

    public static function courseCreatedUsersAssignedDataProvider(): \Generator
    {
        yield 'User as creator. 2 courses previously created with 1 set as createdBy.' => [
            'userRoles' => ['ROLE_CREATOR'],
            'expectedCourses' => 2,
        ];

        yield 'User as admin & creator. 3 courses: 2 previously created with 1 set as createdBy, 1 new and assigned' => [
            'userRoles' => ['ROLE_ADMIN', 'ROLE_CREATOR'],
            'expectedCourses' => 3,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('courseManagerAndCreatorRolesDataProvider')]
    public function testCourseManagerAndCreatorGetCourses(
        array $userRoles,
        ?string $creatorCreateCourse,
        ?string $creatorAssignedCreatedCourse,
        ?string $assignManagerCourse,
        int $expectedCourses
    ): void {
        $this->user->setRoles($userRoles);

        if ($creatorCreateCourse) {
            $courseCreatedByCreator = $this->createAndGetCourse(
                name: $creatorCreateCourse,
                createdBy: $this->user,
            );
        }

        if ($creatorAssignedCreatedCourse) {
            $courseCreatedAndAssigned = $this->createAndGetCourse(
                name: $creatorAssignedCreatedCourse,
            );
            $this->setAndGetCourseCreatorInRepository(
                userId: new Id($this->user->getId()),
                courseId: new Id($courseCreatedAndAssigned->getId()),
            );
        }

        if ($assignManagerCourse) {
            $courseManagerAssigned = $this->createAndGetCourse(
                name: $assignManagerCourse,
            );
            $courseManagerAssigned->setManagers([$this->user]);
        }

        $this->getEntityManager()->flush();

        $response = $this->getApiRequestResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals($expectedCourses, $responseData['totalCourses']);

        $this->user->setRoles(['ROLE_USER', 'ROLE_ADMIN']);
        $this->getEntityManager()->flush();
    }

    public static function courseManagerAndCreatorRolesDataProvider(): \Generator
    {
        yield 'User as creator && manager && admin. 2 previously courses created. With no new course creation, no course creation assign and no course manager assign. ' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER', 'ROLE_ADMIN'],
            'creatorCreateCourse' => null,
            'creatorAssignedCreatedCourse' => null,
            'assignManagerCourse' => null,
            'expectedCourses' => 2,
        ];

        yield 'User as creator && manager. 2 previously courses created. With no new course creation, no course creation assign and 1 new course assign.' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER'],
            'creatorCreateCourse' => null,
            'creatorAssignedCreatedCourse' => null,
            'assignManagerCourse' => 'New Assigned Course',
            'expectedCourses' => 1,
        ];

        yield 'User as creator && manager. 2 previously courses created. With 1 new course creation, no course creation assign and no course assign.' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER'],
            'creatorCreateCourse' => 'New Created Course',
            'creatorAssignedCreatedCourse' => null,
            'assignManagerCourse' => null,
            'expectedCourses' => 3,
        ];

        yield 'User as creator && manager. 2 previously courses created. With no new course creation, 1 new course creation assign and no course assign.' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER'],
            'creatorCreateCourse' => null,
            'creatorAssignedCreatedCourse' => 'New Created Course Assigned',
            'assignManagerCourse' => null,
            'expectedCourses' => 3,
        ];

        yield 'User as creator && manager. 2 previously courses created. With 1 new course creation, 1 new course creation assign and no course assign.' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER'],
            'creatorCreateCourse' => 'New Created Course',
            'creatorAssignedCreatedCourse' => 'New Created Course Assigned',
            'assignManagerCourse' => null,
            'expectedCourses' => 4,
        ];

        yield 'User as creator && manager. 2 previously courses created. With 1 new course creation, 1 new course creation assign and 1 new course assign.' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER'],
            'creatorCreateCourse' => 'New Created Course',
            'creatorAssignedCreatedCourse' => 'New Created Course Assigned',
            'assignManagerCourse' => 'New Assigned Course',
            'expectedCourses' => 3,
        ];

        yield 'User as creator && manager && admin. 2 previously courses created. With 1 new course creation, 1 new course creation assign and 1 new course assign.' => [
            'userRoles' => ['ROLE_CREATOR', 'ROLE_MANAGER', 'ROLE_ADMIN'],
            'creatorCreateCourse' => 'New Created Course',
            'creatorAssignedCreatedCourse' => 'New Created Course Assigned',
            'assignManagerCourse' => 'New Assigned Course',
            'expectedCourses' => 5,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCourseManagerGetAllCourses(): void
    {
        $this->user->setRoles(['ROLE_MANAGER']);
        $this->getEntityManager()->flush();

        $response = $this->getApiRequestResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals(2, $responseData['totalCourses']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('courseUsersAssignedDataProvider')]
    public function testCourseManagerGetAssignedCourse(array $userRoles, int $expectedCourses): void
    {
        $this->user->setRoles($userRoles);
        $this->course1->setManagers([$this->user]);
        $this->getEntityManager()->flush();

        $response = $this->getApiRequestResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals($expectedCourses, $responseData['totalCourses']);

        $this->user->setRoles(['ROLE_USER', 'ROLE_ADMIN']);
        $this->getEntityManager()->flush();
    }

    public static function courseUsersAssignedDataProvider(): \Generator
    {
        yield 'User as manager' => [
            'userRoles' => ['ROLE_MANAGER'],
            'expectedCourses' => 1,
        ];

        yield 'User as admin & manager' => [
            'userRoles' => ['ROLE_ADMIN', 'ROLE_MANAGER'],
            'expectedCourses' => 2,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('courseCreatedUsersDataProvider')]
    public function testCourseManagerGetCreatedCourse(array $userRoles, int $expectedCourses): void
    {
        $this->user->setRoles($userRoles);
        $this->course1->setManagers([$this->user]);
        $course3 = $this->createAndGetCourse(
            name: 'Created Course',
            createdBy: $this->user,
        );
        $this->getEntityManager()->flush();

        $response = $this->getApiRequestResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals($expectedCourses, $responseData['totalCourses']);

        $this->user->setRoles(['ROLE_USER', 'ROLE_ADMIN']);
        $this->getEntityManager()->flush();
    }

    public static function courseCreatedUsersDataProvider(): \Generator
    {
        yield 'User as manager & creator' => [
            'userRoles' => ['ROLE_MANAGER', 'ROLE_CREATOR'],
            'expectedCourses' => 2,
        ];

        yield 'User as admin & manager' => [
            'userRoles' => ['ROLE_ADMIN', 'ROLE_MANAGER', 'ROLE_CREATOR'],
            'expectedCourses' => 3,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCoursesListStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getApiRequestResponse());

        $this->assertArrayHasKey('courses', $responseData);
        $this->assertArrayHasKey('totalCourses', $responseData);
        $this->assertArrayHasKey('typeCourses', $responseData);
        $this->assertArrayHasKey('courseCategories', $responseData);
        $this->assertArrayHasKey('creatorsCourses', $responseData);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleCourseStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getApiRequestResponse());

        $this->assertNotEmpty($responseData['courses']);
        $course = $responseData['courses'][0];

        $expectedKeys = [
            'id',
            'name',
            'typeCourse',
            'category',
            'active',
            'open',
            'totalChapters',
            'thumbnailUrl',
            'locale',
            'completed',
            'languages',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $course);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleTypeCourseStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getApiRequestResponse());

        $this->assertNotEmpty($responseData['typeCourses']);
        $typeCourse = $responseData['typeCourses'][0];

        $expectedKeys = [
            'id',
            'name',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $typeCourse);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleValidationTotalCourses(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getApiRequestResponse());

        $this->assertNotEmpty($responseData['totalCourses']);
        $totalCourses = $responseData['totalCourses'];
        $this->assertIsInt($totalCourses, 'The data entered must be an integer.');
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleCourseCategoriesStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getApiRequestResponse());

        $this->assertNotEmpty($responseData['courseCategories']);
        $courseCategories = $responseData['courseCategories'][0];

        $expectedKeys = [
            'id',
            'name',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $courseCategories);
        }
    }

    private function getApiRequestResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseEndpoints::coursesEndpoint(),
            bearerToken: $userToken
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            'course_manager',
            'course_creator',
        ]);

        parent::tearDown();
    }
}
