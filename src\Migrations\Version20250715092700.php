<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250715092700 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
               CREATE TABLE purchase_item (
                   id CHAR(36) NOT NULL,
                   purchase_id CHAR(36) NOT NULL,
                   purchase_item_id CHAR(36) NOT NULL,
                   price_amount INT NOT NULL,
                   price_currency VARCHAR(3) NOT NULL,
                   PRIMARY KEY(id),
                   FOREIGN KEY (purchase_id) REFERENCES `purchase` (id) ON DELETE CASCADE,
                   FOREIGN KEY (purchase_item_id) REFERENCES `purchasable_item` (id) ON DELETE CASCADE
               ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
            SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE purchase');
    }
}
