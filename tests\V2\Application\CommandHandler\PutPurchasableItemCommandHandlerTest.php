<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\Mother\Entity\CourseMother;
use App\V2\Application\Command\PutPurchasableItemCommand;
use App\V2\Application\CommandHandler\PutPurchasableItemCommandHandler;
use App\V2\Application\Purchase\PurchasableItemAdapter;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemFactory;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PutPurchasableItemCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?PurchasableItemRepository $purchasableItemRepository = null,
        ?PurchasableItemAdapter $purchasableItemAdapter = null,
        ?ResourceProvider $resourceProvider = null,
    ): PutPurchasableItemCommandHandler {
        if (null === $purchasableItemAdapter) {
            $courseFactory = $this->createMock(PurchasableItemFactory::class);
            $purchasableItemAdapter = new PurchasableItemAdapter([$courseFactory]);
        }

        return new PutPurchasableItemCommandHandler(
            $purchasableItemRepository ?? $this->createMock(PurchasableItemRepository::class),
            $purchasableItemAdapter,
            $resourceProvider ?? $this->createMock(ResourceProvider::class),
        );
    }

    /**
     * @throws Exception
     */
    private function getHandlerWithWorkingAdapter(
        ?PurchasableItemRepository $purchasableItemRepository = null,
        ?ResourceProvider $resourceProvider = null,
    ): PutPurchasableItemCommandHandler {
        $courseFactory = $this->createMock(PurchasableItemFactory::class);
        $courseFactory
            ->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(true);

        $purchasableItemAdapter = new PurchasableItemAdapter([
            $courseFactory,
        ]);

        return new PutPurchasableItemCommandHandler(
            $purchasableItemRepository ?? $this->createMock(PurchasableItemRepository::class),
            $purchasableItemAdapter,
            $resourceProvider ?? $this->createMock(ResourceProvider::class),
        );
    }

    private function getCommand(
        ?Resource $resource = null,
        ?Money $price = null,
    ): PutPurchasableItemCommand {
        return new PutPurchasableItemCommand(
            resource: $resource ?? new Resource(
                type: ResourceType::Course,
                id: new Id(1),
            ),
            price: $price ?? Money::create(1000),
        );
    }

    /**
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(PutPurchasableItemCommandHandler::class, $handler);
    }

    /**
     * @throws Exception
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws ResourceNotFoundException
     */
    public function testHandle(): void
    {
        $course = CourseMother::create();
        $resourceProvider = $this->createMock(ResourceProvider::class);
        $resourceProvider
            ->expects($this->once())
            ->method('getEntity')
            ->willReturn($course);

        $handler = $this->getHandlerWithWorkingAdapter(
            resourceProvider: $resourceProvider,
        );
        $command = $this->getCommand();

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws ResourceNotFoundException
     */
    public function testHandleWithDuplicateResource(): void
    {
        $courseId = 1;
        $course = CourseMother::create(id: $courseId);
        $resourceProvider = $this->createMock(ResourceProvider::class);
        $resourceProvider
            ->expects($this->once())
            ->method('getEntity')
            ->willReturn($course);

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->willThrowException(PurchasableItemRepositoryException::duplicateResource(
                new Resource(
                    type: ResourceType::Course,
                    id: new Id($courseId),
                )
            ));

        $handler = $this->getHandlerWithWorkingAdapter(
            purchasableItemRepository: $purchasableItemRepository,
            resourceProvider: $resourceProvider,
        );
        $command = $this->getCommand();

        $this->expectException(PurchasableItemRepositoryException::class);

        $handler->handle($command);
    }
}
