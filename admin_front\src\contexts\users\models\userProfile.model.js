import { USER_ROLE_NAMES } from '@/contexts/users/constants/users.constants.js'
import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export class UserProfileModel {
  constructor({
    id = 0,
    name = '',
    last_name = '',
    email = '',
    avatar = '',
    locale = '',
    code = '',
    company_name = '',
    zone = '',
    dni = '',
    open_campus = true,
    extra = [],
    roles = [],
    actions = {},
  } = {}) {
    this.firstName = name || ''
    this.id = id || 0
    this.lastName = last_name || ''
    this.fullName = `${this.firstName} ${this.lastName}`.trim()
    this.email = email || ''
    this.avatar = avatar || ''
    this.locale = locale || 'es'
    const languageName = new Intl.DisplayNames(['en'], { type: 'language' })
    this.localeName = languageName.of(this.locale)
    this.code = code || ''
    this.companyName = company_name || ''
    this.zone = zone || ''
    this.dni = dni || ''
    this.openCampus = open_campus || false
    this.extra = (extra || []).map((extraField, index) => ({
      label: extraField.label,
      value: extraField.value,
      key: extraField.key || `ef_${index}`,
    }))
    this.roles = (roles || []).map((roleCode, index) => {
      return {
        key: `${this.key}_role${index}`,
        code: USER_ROLE_NAMES[roleCode] || USER_ROLE_LIST.USER,
      }
    })
    this.actions = {
      impersonate: actions?.allow_login_as || false,
      deletable: actions?.deletable || false,
      editable: actions?.editable || false,
    }
    this.showActions = Object.values(this.actions).some((value) => !!value)
  }
}
