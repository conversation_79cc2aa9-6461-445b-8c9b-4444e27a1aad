<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250708133910 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME NOT NULL, ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen ADD CONSTRAINT FK_4907912BB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen ADD CONSTRAINT FK_4907912B896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen ADD CONSTRAINT FK_4907912BC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4907912BB03A8386 ON parejas_imagen (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4907912B896DBBDE ON parejas_imagen (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4907912BC76F1F52 ON parejas_imagen (deleted_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen DROP FOREIGN KEY FK_4907912BB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen DROP FOREIGN KEY FK_4907912B896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen DROP FOREIGN KEY FK_4907912BC76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_4907912BB03A8386 ON parejas_imagen
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_4907912B896DBBDE ON parejas_imagen
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_4907912BC76F1F52 ON parejas_imagen
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE parejas_imagen DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP updated_at, DROP deleted_at
        SQL);
    }
}
