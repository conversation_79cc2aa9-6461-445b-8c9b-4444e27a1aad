<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Purchase;

use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class PurchasableItemCollectionMother
{
    /**
     * Create a PurchasableItemCollection with default or custom items.
     *
     * @param array $items Items to include in the collection (default: empty array)
     *
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public static function create(
        array $items = [],
    ): PurchasableItemCollection {
        // If no items provided, create a default collection with 3 items
        if (empty($items)) {
            $items = [
                PurchasableItemMother::create(name: 'Item 1'),
                PurchasableItemMother::create(name: 'Item 2'),
                PurchasableItemMother::create(name: 'Item 3'),
            ];
        }

        return new PurchasableItemCollection($items);
    }

    /**
     * Create a PurchasableItemCollection with a specific number of items.
     *
     * @param int $count Number of items to create
     *
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public static function createWithCount(
        int $count,
    ): PurchasableItemCollection {
        $items = [];
        for ($i = 1; $i <= $count; ++$i) {
            $items[] = PurchasableItemMother::create(
                name: "Item $i",
                description: "Description for item $i",
            );
        }

        return new PurchasableItemCollection($items);
    }

    /**
     * Create an empty PurchasableItemCollection.
     *
     * @throws CollectionException
     */
    public static function empty(): PurchasableItemCollection
    {
        return new PurchasableItemCollection([]);
    }

    /**
     * Create a PurchasableItemCollection with a single item.
     *
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public static function withSingleItem(): PurchasableItemCollection
    {
        return new PurchasableItemCollection([
            PurchasableItemMother::create(),
        ]);
    }
}
