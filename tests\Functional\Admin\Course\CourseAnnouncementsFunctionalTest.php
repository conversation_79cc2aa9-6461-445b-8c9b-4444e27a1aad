<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\SettingsConstants;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class CourseAnnouncementsFunctionalTest extends FunctionalTestCase
{
    private ?User $user = null;

    private ?User $testUser = null;

    private const string START_AT = '2025-01-01 00:00:00';
    private const string FINISH_AT = '2025-01-31 00:00:00';

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->testUser = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );
    }

    /**
     * @throws ORMException
     * @throws \DateMalformedStringException
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     */
    #[DataProvider('providerAuthorizedUserCanSeeCourseAnnouncements')]
    public function testAuthorizedUserCanSeeCourseAnnouncements(
        array $userRoles,
        bool $sharingEnabled,
        int $expectedStatusCode,
        int $expectedCount,
        array $expectedItems
    ): void {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: $sharingEnabled ? 'true' : 'false',
        );
        $course = $this->createAndGetCourse(
            name: 'Course 1'
        );

        $announcement1 = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, new \DateTimeZone('Europe/Madrid')),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, new \DateTimeZone('Europe/Madrid')),
            code: 'announcement-1',
            createdBy: $this->user,
        );

        $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, new \DateTimeZone('Europe/Madrid')),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, new \DateTimeZone('Europe/Madrid')),
            code: 'announcement-2',
            createdBy: $this->testUser,
        );

        $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, new \DateTimeZone('Europe/Madrid')),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, new \DateTimeZone('Europe/Madrid')),
            code: 'announcement-3',
            createdBy: $this->user,
        );

        $this->createAndGetAnnouncementManager(
            announcement: $announcement1,
            user: $this->testUser,
        );

        $this->testUser->setRoles($userRoles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCourseEndpoints::getCourseAnnouncementsEndpoint($course->getId()),
            bearerToken: $this->loginAndGetTokenForUser($this->testUser)
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (200 === $expectedStatusCode) {
            $data = $this->extractResponseData($response);
            $this->assertCount($expectedCount, $data);
            $this->assertEquals($expectedItems, $data);
        }
    }

    /**
     * @throws \DateMalformedStringException
     */
    public static function providerAuthorizedUserCanSeeCourseAnnouncements(): \Generator
    {
        $timezone = new \DateTimeZone('Europe/Madrid');

        $startAt = (new \DateTimeImmutable(self::START_AT, $timezone))->format('c');

        $finishAt = (new \DateTimeImmutable(self::FINISH_AT, $timezone))->format('c');

        yield 'Super Admin can see all announcements' => [
            'userRoles' => [User::ROLE_SUPER_ADMIN],
            'sharingEnabled' => true,
            'expectedStatusCode' => 200,
            'expectedCount' => 3,
            'expectedItems' => [
                [
                    'id' => 3,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-3',
                ],
                [
                    'id' => 2,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-2',
                ],
                [
                    'id' => 1,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-1',
                ],
            ],
        ];

        yield 'Admin can see all announcements' => [
            'userRoles' => [User::ROLE_ADMIN],
            'sharingEnabled' => true,
            'expectedStatusCode' => 200,
            'expectedCount' => 3,
            'expectedItems' => [
                [
                    'id' => 3,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-3',
                ],
                [
                    'id' => 2,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-2',
                ],
                [
                    'id' => 1,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-1',
                ],
            ],
        ];

        yield 'Manager can see his own announcements and shared announcements' => [
            'userRoles' => [User::ROLE_MANAGER],
            'sharingEnabled' => true,
            'expectedStatusCode' => 200,
            'expectedCount' => 2,
            'expectedItems' => [
                [
                    'id' => 2,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-2',
                ],
                [
                    'id' => 1,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-1',
                ],
            ],
        ];

        yield 'Manager can see his own announcements when sharing is disabled' => [
            'userRoles' => [User::ROLE_MANAGER],
            'sharingEnabled' => false,
            'expectedStatusCode' => 200,
            'expectedCount' => 1,
            'expectedItems' => [
                [
                    'id' => 2,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-2',
                ],
            ],
        ];

        yield 'Manager editor can see any announcement' => [
            'userRoles' => [User::ROLE_MANAGER_EDITOR],
            'sharingEnabled' => true,
            'expectedStatusCode' => 200,
            'expectedCount' => 2,
            'expectedItems' => [
                [
                    'id' => 2,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-2',
                ],
                [
                    'id' => 1,
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'code' => 'announcement-1',
                ],
            ],
        ];

        yield 'Team manager cannot see any announcement' => [
            'userRoles' => [User::ROLE_TEAM_MANAGER],
            'sharingEnabled' => true,
            'expectedStatusCode' => 302,
            'expectedCount' => 0,
            'expectedItems' => [],
        ];

        yield 'Tutor cannot see any announcement' => [
            'userRoles' => [User::ROLE_TUTOR],
            'sharingEnabled' => true,
            'expectedStatusCode' => 302,
            'expectedCount' => 0,
            'expectedItems' => [],
        ];

        yield 'Creator cannot see any announcement' => [
            'userRoles' => [User::ROLE_CREATOR],
            'sharingEnabled' => true,
            'expectedStatusCode' => 200,
            'expectedCount' => 0,
            'expectedItems' => [],
        ];

        yield 'User cannot see any announcement' => [
            'userRoles' => [User::ROLE_USER],
            'sharingEnabled' => true,
            'expectedStatusCode' => 302,
            'expectedCount' => 0,
            'expectedItems' => [],
        ];

        yield 'Support cannot see any announcement' => [
            'userRoles' => [User::ROLE_SUPPORT],
            'sharingEnabled' => true,
            'expectedStatusCode' => 302,
            'expectedCount' => 0,
            'expectedItems' => [],
        ];

        yield 'Inspector cannot see any announcement' => [
            'userRoles' => [User::ROLE_INSPECTOR],
            'sharingEnabled' => true,
            'expectedStatusCode' => 302,
            'expectedCount' => 0,
            'expectedItems' => [],
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    public function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );

        $this->truncateEntities(
            [
                Course::class,
                CourseCategory::class,
                Announcement::class,
                AnnouncementManager::class,
                UserLogin::class,
                UserManage::class,
            ]
        );

        $this->hardDeleteUsersByIds([
            $this->testUser?->getId(),
        ]);

        parent::tearDown();
    }
}
