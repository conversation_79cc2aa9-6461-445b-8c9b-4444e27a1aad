<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Exception;

class PostAnnouncementGroupUserException extends \Exception
{
    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function userAlreadyInAnnouncement(): self
    {
        return new self('User already belongs to this announcement');
    }

    public static function maxGroupSizeReached(): self
    {
        return new self('Maximum group size reached');
    }
}
