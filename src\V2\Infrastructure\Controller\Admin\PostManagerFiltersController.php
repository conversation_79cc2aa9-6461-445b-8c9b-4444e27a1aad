<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PostManagerFiltersCommand;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Admin\ManagerFilterValidator;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostManagerFiltersController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws CollectionException
     */
    public function __invoke(Request $request, int $userId): Response
    {
        IdValidator::validateId($userId);
        $body = json_decode($request->getContent(), true);

        ManagerFilterValidator::validatePostManagerFilters($body);

        $this->execute(
            new PostManagerFiltersCommand(
                userId: new Id($userId),
                filterIds: new IdCollection(array_map(fn (int $id) => new Id($id), $body))
            )
        );

        return new JsonResponse(
            status: Response::HTTP_OK,
        );
    }
}
