<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Exception;

class PutAnnouncementManagerException extends \Exception
{
    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function managerAlreadyExists(): self
    {
        return new self('User is already a manager of this announcement');
    }

    public static function userIsTheCreatorOfAnnouncement(): self
    {
        return new self('Cannot assign the creator of the announcement as a manager');
    }

    public static function userIsNotAManager(): self
    {
        return new self('User is not a manager');
    }
}
