<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\CourseCategory;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseCategoryEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class AdminCourseCategoryFunctionalTest extends FunctionalTestCase
{
    public function testSingleCourseCategoryStructure(): void
    {
        $response = $this->getCourseCategoriesAll();

        $response = $this->getCourseCategoriesResponse();
        $responseData = $this->extractResponseData($response);

        $this->assertNotEmpty($responseData);
        $this->assertArrayHasKey('data', $responseData);

        $courseCategory = $responseData['data'][0];
        $this->assertArrayHasKey('id', $courseCategory);
        $this->assertArrayHasKey('name', $courseCategory);
        $this->assertArrayHasKey('active', $courseCategory);
    }

    public function testSingleValidationACreateCourseCategory(): void
    {
        $typeCourse = $this->getTypeCourse();
        $this->createAndGetCourseCategories(typeCourse: $typeCourse);

        $response = $this->getCoursesCategoryCreateResponse();
        $responseData = $this->extractResponseData($response);

        $courseCategory = ['id' => $responseData];
        $this->assertArrayHasKey('id', $courseCategory);
    }

    public function testSingleValidationAUpdateCourseCategory(): void
    {
        $typeCourse = $this->getTypeCourse();
        $this->createAndGetCourseCategories(typeCourse: $typeCourse);

        $response = $this->getCoursesCategoryUpdateResponse();
        $responseData = $this->extractResponseData($response);

        $courseCategory = ['id' => $responseData];
        $this->assertArrayHasKey('id', $courseCategory);
    }

    private function getCourseCategoriesResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminCourseCategoryEndpoints::courseCategoriesEndpoint(),
            [],
            [],
            [],
            $userToken
        );
    }

    private function getCoursesCategoryCreateResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'POST',
            AdminCourseCategoryEndpoints::courseCategoriesCreateUpdateEndpoint(),
            $this->setParamsQueryCreateCourseCategories(),
            [],
            [],
            $userToken
        );
    }

    private function getCoursesCategoryUpdateResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'PUT',
            AdminCourseCategoryEndpoints::courseCategoriesCreateUpdateEndpoint(),
            $this->setParamsQueryCreateCourseCategories(),
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([CourseCategory::class]);

        parent::tearDown();
    }
}
