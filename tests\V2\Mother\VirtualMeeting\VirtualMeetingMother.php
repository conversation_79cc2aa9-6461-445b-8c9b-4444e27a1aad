<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\VirtualMeeting;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;

final class VirtualMeetingMother
{
    private const string DEFAULT_URL = 'https://example.com/meeting';

    /**
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    public static function create(
        ?Uuid $id = null,
        ?VirtualMeetingType $type = null,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishAt = null,
        ?string $url = null,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ): VirtualMeeting {
        $now = new \DateTimeImmutable();
        $type ??= VirtualMeetingType::Fixed;

        return new VirtualMeeting(
            $id ?? UuidMother::create(),
            $type ?? VirtualMeetingType::Fixed,
            $startAt ?? $now->modify('+1 day')->setTime(10, 0),
            $finishAt ?? $now->modify('+2 days')->setTime(22, 0),
            $url ?? (
                (VirtualMeetingType::Fixed === $type) ? self::DEFAULT_URL : null
            ),
            $createdAt ?? $now,
            $updatedAt,
            $deletedAt
        );
    }
}
