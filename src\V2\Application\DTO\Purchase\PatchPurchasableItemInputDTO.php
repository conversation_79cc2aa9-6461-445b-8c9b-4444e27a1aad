<?php

declare(strict_types=1);

namespace App\V2\Application\DTO\Purchase;

use App\V2\Domain\Shared\Financial\Money;

readonly class PatchPurchasableItemInputDTO
{
    public function __construct(
        private ?Money $price = null,
        private ?bool $isActive = null,
    ) {
    }

    public function getPrice(): ?Money
    {
        return $this->price;
    }

    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }
}
