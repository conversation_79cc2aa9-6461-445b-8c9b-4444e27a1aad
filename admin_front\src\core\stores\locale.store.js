import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import StorageService from '@/core/services/storage.service.js'
import { getBrowserLocales, stringToJson } from '@/core/utils/misc.utils.js'

export const useLocalStore = defineStore('localeStore', () => {
  const locales = ref({ admin: [], campus: [] })
  async function fetchLocales() {
    if (StorageService.getLocaleList()) locales.value = stringToJson(window.atob(StorageService.getLocaleList()))
    if (!$settings.LOCALE_LIST_URL) {
      return null
    }
    const { data: adminLocales, error: adminError } = await ApiService.get($settings.LOCALE_LIST_URL)
    if (adminError) {
      return null
    }
    locales.value.admin = adminLocales

    const { data: campusLocales, error: campusError } = await ApiService.get(
      $settings.LOCALE_LIST_URL,
      {},
      { baseURL: '/api/v2/' }
    )
    if (campusError) {
      return null
    }
    locales.value.campus = campusLocales
  }

  const locale = ref('')
  function loadLocale() {
    if (currentLocale.value) {
      locale.value = currentLocale.value
      return null
    }

    const browserLocales = getBrowserLocales()
    if (browserLocales.length) {
      const found = browserLocales.find((locale) => locale in locales.value.admin)
      if (found) {
        locale.value = found
        return null
      }
    }

    locale.value = clientDefaultLocale.value
  }

  const currentLocale = computed(() => locale.value || StorageService.getLocale() || $settings.DEFAULT_LOCALE)
  const clientDefaultLocale = computed(() => $settings.DEFAULT_LOCALE)

  return {
    currentLocale,
    clientDefaultLocale,
    locales,
    locale,
    fetchLocales,
    loadLocale,
  }
})
