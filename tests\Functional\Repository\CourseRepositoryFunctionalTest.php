<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;

class CourseRepositoryFunctionalTest extends FunctionalTestCase
{
    private User $user;
    private TypeCourse $typeCourse;
    private Course $course1;
    private Course $course2;
    private Course $course3;
    private Chapter $chapter1;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->typeCourse = $this->getEntityManager()->getRepository(TypeCourse::class)->findOneBy(['active' => 'true']);
        $this->courseCategory = $this->createAndGetCourseCategory();
        $this->course1 = $this->createAndGetCourse(
            name: 'Test Course 1',
            typeCourse: $this->typeCourse,
            courseCategory: $this->courseCategory,
        );
        $this->course2 = $this->createAndGetCourse(
            name: 'Test Course 2',
            typeCourse: $this->typeCourse,
            courseCategory: $this->courseCategory,
        );
        $this->course3 = $this->createAndGetCourse(
            name: 'Test Course 3',
            typeCourse: $this->typeCourse,
            courseCategory: $this->courseCategory,
        );
        $this->chapter1 = $this->createAndGetChapter(
            course: $this->course1,
            title: 'Chapter 1',
        );
        $this->chapter2 = $this->createAndGetChapter(
            course: $this->course2,
            title: 'Chapter 2',
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     * @throws Exception
     */
    public function testGetCourseDataWithNoChapter(): void
    {
        $this->course3->setCreatedBy($this->user);
        $this->course3->setTypeCourse($this->typeCourse);

        $this->getEntityManager()->flush();

        $coursesData = $this->getEntityManager()->getRepository(Course::class)->getCoursesData([$this->course3->getId()]);

        $this->assertCount(0, $coursesData);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     * @throws Exception
     */
    public function testGetCourse1DataWithChapter(): void
    {
        $this->course1->setCreatedBy($this->user);

        $this->getEntityManager()->flush();

        $coursesData = $this->getEntityManager()->getRepository(Course::class)->getCoursesData([$this->course1->getId()]);

        $this->assertCount(1, $coursesData);
        $this->assertEquals('Test Course 1', $coursesData[0]['name']);
        $this->assertEquals($this->user->getEmail(), $coursesData[0]['created_by']);
        $this->assertCoursesDataStructure($coursesData);
    }

    public function testGetCourse2DataWithChapter(): void
    {
        $this->course2->setCreatedBy($this->user);

        $this->getEntityManager()->flush();

        $coursesData = $this->getEntityManager()->getRepository(Course::class)->getCoursesData([$this->course2->getId()]);

        $this->assertCount(1, $coursesData);
        $this->assertEquals('Test Course 2', $coursesData[0]['name']);
        $this->assertEquals($this->user->getEmail(), $coursesData[0]['created_by']);
        $this->assertCoursesDataStructure($coursesData);
    }

    public function testGetCoursesDataWithChapter(): void
    {
        $coursesData = $this->getEntityManager()->getRepository(Course::class)->getCoursesData();

        $this->assertCount(2, $coursesData);
        $this->assertCoursesDataStructure($coursesData);
    }

    private function assertCoursesDataStructure(array $coursesData): void
    {
        foreach ($coursesData as $courseData) {
            // Assert that all expected fields are present
            $this->assertArrayHasKey('id', $courseData);
            $this->assertArrayHasKey('name', $courseData);
            $this->assertArrayHasKey('code', $courseData);
            $this->assertArrayHasKey('category', $courseData);
            $this->assertArrayHasKey('created_by', $courseData);
            $this->assertArrayHasKey('created_at', $courseData);
            $this->assertArrayHasKey('no_chapters', $courseData);
            $this->assertArrayHasKey('no_seasons', $courseData);
            $this->assertArrayHasKey('active', $courseData);
            $this->assertArrayHasKey('open', $courseData);
            $this->assertArrayHasKey('open_campus', $courseData);
            $this->assertArrayHasKey('is_new', $courseData);
            $this->assertArrayHasKey('langs', $courseData);
            $this->assertArrayHasKey('avg_rating', $courseData);
            $this->assertArrayHasKey('avg_time', $courseData);
            $this->assertArrayHasKey('time_spent', $courseData);
            $this->assertArrayHasKey('started', $courseData);
            $this->assertArrayHasKey('finished', $courseData);

            // Assert data types
            $this->assertIsInt($courseData['id']);
            $this->assertIsString($courseData['name']);
            $this->assertIsString($courseData['code']);
            $this->assertIsString($courseData['category']);
            $this->assertIsNumeric($courseData['no_chapters']);
            $this->assertIsNumeric($courseData['no_seasons']);
            $this->assertContains($courseData['active'], ['YES', 'NO']);
            $this->assertContains($courseData['open'], ['YES', 'NO']);
            $this->assertContains($courseData['open_campus'], ['YES', 'NO']);
            $this->assertContains($courseData['is_new'], ['YES', 'NO']);
            $this->assertIsString($courseData['langs']);
            $this->assertIsNumeric($courseData['avg_rating']);
            $this->assertIsNumeric($courseData['avg_time']);
            $this->assertIsNumeric($courseData['time_spent']);
            $this->assertIsNumeric($courseData['started']);
            $this->assertIsNumeric($courseData['finished']);
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Chapter::class,
        ]);

        parent::tearDown();
    }
}
