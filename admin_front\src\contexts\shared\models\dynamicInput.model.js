import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'
import { addPadStart } from '@/core/utils/number.utils.js'

export class DynamicInputModel {
  constructor({ id = 0, name = '', error = '', type = '', options = [], value } = {}) {
    this.id = id || 0
    this.name = name || ''
    this.options = options || []
    this.type = type || INPUT_TYPES.DEFAULT
    this.value = this.parseValue(value)
    this.initialValue = this.parseValue(value)
    this.error = error || ''
    this.key = `input_${this.id}`
  }

  reset() {
    this.value = this.parseValue(this.initialValue)
  }

  parseValue(newValue = '') {
    if (this.type === INPUT_TYPES.BOOLEAN) {
      return !!newValue
    }
    if (this.type === INPUT_TYPES.INTEGER) {
      return +newValue
    }
    if (this.type === INPUT_TYPES.MULTISELECT) {
      return newValue
    }
    return `${newValue || ''}`
  }

  getInputValue() {
    return { id: this.id, value: this.getParsedDataValue(this.value) }
  }

  getParsedDataValue() {
    if (this.type === INPUT_TYPES.BOOLEAN) {
      return !!this.value
    }
    if (this.type === INPUT_TYPES.INTEGER) {
      return isNaN(+this.value) ? 0 : +this.value
    }
    if (this.type === INPUT_TYPES.MULTISELECT) {
      return this.value
    }
    if (this.type === INPUT_TYPES.DATE && this.value && typeof this.value === 'object') {
      return `${this.value.getFullYear()}-${addPadStart(this.value.getMonth() + 1)}-${addPadStart(this.value.getDate())} 00:00:00`
    }
    return `${this.value || ''}`.trim()
  }
}
