<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use App\Tests\Functional\V2\Fixtures\FilterFixtureTrait;
use App\Tests\Functional\V2\Fixtures\ManagerFilterFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class PostManagerFiltersFunctionalTest extends FunctionalTestCase
{
    use FilterFixtureTrait;
    use ManagerFilterFixtureTrait;

    private const string EMAIL_MANAGER = '<EMAIL>';
    private ?User $manager;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->manager = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: self::EMAIL_MANAGER);
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1)
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '' => 'Body cannot be empty',
            ],
        ], $content['metadata']);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1),
            body: json_encode([0, -1, 'aA']),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[0]' => 'This value should be greater than or equal to 1.',
                '[1]' => 'This value should be greater than or equal to 1.',
                '[2]' => 'This value should be of type integer.',
            ],
        ], $content['metadata']);
    }

    public function testPostManagerFilters(): void
    {
        $token = $this->loginAndGetToken();

        // Try not existing user
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(999),
            body: json_encode([1]),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User not found', $content['message']);

        // try not existing filter
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            body: json_encode([1]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('At least one of the filters does not exist', $content['message']);

        $this->setAndGetFilterInRepository(
            id: new Id(1),
            name: 'Filter 1',
        );
        $this->setAndGetFilterInRepository(
            id: new Id(2),
            name: 'Filter 2',
        );
        $this->setAndGetFilterInRepository(
            id: new Id(3),
            name: 'Filter 3',
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            body: json_encode([1, 3]),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        // Try to reassign filter
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            body: json_encode([3]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('At least one of the filters is already assigned to a user', $content['message']);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([$this->manager->getId()]);
        parent::tearDown();
    }
}
