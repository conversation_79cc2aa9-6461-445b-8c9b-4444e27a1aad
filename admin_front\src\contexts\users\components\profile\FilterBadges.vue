<template>
  <div class="FilterBadges">
    <p>{{ item.category }}</p>
    <div class="badgeContainer">
      <BaseBadge
        v-for="filter in item.filters"
        :key="filter.key"
        type="info"
        :value="filter.name"
      />
    </div>
  </div>
</template>

<script setup>
import { UserProfileFilterModel } from '@/contexts/users/models/userProfileFilter.model.js'
import BaseBadge from '@/contexts/shared/components/BaseBadge.vue'

defineProps({
  item: { type: [UserProfileFilterModel, Object], default: () => ({}) },
})
</script>

<style scoped lang="scss">
.FilterBadges {
  p {
    font-weight: bold;
    margin: 0;
  }

  .badgeContainer {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}
</style>
