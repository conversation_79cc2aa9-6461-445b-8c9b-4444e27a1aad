<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Announcement;

use App\Entity\Announcement as LegacyAnnouncement;
use App\Entity\Course as LegacyCourse;
use App\Entity\User;
use App\Service\SettingsService;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Id\Id;

readonly class AnnouncementAuthorizationService implements AnnouncementAuthorizationServiceInterface
{
    public function __construct(
        private AnnouncementManagerRepository $announcementManagerRepository,
        private CourseManagerRepository $courseManagerRepository,
        private SettingsService $settingsService,
    ) {
    }

    #[\Override]
    public function ensureUserCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if ($user->isManager() && $announcement->getCreatedBy()?->getId() === $user->getId()) {
            return;
        }

        // Shared managers can if sharing is enabled and they are actually shared
        if ($user->isManager() && $this->settingsService->get('app.announcement.managers.sharing')) {
            try {
                $this->announcementManagerRepository->findOneBy(
                    AnnouncementManagerCriteria::createEmpty()
                        ->filterByUserId($user->getId())
                        ->filterByAnnouncementId($announcement->getId())
                );

                return;
            } catch (AnnouncementManagerNotFoundException) {
                // Manager is not shared for this announcement.
            }
        }

        throw ManagerNotAuthorizedException::userNotAuthorized(
            announcement: $announcement,
            user: $user->getEmail() ?? 'Unknown user'
        );
    }

    #[\Override]
    public function ensureUserCanCreateAnnouncement(User $user, LegacyCourse $course): void
    {
        if ($user->isAdmin()) {
            return;
        }

        $courseManagerCollection = $this->courseManagerRepository->findBy(
            CourseManagerCriteria::createEmpty()
                ->filterByUserId(new Id($user->getId())),
        );

        if ($courseManagerCollection->isEmpty()) {
            return; // No assigned courses, can create the announcement.
        }

        $result = $courseManagerCollection->filter(
            fn (CourseManager $courseManager) => $courseManager->getCourseId()->value() === $course->getId()
        );

        if (!$result->isEmpty()) {
            return; // Course assigned
        }

        throw ManagerNotAuthorizedException::userNoPermissionsToCreateAnnouncementForCourse(
            course: $course,
            email: $user->getEmail(),
        );
    }
}
