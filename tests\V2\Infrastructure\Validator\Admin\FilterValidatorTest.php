<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\FilterValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class FilterValidatorTest extends ValidatorTestCase
{
    #[DataProvider('provideValidateGetFilters')]
    public function testValidateGetFilters(array $payload): void
    {
        $this->expectNotToPerformAssertions();
        FilterValidator::validateGetFilters($payload);
    }

    #[DataProvider('provideInvalidValidateGetFilters')]
    public function testInvalidValidateGetFilters(array $payload, array $violations): void
    {
        try {
            FilterValidator::validateGetFilters($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideValidateGetFilters(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
        ];

        yield 'with search string' => [
            'payload' => [
                'search' => 'Value',
            ],
        ];

        yield 'with category id' => [
            'payload' => [
                'category_id' => '1',
            ],
        ];

        yield 'with parent id' => [
            'payload' => [
                'parent_id' => '1',
            ],
        ];

        yield 'with all id' => [
            'payload' => [
                'parent_id' => '1',
                'category_id' => '1',
                'search' => 'Value',
            ],
        ];
    }

    public static function provideInvalidValidateGetFilters(): \Generator
    {
        yield 'empty search' => [
            'payload' => [
                'search' => '',
            ],
            'violations' => [
                '[search]' => 'This value should not be blank.',
            ],
        ];
        yield 'invalid search type' => [
            'payload' => [
                'search' => 1212,
            ],
            'violations' => [
                '[search]' => 'This value should be of type string.',
            ],
        ];

        yield 'parent id less than 1' => [
            'payload' => [
                'parent_id' => '0',
            ],
            'violations' => [
                '[parent_id]' => 'parent_id must be greater than 0.',
            ],
        ];

        yield 'parent id as string' => [
            'payload' => [
                'parent_id' => 'a',
            ],
            'violations' => [
                '[parent_id]' => 'This value should be of type digit.',
            ],
        ];

        yield 'category id less than 1' => [
            'payload' => [
                'category_id' => '0',
            ],
            'violations' => [
                '[category_id]' => 'category_id must be greater than 0.',
            ],
        ];

        yield 'category id as string' => [
            'payload' => [
                'category_id' => 'a',
            ],
            'violations' => [
                '[category_id]' => 'This value should be of type digit.',
            ],
        ];
    }
}
