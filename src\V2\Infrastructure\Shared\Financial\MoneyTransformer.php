<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Financial;

use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;

class MoneyTransformer
{
    /**
     * @throws InvalidCurrencyCodeException
     */
    public static function fromPayload(array $payload): Money
    {
        return Money::create(
            amount: $payload['price_amount'],
            currency: Currency::fromCode(CurrencyCodeTransformer::fromString($payload['price_currency']))
        );
    }
}
