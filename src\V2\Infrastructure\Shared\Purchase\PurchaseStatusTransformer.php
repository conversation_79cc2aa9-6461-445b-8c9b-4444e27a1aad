<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Purchase;

use App\V2\Domain\Purchase\PurchaseStatus;

class PurchaseStatusTransformer
{
    public static function toString(PurchaseStatus $status): string
    {
        return match ($status) {
            PurchaseStatus::Pending => 'pending',
            PurchaseStatus::Completed => 'completed',
            PurchaseStatus::Cancelled => 'cancelled',
        };
    }

    public static function fromString(string $status): PurchaseStatus
    {
        return match ($status) {
            'pending' => PurchaseStatus::Pending,
            'completed' => PurchaseStatus::Completed,
            'cancelled' => PurchaseStatus::Cancelled,
        };
    }
}
