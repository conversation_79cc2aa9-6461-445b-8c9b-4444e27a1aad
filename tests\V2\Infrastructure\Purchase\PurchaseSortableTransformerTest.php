<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase;

use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Infrastructure\Purchase\PurchaseSortableTransformer;
use App\V2\Infrastructure\Shared\QueryParamTransformer\LifeCycleSortableTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PurchaseSortableTransformerTest extends TestCase
{
    /**
     * Test that getSortableFields returns the expected array of sortable fields.
     */
    public function testGetSortableFields(): void
    {
        // Act
        $result = PurchaseSortableTransformer::getSortableFields();

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('created_at', $result);
        $this->assertArrayHasKey('amount', $result);
        $this->assertSame('id', $result['id']);
        $this->assertSame('status', $result['status']);
        $this->assertSame('createdAt', $result['created_at']);
        $this->assertSame('amount', $result['amount']);
    }

    /**
     * Test that toSortableField correctly transforms valid sort fields.
     */
    #[DataProvider('validSortFieldProvider')]
    public function testToSortableFieldWithValidField(string $sortBy, string $expected): void
    {
        // Act
        $result = PurchaseSortableTransformer::toSortableField($sortBy);

        // Assert
        $this->assertInstanceOf(SortableField::class, $result);
        $this->assertSame($expected, $result->value());
    }

    /**
     * Test that toSortableField correctly handles lifecycle fields.
     */
    #[DataProvider('lifecycleSortFieldProvider')]
    public function testToSortableFieldWithLifecycleField(string $sortBy, string $expected): void
    {
        // Act
        $result = PurchaseSortableTransformer::toSortableField($sortBy);

        // Assert
        $this->assertInstanceOf(SortableField::class, $result);
        $this->assertSame($expected, $result->value());
    }

    /**
     * Test that toSortableField throws an exception for invalid sort fields.
     */
    public function testToSortableFieldWithInvalidField(): void
    {
        // Arrange
        $invalidField = 'invalid_field';

        // Assert
        $this->expectException(InvalidSortException::class);
        $this->expectExceptionMessage(\sprintf('Field %s is not sortable.', $invalidField));

        // Act
        PurchaseSortableTransformer::toSortableField($invalidField);
    }

    /**
     * Data provider for testToSortableFieldWithValidField.
     */
    public static function validSortFieldProvider(): \Generator
    {
        yield 'id' => [
            'sortBy' => 'id',
            'expected' => 'id',
        ];

        yield 'status' => [
            'sortBy' => 'status',
            'expected' => 'status',
        ];

        yield 'created_at' => [
            'sortBy' => 'created_at',
            'expected' => 'createdAt',
        ];

        yield 'amount' => [
            'sortBy' => 'amount',
            'expected' => 'amount',
        ];
    }

    /**
     * Data provider for testToSortableFieldWithLifecycleField.
     */
    public static function lifecycleSortFieldProvider(): \Generator
    {
        foreach (LifeCycleSortableTransformer::getSortableFields() as $key => $value) {
            yield $key => [
                'sortBy' => $key,
                'expected' => $value,
            ];
        }
    }
}
