<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Filter;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Filter\FilterMother;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class FilterCollectionTest extends CollectionTestCase
{
    protected function getExpectedType(): string
    {
        return Filter::class;
    }

    protected function getItem(): object
    {
        return FilterMother::create();
    }

    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new FilterCollection($items);
    }
}
