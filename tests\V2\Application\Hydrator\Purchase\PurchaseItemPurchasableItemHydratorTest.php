<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\Purchase;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\Purchase\PurchaseItemPurchasableItemHydrator;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemHydrationCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PurchaseItemPurchasableItemHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?PurchasableItemRepository $purchasableItemRepository = null,
    ): PurchaseItemPurchasableItemHydrator {
        return new PurchaseItemPurchasableItemHydrator(
            purchasableItemRepository: $purchasableItemRepository
                ?? $this->createMock(PurchasableItemRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                PurchaseItemHydrationCriteria::createEmpty()->withPurchasableItem()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                PurchaseItemHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws Exception
     * @throws PurchasableItemRepositoryException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testEmptyCollection(): void
    {
        $collection = new PurchaseItemCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            PurchaseItemHydrationCriteria::createEmpty()->withPurchasableItem()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemRepositoryException
     */
    public function testHydrate(): void
    {
        $purchasableItem1 = PurchasableItemMother::create();
        $purchasableItem2 = PurchasableItemMother::create();
        $purchasableItem3 = PurchasableItemMother::create();

        $purchaseItem1 = PurchaseItemMother::create(
            purchasableItemId: $purchasableItem1->getId()
        );
        $purchaseItem2 = PurchaseItemMother::create(
            purchasableItemId: $purchasableItem2->getId()
        );
        $purchaseItem3 = PurchaseItemMother::create(
            purchasableItemId: $purchasableItem3->getId()
        );

        $collection = new PurchaseItemCollection([$purchaseItem1, $purchaseItem2, $purchaseItem3]);

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new PurchasableItemCollection([$purchasableItem1, $purchasableItem2, $purchasableItem3]));

        $hydrator = $this->getHydrator(
            purchasableItemRepository: $purchasableItemRepository
        );

        $hydrator->hydrate(
            $collection,
            PurchaseItemHydrationCriteria::createEmpty()->withPurchasableItem()
        );

        $this->assertEquals($purchasableItem1, $purchaseItem1->getPurchasableItem());
        $this->assertEquals($purchasableItem2, $purchaseItem2->getPurchasableItem());
        $this->assertEquals($purchasableItem3, $purchaseItem3->getPurchasableItem());
    }
}
