<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250630095830 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add isActive field to purchasable_item table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE purchasable_item ADD is_active TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE purchasable_item DROP is_active');
    }
}
