<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Shared\Financial;

use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;

class CurrencyMother
{
    private const CurrencyCode DEFAULT_CURRENCY_CODE = CurrencyCode::EUR;

    public static function create(
        ?CurrencyCode $code = null,
    ): Currency {
        return new Currency($code ?? self::DEFAULT_CURRENCY_CODE);
    }

    public static function eur(): Currency
    {
        return new Currency(CurrencyCode::EUR);
    }

    public static function usd(): Currency
    {
        return new Currency(CurrencyCode::USD);
    }
}
