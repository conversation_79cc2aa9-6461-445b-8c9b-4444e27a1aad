<?php

namespace App\Entity;

use App\Repository\FilterRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=FilterRepository::class)
 */
class Filter implements TranslatableInterface
{
    public const SOURCE_REMOTE = 'remote';
    public const SOURCE_LOCAL = 'local';

    use TranslatableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"only_filter", "details", "user_area", "filter_excel","ranking", "itinerary","library"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"only_filter","details", "user_area","filter_excel","ranking", "itinerary","library"})
     */
    private $name;

    /**
    * @ORM\ManyToOne(targetEntity=FilterCategory::class, inversedBy="filters", cascade={"persist"})
    * @Groups({"details","filter_excel","ranking"})
    */
    private $filterCategory;

    /**
     * @ORM\ManyToMany(targetEntity=Course::class, mappedBy="filters", cascade={"persist"})
     */
    private $courses;

    /**
     * @ORM\ManyToOne(targetEntity=Filter::class, inversedBy="children")
     */
    private $parent;

    /**
     * @ORM\OneToMany(targetEntity=Filter::class, mappedBy="parent")
     */
    private $children;

    /**
     * @ORM\ManyToMany(targetEntity=User::class, mappedBy="filter")
     */
    private $users;

    /**
     * @ORM\ManyToMany(targetEntity=User::class, mappedBy="filters", cascade={"persist", "remove"})
     */
    private $managerFilters;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $code;

    /**
     * @ORM\ManyToMany(targetEntity=Itinerary::class, mappedBy="filters")
     */
    private $itineraries;

    /**
     * @ORM\ManyToMany(targetEntity=Library::class, mappedBy="filters")
     */
    private $libraries;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     */
    private $source;

    /**
     * @ORM\Column(type="integer")
     */
    private $sort;


    public function __construct()
    {
        $this->children       = new ArrayCollection();
        $this->users          = new ArrayCollection();
        $this->managerFilters = new ArrayCollection();
        $this->courses        = new ArrayCollection();
        $this->itineraries = new ArrayCollection();
        $this->libraries = new ArrayCollection();
        $this->sort = 0;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }


    public function __toString()
    {
        return  $this->filterCategory  . " : " . $this->name ;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFilterCategory(): ?FilterCategory
    {
        return $this->filterCategory;
    }

    public function setFilterCategory(?FilterCategory $filterCategory): self
    {
        $this->filterCategory = $filterCategory;

        return $this;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    public function addChild(self $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }

    public function removeChild(self $child): self
    {
        if ($this->children->removeElement($child)) {
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this) {
                $child->setParent(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|User[]
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(User $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users[] = $user;
            $user->addFilter($this);
        }

        return $this;
    }

    public function removeUser(User $user): self
    {
        if ($this->users->removeElement($user)) {
            $user->removeFilter($this);
        }

        return $this;
    }



    /**
     * @return Collection|User[]
     */
    public function getManagerFilters(): Collection
    {
        return $this->managerFilters;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }


    /**
     * @return Collection|Course[]
     */
    public function getCourses(): Collection
    {
        return $this->courses;
    }

    public function addCourse(Course $course): self
    {
        if (!$this->courses->contains($course)) {
            $this->courses[] = $course;
            $course->addFilter($this);
        }

        return $this;
    }

    public function removeCourse(Course $course): self
    {
        if ($this->users->removeElement($course)) {
            $course->removeFilter($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, Itinerary>
     */
    public function getItineraries(): Collection
    {
        return $this->itineraries;
    }

    public function addItinerary(Itinerary $itinerary): self
    {
        if (!$this->itineraries->contains($itinerary)) {
            $this->itineraries[] = $itinerary;
            $itinerary->addFilter($this);
        }

        return $this;
    }

    public function removeItinerary(Itinerary $itinerary): self
    {
        if ($this->itineraries->removeElement($itinerary)) {
            $itinerary->removeFilter($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, Library>
     */
    public function getLibraries(): Collection
    {
        return $this->libraries;
    }

    public function addLibrary(Library $library): self
    {
        if (!$this->libraries->contains($library)) {
            $this->libraries[] = $library;
            $library->addFilter($this);
        }

        return $this;
    }

    public function removeLibrary(Library $library): self
    {
        if ($this->libraries->removeElement($library)) {
            $library->removeFilter($this);
        }

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }
}
