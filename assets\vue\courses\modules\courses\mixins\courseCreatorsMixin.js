import { get } from 'vuex-pathify';

export const courseCreatorsMixin = {
    computed: {
        ...get("coursesModule", {
            courseCreators: "getCourseCreators",
            courseInfo: "getCourseInfo",
        }),

        canManageCourseContent() {
            if (!this.$auth.isCreator) {
                return false
            }

            const user = this.$auth.getUser()
            if (this.courseInfo?.createdBy === user.email) {
                return true
            }

            return this.courseCreators?.some(creator => creator.id === user.id);
        },
  },

    async mounted() {
    await this.loadCourseCreators();
  },

  methods: {
    async loadCourseCreators() {
      await this.$store.dispatch("coursesModule/fetchCourseCreators", this.courseId);
      },
  },
};
