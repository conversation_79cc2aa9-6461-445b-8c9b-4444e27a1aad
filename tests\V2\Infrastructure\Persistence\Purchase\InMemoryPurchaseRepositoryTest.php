<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Purchase;

use App\Tests\V2\Domain\Purchase\PurchaseRepositoryTestCase;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Infrastructure\Persistence\Purchase\InMemoryPurchaseRepository;

class InMemoryPurchaseRepositoryTest extends PurchaseRepositoryTestCase
{
    protected function getRepository(): PurchaseRepository
    {
        return new InMemoryPurchaseRepository();
    }
}
