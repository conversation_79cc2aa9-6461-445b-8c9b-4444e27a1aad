<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AlertTypeTutor;
use App\Entity\AlertTypeTutorTranslation;
use App\Entity\TypeCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AlertTypeTutor>
 *
 * @method AlertTypeTutor|null find($id, $lockMode = null, $lockVersion = null)
 * @method AlertTypeTutor|null findOneBy(array $criteria, array $orderBy = null)
 * @method AlertTypeTutor[]    findAll()
 * @method AlertTypeTutor[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AlertTypeTutorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AlertTypeTutor::class);
    }

    public function add(AlertTypeTutor $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AlertTypeTutor $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getActiveTranslatedList(?string $locale = null): array
    {
        $list = [];
        foreach ($this->findBy(['active' => true]) as $alertTypeTutor) {
            $name = null;
            $description = null;
            if (!empty($locale)) {
                /** @var AlertTypeTutorTranslation $translation */
                $translation = $alertTypeTutor->translate($locale, false);
                $name = $translation->getName();
                $description = $translation->getDescription();
            }
            $typeCourses = [];
            foreach ($alertTypeTutor->getTypeCourseAlerts() as $typeCourseAlert) {
                $typeCourse = $this
                    ->getEntityManager()
                    ->getRepository(TypeCourse::class)
                    ->find($typeCourseAlert->getTypeCourse()->getId());

                if (!$typeCourse) {
                    continue;
                }

                $typeCourses[] = [
                    'id' => $typeCourse->getId(),
                    'name' => $typeCourse->getName(),
                    'type' => $typeCourse->getCode(),
                ];
            }
            $list[] = [
                'id' => $alertTypeTutor->getId(),
                'name' => $name ?? $alertTypeTutor->getName(),
                'description' => $description ?? $alertTypeTutor->getDescription(),
                'typeCourses' => $typeCourses,
            ];
        }

        return $list;
    }
}
