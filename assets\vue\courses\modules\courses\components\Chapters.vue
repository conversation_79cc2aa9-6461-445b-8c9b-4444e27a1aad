<template>
  <div>
    <div
      class="d-flex align-items-center justify-content-center"
      v-if="isLoading"
    >
      <Spinner />
    </div>

    <div v-else>
      <div class="pb-4 d-flex justify-content-end">
        <select
          v-if="seasons.length"
          id="season-filter"
          class="form-control mr-1 w-auto"
          v-model="selectedSeason"
        >
          <option v-if="seasons.length > 1" value="" selected>
            {{ $t("COURSE.ALL_SEASONS") }}
          </option>
          <option v-for="season in seasons" :key="season.id" :value="season.id">
            {{ season.name }}
          </option>
        </select>
        <a class="action-new btn btn-primary mb-1" :href="addChapterUrl" v-if="$auth.hasPermission(COURSE_PERMISSIONS.CREATE_CHAPTER) || canManageCourseContent">
          {{ $t("COURSE.ADD_CHAPTER") }}
        </a>
      </div>

      <BaseNotResult v-if="!filteredChapters.length" />

      <div v-else>
        <div class="course-chapters">
          <div
            v-for="(chapter, index) in filteredChapters"
            :key="'chapter_' + chapter.id"
            class="card"
            draggable="true"
            @dragstart="dragStart(index)"
            @drop="drop(index)"
            @dragover.prevent
            @dragenter.prevent
          >
            <div
              :class="{
                'card-chapter-warning': !chapter.hasContentCompleted,
                'card-chapter-success': chapter.hasContentCompleted,
              }"
            >
              <div class="icon tooltip-container">
                <i
                  v-if="!chapter.hasContentCompleted"
                  class="fas fa-exclamation-triangle"
                ></i>
                <span class="tooltip" v-if="!chapter.hasContentCompleted">
                  {{ $t("MESSAGE_API.ALERT.CHAPTER_CONTENT") }}
                </span>
              </div>
            </div>
            <div
              class="card-img card-img-background-default"
              :style="`background-image: url('${
                chapter.image
                  ? chapter.thumbnailUrl
                  : '/assets/chapters/noimg.svg'
              }')`"
            >
              <div class="order card-order">{{ chapter.order }}</div>
              <div class="card-icon">
                <img
                  :src="`/assets/chapters/${chapter.icon}`"
                  :alt="chapter.seasonName"
                  :title="chapter.seasonName"
                  style="max-width: 33px"
                />
              </div>
            </div>
            <div class="card-body card-body-chapter">
              <p class="card-title">{{ chapter.name }}</p>
              <div>
                <h6>{{ chapter.seasonName }}</h6>
                <div class="row">
                  <div class="col-md-6 d-flex gap-1">
                    <a :href="chapter.editUrl" class="btn btn-primary btn-sm" v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.CHAPTERS) || canManageCourseContent">
                      <i class="fa fa-pencil"></i>
                    </a>
                    <button
                      class="btn btn-primary play btn-sm"
                      :disabled="!chapter.hasContentCompleted"
                      @click="openChapterPlayer(chapter)"
                    >
                      <i class="fa fa-play"></i>
                    </button>
                    <button
                      v-if="specialTypes.includes(chapter.typeId) && ($auth.hasPermission(COURSE_PERMISSIONS.UPDATE.CHAPTERS) || canManageCourseContent)"
                      class="btn btn-primary play btn-sm"
                      @click="openChapterPlayer(chapter, true)"

                    >
                      <i class="fa fa-pencil-square"></i>
                    </button>
                  </div>
                  <div class="col-md-6 text-right">
                    <button
                      class="btn btn-danger btn-sm"
                      @click="deleteChapter(chapter.id)"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <p class="mt-4">
          <b>{{ totalChapters }}</b> {{ $t("RESULTS") }}
        </p>
        <PlayerGames ref="playerGames" />
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../../../base/BaseNotResult.vue";
import PlayerGames from "./PlayerGames.vue";
import {courseCreatorsMixin} from "../mixins/courseCreatorsMixin";
import {COURSE_PERMISSIONS} from "../../../../common/utils/auth/permissions/course.permissions";

export default {
  name: "Chapters",
  mixins: [courseCreatorsMixin],
  components: {
    Spinner,
    BaseNotResult,
    PlayerGames,
  },
  data() {
    return {
      isLoading: true,
      selectedSeason: "",
      specialTypes: [22, 23],
      initDrag: -1,
      finishDrag: 0,
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS
    },
    ...get("coursesModule", {
      chapters: "getChapters",
      seasons: "getSeasons",
      addChapterUrl: "getAddChapterUrl",
    }),
    filteredChapters() {
      return this.selectedSeason
        ? this.chapters.filter(
            (chapter) => chapter.seasonId === this.selectedSeason
          )
        : this.chapters;
    },
    totalChapters() {
      return this.filteredChapters.length;
    },
    courseId() {
      return this.$route.params.id;
    },
  },
  async created() {
    this.isLoading = true;
    await this.fetchChapters();
    this.isLoading = false;
    if (this.seasons.length === 1) {
      this.selectedSeason = this.seasons[0].id;
    }
  },
  methods: {
    async fetchChapters() {
      const currentUrl = window.location.href;
      await this.$store.dispatch("coursesModule/fetchChapters", {
        endpoint: `/admin/api/v1/courses/${this.courseId}/chapters`,
        currentUrl: currentUrl,
      });
      await this.$store.dispatch("coursesModule/fetchSeasons", this.courseId);
    },
    imageFullPath(image) {
      return `${window.origin}/${image}`;
    },
    openChapterPlayer(chapter, manager = false) {
      const  url = this.getChapterUrl(chapter, manager);

      if (!url) return;

      const title = chapter.name;
      this.$refs.playerGames.openPlayer({ url, title });
    },
    getChapterUrl(chapter, manager) {
      const type = chapter.typeId;
      const mappings = {
        9: "/video/",
        24: "/ppt/",
        2: "/contents/",
        1: "/scorm/",
        8: "/pdf/",
        25: "/lti/0/",
      };
      if (this.specialTypes.includes(type)) {
        return manager ? chapter.projectEditUrl : chapter.projectViewUrl;
      }
      if (mappings[type]) {
        return `${mappings[type]}${chapter.id}`;
      }
      return `/games/?id=${chapter.id}`;
    },
    async deleteChapter(id) {
      try {
        await this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          async () => {
            await this.$store.dispatch("coursesModule/deleteChapter", id);
            await this.fetchChapters();
            this.$toast.success(this.$t("DELETE_SUCCESS"));
          },
          () => {}
        );
      } catch (e) {
        this.$toast.error(this.$t("DELETE_FAILED"));
      }
    },
    dragStart(index) {
      if(this.selectedSeason !==""){
        this.initDrag = index;
      }else{
        this.$toast.error(this.$t("COURSE.CHAPTER.DRAGDROP_ERROR"));
      }

    },
    drop(index) {
      if(this.selectedSeason !==""){
        this.finishDrag = index
        this.$store.dispatch("coursesModule/sortChapter", [this.initDrag, this.finishDrag, this.courseId])
          .then((response) => { if (response?.length) this.$toast.success(this.$t(response)); })
      }
    },
  },
};
</script>

<style scoped lang="scss">
.course-chapters {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem 0.75rem;
}
</style>
