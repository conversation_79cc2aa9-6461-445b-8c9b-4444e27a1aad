<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\PurchasableItemEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\SubscriptionFixtureTrait;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PutPurchasableItemControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use CourseHelperTrait;
    use SubscriptionFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        $this->truncateEntities([
            'purchasable_item',
            'course',
        ]);
        parent::tearDown();
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function testCreatePurchasableItemWithCourseSuccessfully(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Test Course',
            description: 'Test Course Description'
        );

        $token = $this->loginAndGetToken();

        $payload = [
            'type' => 'course',
            'resource_id' => $course->getId(),
            'price_amount' => 5000, // 50.00 EUR in cents
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testCreatePurchasableItemWithSubscriptionSuccessfully(): void
    {
        $subscription = $this->setAndGetSubscriptionInRepository(
            name: 'Premium Subscription',
            description: 'Premium subscription with full access'
        );

        $token = $this->loginAndGetToken(
        );

        $payload = [
            'type' => 'subscription',
            'resource_id' => $subscription->getId()->value(),
            'price_amount' => 9900, // 99.00 EUR in cents
            'price_currency' => 'USD',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function testCreatePurchasableItemWithZeroPriceSuccessfully(): void
    {
        $course = $this->createAndGetCourse(
            name: 'Free Course',
            description: 'Free course for everyone'
        );

        $token = $this->loginAndGetToken();

        $payload = [
            'type' => 'course',
            'resource_id' => $course->getId(),
            'price_amount' => 0, // Free course
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testUnauthorizedAccess(): void
    {
        $payload = [
            'type' => 'course',
            'resource_id' => 1,
            'price_amount' => 1000,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testResourceNotFound(): void
    {
        $token = $this->loginAndGetToken();

        $payload = [
            'type' => 'course',
            'resource_id' => 999999, // Non-existent course ID
            'price_amount' => 1000,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('Resource of type Course with ID 999999 not found', $responseData['message']);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[DataProvider('priceInCentsDataProvider')]
    public function testPricesAreInCents(int $priceAmount, string $expectedDescription): void
    {
        $course = $this->createAndGetCourse(
            name: 'Price Test Course',
            description: 'Course for testing price handling'
        );

        $token = $this->loginAndGetToken(
        );

        $payload = [
            'type' => 'course',
            'resource_id' => $course->getId(),
            'price_amount' => $priceAmount,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode(), $expectedDescription);
    }

    public static function priceInCentsDataProvider(): \Generator
    {
        yield 'price 1 cent' => [
            'priceAmount' => 1,
            'expectedDescription' => 'Should accept 1 cent (0.01 EUR)',
        ];

        yield 'price 50 euros in cents' => [
            'priceAmount' => 5000,
            'expectedDescription' => 'Should accept 5000 cents (50.00 EUR)',
        ];

        yield 'price 999.99 euros in cents' => [
            'priceAmount' => 99999,
            'expectedDescription' => 'Should accept 99999 cents (999.99 EUR)',
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testAccessDeniedForManagersOrCreators(): void
    {
        $user = $this->createAndGetUser(
            firstName: 'Regular',
            lastName: 'User',
            roles: [User::ROLE_USER, User::ROLE_MANAGER, User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user->getId();

        $userToken = $this->loginAndGetTokenForUser($user);
        $course = $this->createAndGetCourse();

        $payload = [
            'type' => 'course',
            'resource_id' => $course->getId(),
            'price_amount' => 5000,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testWithoutCurrencyCode(): void
    {
        $token = $this->loginAndGetToken();
        $course = $this->createAndGetCourse();

        $payload = [
            'type' => 'course',
            'resource_id' => $course->getId(),
            'price_amount' => 1000,
        ];
        $response = $this->makeRequest(
            method: 'PUT',
            uri: PurchasableItemEndpoints::putPurchasableItemEndpoint(),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
    }
}
