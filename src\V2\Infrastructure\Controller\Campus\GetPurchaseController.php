<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Campus;

use App\V2\Application\Query\GetPurchase;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Purchase\PurchaseTransformer;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetPurchaseController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUuidException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(string $purchaseId, Request $request): Response
    {
        UuidValidator::validateUuid($purchaseId);

        $user = RequestAttributeExtractor::extractUser($request);

        $purchase = $this->ask(
            new GetPurchase(
                purchaseId: new Uuid($purchaseId),
                purchaseOwner: $user,
                withItems: true,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                PurchaseTransformer::fromPurchaseToArray($purchase)
            )->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
