<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared\Resource;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Shared\Resource\ResourceTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ResourceTransformerTest extends TestCase
{
    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('validPayloadProvider')]
    public function testFromPayloadWithValidInputs(
        array $payload,
        ResourceType $expectedType,
        string $expectedIdentifierClass,
    ): void {
        $resource = ResourceTransformer::fromPayload($payload);

        $this->assertInstanceOf(Resource::class, $resource);
        $this->assertSame($expectedType, $resource->getType());
        $this->assertInstanceOf($expectedIdentifierClass, $resource->getId());
    }

    public function testFromPayloadMethodExists(): void
    {
        $this->assertTrue(method_exists(ResourceTransformer::class, 'fromPayload'));
        $this->assertTrue(\is_callable([ResourceTransformer::class, 'fromPayload']));
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('returnValueConsistencyProvider')]
    public function testFromPayloadReturnValueConsistency(array $payload): void
    {
        $resource1 = ResourceTransformer::fromPayload($payload);
        $resource2 = ResourceTransformer::fromPayload($payload);

        $this->assertInstanceOf(Resource::class, $resource1);
        $this->assertInstanceOf(Resource::class, $resource2);
        $this->assertTrue($resource1->equals($resource2));
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('dataTransformationProvider')]
    public function testDataTransformationAccuracy(
        array $payload,
        ResourceType $expectedType,
        mixed $expectedIdValue,
    ): void {
        $resource = ResourceTransformer::fromPayload($payload);

        $this->assertSame($expectedType, $resource->getType());
        $this->assertSame($expectedIdValue, $resource->getId()->value());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testCourseResourceTypeHandling(): void
    {
        $payload = [
            'type' => 'course',
            'resource_id' => '123',
        ];

        $resource = ResourceTransformer::fromPayload($payload);

        $this->assertSame(ResourceType::Course, $resource->getType());
        $this->assertInstanceOf(Id::class, $resource->getId());
        $this->assertSame(123, $resource->getId()->value());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testSubscriptionResourceTypeHandling(): void
    {
        $uuid = UuidMother::create();
        $payload = [
            'type' => 'subscription',
            'resource_id' => $uuid->value(),
        ];

        $resource = ResourceTransformer::fromPayload($payload);

        $this->assertSame(ResourceType::Subscription, $resource->getType());
        $this->assertInstanceOf(Uuid::class, $resource->getId());
        $this->assertSame($uuid->value(), $resource->getId()->value());
    }

    public function testSubscriptionWithInvalidUuid(): void
    {
        $this->expectException(InvalidUuidException::class);

        $payload = [
            'type' => 'subscription',
            'resource_id' => 'invalid-uuid',
        ];

        ResourceTransformer::fromPayload($payload);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testInvalidResourceType(): void
    {
        $this->expectException(\UnhandledMatchError::class);

        $payload = [
            'type' => 'invalid_type',
            'resource_id' => '123',
        ];

        ResourceTransformer::fromPayload($payload);
    }

    /**
     * @throws InvalidUuidException
     */
    public static function validPayloadProvider(): \Generator
    {
        yield 'course with integer ID' => [
            'payload' => [
                'type' => 'course',
                'resource_id' => '123',
            ],
            'expectedType' => ResourceType::Course,
            'expectedIdentifierClass' => Id::class,
        ];

        yield 'subscription with valid UUID' => [
            'payload' => [
                'type' => 'subscription',
                'resource_id' => UuidMother::create()->value(),
            ],
            'expectedType' => ResourceType::Subscription,
            'expectedIdentifierClass' => Uuid::class,
        ];
    }

    /**
     * Data provider for return value consistency tests.
     *
     * @throws InvalidUuidException
     */
    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'course consistency' => [
            'payload' => [
                'type' => 'course',
                'resource_id' => '123',
            ],
        ];

        yield 'subscription consistency' => [
            'payload' => [
                'type' => 'subscription',
                'resource_id' => UuidMother::create()->value(),
            ],
        ];
    }

    /**
     * Data provider for invalid payload tests.
     */
    public static function invalidPayloadProvider(): \Generator
    {
        yield 'empty payload' => [
            'payload' => [],
            'expectedException' => \TypeError::class,
        ];

        yield 'null payload type' => [
            'payload' => [
                'type' => null,
                'resource_id' => '123',
            ],
            'expectedException' => \TypeError::class,
        ];

        yield 'invalid resource type' => [
            'payload' => [
                'type' => 'invalid_type',
                'resource_id' => '123',
            ],
            'expectedException' => \UnhandledMatchError::class,
        ];

        yield 'invalid UUID for subscription' => [
            'payload' => [
                'type' => 'subscription',
                'resource_id' => 'not-a-uuid',
            ],
            'expectedException' => InvalidUuidException::class,
        ];
    }

    /**
     * Data provider for data transformation accuracy tests.
     *
     * @throws InvalidUuidException
     */
    public static function dataTransformationProvider(): \Generator
    {
        $uuid = UuidMother::create();

        yield 'course transformation' => [
            'payload' => [
                'type' => 'course',
                'resource_id' => '456',
            ],
            'expectedType' => ResourceType::Course,
            'expectedIdValue' => 456,
        ];

        yield 'subscription transformation' => [
            'payload' => [
                'type' => 'subscription',
                'resource_id' => $uuid->value(),
            ],
            'expectedType' => ResourceType::Subscription,
            'expectedIdValue' => $uuid->value(),
        ];
    }
}
