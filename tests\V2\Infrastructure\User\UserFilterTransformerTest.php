<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\User;

use App\Tests\V2\Mother\User\FilterMother;
use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Infrastructure\User\UserFilterTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UserFilterTransformerTest extends TestCase
{
    #[DataProvider('provideToArray')]
    public function testToArray(UserFilter $userFilter, array $expectedResult): void
    {
        $this->assertEquals($expectedResult, UserFilterTransformer::toArray($userFilter));
    }

    public static function provideToArray(): \Generator
    {
        yield 'only id' => [
            'userFilter' => UserFilterMother::create(
                userId: new Id(1),
                filterId: new Id(2)
            ),
            'expectedResult' => [
                'id' => 2,
            ],
        ];

        yield 'with hydrated filter' => [
            'userFilter' => UserFilterMother::create(
                userId: new Id(1),
                filterId: new Id(2)
            )->setFilter(
                FilterMother::create(
                    id: new Id(2),
                    name: 'Filter 2',
                    categoryId: new Id(1),
                )
            ),
            'expectedResult' => [
                'id' => 2,
                'name' => 'Filter 2',
                'category_id' => 1,
            ],
        ];
    }

    #[DataProvider('provideFromCollectionToArray')]
    public function testFromCollectionToArray(UserFilterCollection $collection, array $expectedResult): void
    {
        $this->assertEquals($expectedResult, UserFilterTransformer::fromCollectionToArray($collection));
    }

    public static function provideFromCollectionToArray(): \Generator
    {
        yield 'without hydrated filter' => [
            'collection' => new UserFilterCollection([
                UserFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(1)
                ),
                UserFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(2)
                ),
                UserFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(3)
                ),
            ]),
            'expectedResult' => [
                ['id' => 1],
                ['id' => 2],
                ['id' => 3],
            ],
        ];

        yield 'one element in collection' => [
            'collection' => new UserFilterCollection([
                UserFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(1)
                )->setFilter(
                    FilterMother::create(
                        id: new Id(1),
                        name: 'Filter 1',
                        categoryId: new Id(1),
                    )
                ),
                UserFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(2)
                )->setFilter(
                    FilterMother::create(
                        id: new Id(2),
                        name: 'Filter 2',
                        categoryId: new Id(1),
                    )
                ),
            ]),
            'expectedResult' => [
                [
                    'id' => 1,
                    'name' => 'Filter 1',
                    'category_id' => 1,
                ],
                [
                    'id' => 2,
                    'name' => 'Filter 2',
                    'category_id' => 1,
                ],
            ],
        ];
    }
}
