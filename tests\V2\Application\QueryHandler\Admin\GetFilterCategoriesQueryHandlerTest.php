<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Tests\V2\Mother\Filter\FilterCategoryMother;
use App\V2\Application\Query\Admin\GetFilterCategoriesQuery;
use App\V2\Application\QueryHandler\Admin\GetFilterCategoriesQueryHandler;
use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCollection;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class GetFilterCategoriesQueryHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?FilterCategoryRepository $filterCategoryRepository = null,
    ): GetFilterCategoriesQueryHandler {
        return new GetFilterCategoriesQueryHandler(
            filterCategoryRepository: $filterCategoryRepository ?? $this->createMock(FilterCategoryRepository::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws FilterCategoryNotFoundException
     * @throws CollectionException
     */
    public function testHandler(): void
    {
        $collection = new FilterCategoryCollection([FilterCategoryMother::create(id: new Id(1))]);
        $filterCategoryRepository = $this->createMock(FilterCategoryRepository::class);
        $filterCategoryRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($collection);

        $handler = $this->getHandler(filterCategoryRepository: $filterCategoryRepository);
        $result = $handler->handle(
            new GetFilterCategoriesQuery(
                criteria: FilterCategoryCriteria::createEmpty()->filterByName('test')
            )
        );
        $this->assertCount(1, $result);
        $this->assertEquals($collection, $result);
    }
}
