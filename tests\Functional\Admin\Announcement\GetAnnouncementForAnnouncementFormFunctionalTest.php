<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementGroupEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;

class GetAnnouncementForAnnouncementFormFunctionalTest extends FunctionalTestCase
{
    /**
     * @throws ORMException
     * @throws \DateMalformedStringException
     * @throws OptimisticLockException
     */
    public function testVirtualMeetingDataIsReceived()
    {
        $token = $this->loginAndGetToken();

        $announcementStartAt = (new \DateTimeImmutable('tomorrow'))->setTime(10, 0, 0);
        $announcementFinishAt = $announcementStartAt->modify('+2 day');

        $course = $this->createAndGetCourse(
            typeCourse: $this->getTypeCourse(
                code: 'virtual_classroom',
            ),
        );

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $announcementStartAt,
            finishAt: $announcementFinishAt,
        );

        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        $this->createAndGetAnnouncementUser(
            announcement: $announcement,
            user: $this->getDefaultUser(),
            announcementGroup: $announcementGroup,
        );

        $timeZone = new \DateTimeZone('Europe/Madrid');
        $sessionStartAt = $announcementStartAt->modify('+1 hour')->setTimezone($timeZone);
        $sessionFinishAt = $sessionStartAt->modify('+1 hour')->setTimezone($timeZone);

        $sessionData = [
            'id' => -1,
            'startAt' => $sessionStartAt->format('Y-m-d H:i:s'),
            'finishAt' => $sessionFinishAt->format('Y-m-d H:i:s'),
            'timezone' => 'Europe/Madrid',
            'url' => '',
            'type' => 'VIRTUAL',
            'providerId' => '',
            'session_number' => 1,
            'virtual_meeting_type' => 'fixed',
            'virtual_meeting_url' => 'https://meet.google.com/test-url',
        ];

        $payload = [
            'id' => $announcement->getId(),
            'data' => [
                [
                    'code' => null,
                    'companyCif' => null,
                    'companyProfile' => null,
                    'cost' => '0.00',
                    'denomination' => null,
                    'fileNumber' => null,
                    'id' => $announcementGroup->getId(),
                    'numberOfSessions' => null,
                    'place' => null,
                    'typeMoney' => null,
                    'sessions' => [$sessionData],
                ],
            ],
        ];

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementGroupEndpoints::postAnnouncementGroupEndpoint(),
            body: $payload,
            bearerToken: $token,
        );
        $this->assertEquals(200, $response->getStatusCode());

        // Get announcement to get group sessionId
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::announcementFormAnnouncementEndpoint($announcement->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(200, $response->getStatusCode());

        $data = $this->extractResponseData($response);
        $this->assertCount(1, $data['students'][0]['sessions']);
        $sessionResponseData = $data['students'][0]['sessions'][0];

        $this->assertArrayHasKey('virtual_meeting', $sessionResponseData);
        $this->assertIsArray($sessionResponseData['virtual_meeting']);
        $this->assertArrayHasKey('url', $sessionResponseData['virtual_meeting']);
        $this->assertEquals('https://meet.google.com/test-url', $sessionResponseData['virtual_meeting']['url']);
        $this->assertArrayHasKey('type', $sessionResponseData['virtual_meeting']);
        $this->assertEquals('fixed', $sessionResponseData['virtual_meeting']['type']);
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
            CourseCategory::class,
            AnnouncementGroup::class,
            AnnouncementGroupSession::class,
        ]);
        parent::tearDown();
    }
}
