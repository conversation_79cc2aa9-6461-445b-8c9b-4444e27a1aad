<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\VirtualMeeting;

use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use App\V2\Infrastructure\Shared\DateTimeTransformer;
use App\V2\Infrastructure\VirtualMeeting\VirtualMeetingDTOTransformer;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class VirtualMeetingDTOTransformerTest extends TestCase
{
    /**
     * Test that fromPayload correctly transforms an array to a VirtualMeetingDTO object.
     */
    #[DataProvider('payloadProvider')]
    public function testFromPayload(array $payload, VirtualMeetingDTO $expectedDTO): void
    {
        // Act
        $result = VirtualMeetingDTOTransformer::fromPayload($payload);

        // Assert
        $this->assertInstanceOf(VirtualMeetingDTO::class, $result);

        // Compare type
        $this->assertEquals($expectedDTO->getType(), $result->getType());

        // Compare dates
        $this->assertDateEquals($expectedDTO->getStartAt(), $result->getStartAt());
        $this->assertDateEquals($expectedDTO->getFinishAt(), $result->getFinishAt());

        // Compare URL
        $this->assertEquals($expectedDTO->getUrl(), $result->getUrl());
    }

    /**
     * Helper method to compare two DateTimeImmutable objects.
     */
    private function assertDateEquals(\DateTimeImmutable $expected, \DateTimeImmutable $actual): void
    {
        $this->assertEquals(
            $expected->format(DateTimeTransformer::DATE_TIME_FORMAT),
            $actual->format(DateTimeTransformer::DATE_TIME_FORMAT)
        );
    }

    /**
     * Data provider for testFromPayload.
     */
    public static function payloadProvider(): \Generator
    {
        yield 'fixed_type_with_url' => [
            'payload' => [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            'expectedDTO' => new VirtualMeetingDTO(
                type: VirtualMeetingType::Fixed,
                startAt: new \DateTimeImmutable('2025-08-10 10:00:00'),
                finishAt: new \DateTimeImmutable('2025-08-10 11:00:00'),
                url: 'https://example.com/meeting',
            ),
        ];

        yield 'fixed_type_without_url' => [
            'payload' => [
                'type' => 'fixed',
                'start_at' => '2025-08-15 14:30:00',
                'finish_at' => '2025-08-15 16:00:00',
            ],
            'expectedDTO' => new VirtualMeetingDTO(
                type: VirtualMeetingType::Fixed,
                startAt: new \DateTimeImmutable('2025-08-15 14:30:00'),
                finishAt: new \DateTimeImmutable('2025-08-15 16:00:00'),
                url: null,
            ),
        ];

        yield 'uppercase_type' => [
            'payload' => [
                'type' => 'FIXED',
                'url' => 'https://example.com/meeting2',
                'start_at' => '2025-09-01 09:00:00',
                'finish_at' => '2025-09-01 10:30:00',
            ],
            'expectedDTO' => new VirtualMeetingDTO(
                type: VirtualMeetingType::Fixed,
                startAt: new \DateTimeImmutable('2025-09-01 09:00:00'),
                finishAt: new \DateTimeImmutable('2025-09-01 10:30:00'),
                url: 'https://example.com/meeting2',
            ),
        ];
    }

    public function testPayloadWithInvalidType(): void
    {
        $payload = [
            'type' => 'invalid_type',
            'url' => 'https://example.com/meeting3',
            'start_at' => '2025-09-05 12:00:00',
            'finish_at' => '2025-09-05 13:30:00',
        ];

        $this->expectExceptionObject(
            new \InvalidArgumentException('Invalid virtual meeting type: invalid_type')
        );

        VirtualMeetingDTOTransformer::fromPayload($payload);
    }
}
