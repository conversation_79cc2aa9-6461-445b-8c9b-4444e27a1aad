<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\User;

use App\Tests\V2\Mother\User\FilterMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Infrastructure\User\ManagerFilterTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ManagerFilterTransformerTest extends TestCase
{
    #[DataProvider('provideToArray')]
    public function testToArray(ManagerFilter $managerFilter, array $expected): void
    {
        $this->assertEquals(
            $expected,
            ManagerFilterTransformer::toArray($managerFilter),
        );
    }

    #[DataProvider('provideFromCollectionToArray')]
    public function testFromCollectionToArray(ManagerFilterCollection $collection, array $expected): void
    {
        $this->assertEquals(
            $expected,
            ManagerFilterTransformer::fromCollectionToArray($collection),
        );
    }

    public static function provideToArray(): \Generator
    {
        yield 'only id' => [
            'managerFilter' => ManagerFilterMother::create(
                userId: new Id(1),
                filterId: new Id(2),
            ),
            'expected' => [
                'id' => 2,
            ],
        ];

        yield 'with hydrated filter' => [
            'managerFilter' => ManagerFilterMother::create(
                userId: new Id(1),
                filterId: new Id(2),
            )->setFilter(
                FilterMother::create(
                    id: new Id(2),
                    name: 'Filter 2',
                    categoryId: new Id(1),
                )
            ),
            'expected' => [
                'id' => 2,
                'name' => 'Filter 2',
                'category_id' => 1,
            ],
        ];
    }

    /**
     * @throws CollectionException
     */
    public static function provideFromCollectionToArray(): \Generator
    {
        yield 'only id' => [
            'collection' => new ManagerFilterCollection([
                ManagerFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(2),
                ),
            ]),
            'expected' => [
                [
                    'id' => 2,
                ],
            ],
        ];

        yield 'with hydrated filter' => [
            'collection' => new ManagerFilterCollection([
                ManagerFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(2),
                )->setFilter(
                    FilterMother::create(
                        id: new Id(2),
                        name: 'Filter 2',
                        categoryId: new Id(1),
                    )
                ),
            ]),
            'expected' => [
                [
                    'id' => 2,
                    'name' => 'Filter 2',
                    'category_id' => 1,
                ],
            ],
        ];
    }
}
