<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\VirtualMeetingValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class VirtualMeetingValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        VirtualMeetingValidator::validateVirtualMeetingRequest($payload);
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Valid fixed type with URL' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
        ];

        yield 'Valid with longer duration' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting/longer',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 12:30:00',
            ],
        ];

        yield 'Valid with different days' => [
            [
                'type' => 'fixed',
                'url' => 'https://teams.microsoft.com/meeting/123',
                'start_at' => '2025-08-10 18:00:00',
                'finish_at' => '2025-08-11 09:00:00',
            ],
        ];
    }

    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            VirtualMeetingValidator::validateVirtualMeetingRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Empty body' => [
            [],
            [
                '' => 'Body cannot be empty',
                '[type]' => 'This field is missing.',
                '[start_at]' => 'This field is missing.',
                '[finish_at]' => 'This field is missing.',
            ],
        ];

        yield 'Missing type field' => [
            [
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[type]' => 'This field is missing.',
            ],
        ];

        yield 'Missing url field for fixed type' => [
            [
                'type' => 'fixed',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[type]' => 'URL is required for FIXED type virtual meetings',
            ],
        ];

        yield 'Missing start_at field' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[start_at]' => 'This field is missing.',
            ],
        ];

        yield 'Missing finish_at field' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
            ],
            [
                '[finish_at]' => 'This field is missing.',
            ],
        ];

        yield 'Empty type field' => [
            [
                'type' => '',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[type]' => [
                    'This value should not be blank.',
                    'Type must be one of the valid values from VirtualMeetingType enum.',
                ],
            ],
        ];

        yield 'Invalid type - not string' => [
            [
                'type' => 123,
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[type]' => [
                    'This value should be of type string.',
                    'Type must be one of the valid values from VirtualMeetingType enum.',
                ],
            ],
        ];

        yield 'Invalid type - wrong choice' => [
            [
                'type' => 'invalid_type',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[type]' => 'Type must be one of the valid values from VirtualMeetingType enum.',
            ],
        ];

        yield 'Invalid URL format' => [
            [
                'type' => 'fixed',
                'url' => 'invalid-url',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[url]' => 'This value is not a valid URL.',
            ],
        ];

        yield 'Empty start_at field' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[start_at]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'Invalid start_at format' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => 'invalid-date',
                'finish_at' => '2025-08-10 11:00:00',
            ],
            [
                '[start_at]' => 'This value is not a valid datetime.',
            ],
        ];

        yield 'Empty finish_at field' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '',
            ],
            [
                '[finish_at]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'Invalid finish_at format' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => 'invalid-date',
            ],
            [
                '[finish_at]' => 'This value is not a valid datetime.',
            ],
        ];

        yield 'finish_at before start_at' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 11:00:00',
                'finish_at' => '2025-08-10 10:00:00',
            ],
            [
                '[finish_at]' => 'Finish date must be after start date',
            ],
        ];

        yield 'finish_at equal to start_at' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 10:00:00',
            ],
            [
                '[finish_at]' => 'Finish date must be after start date',
            ],
        ];

        yield 'Multiple validation errors' => [
            [
                'type' => 'invalid_type',
                'url' => 'invalid-url',
                'start_at' => '2025-08-10 11:00:00',
                'finish_at' => '2025-08-10 10:00:00',
            ],
            [
                '[type]' => 'Type must be one of the valid values from VirtualMeetingType enum.',
                '[url]' => 'This value is not a valid URL.',
                '[finish_at]' => 'Finish date must be after start date',
            ],
        ];

        yield 'Extra unexpected field' => [
            [
                'type' => 'fixed',
                'url' => 'https://example.com/meeting',
                'start_at' => '2025-08-10 10:00:00',
                'finish_at' => '2025-08-10 11:00:00',
                'unexpected_field' => 'value',
            ],
            [
                '[unexpected_field]' => 'This field was not expected.',
            ],
        ];
    }
}
