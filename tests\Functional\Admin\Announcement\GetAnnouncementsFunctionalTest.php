<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementManager;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\SettingsConstants;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetAnnouncementsFunctionalTest extends FunctionalTestCase
{
    private ?User $user = null;
    private ?User $userManager = null;

    private const string START_AT = '2025-01-01 00:00:00';
    private const string FINISH_AT = '2025-01-31 00:00:00';

    private const string EMAIL_MANAGER = '<EMAIL>';

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();

        $this->user = $this->getDefaultUser();
        $this->userManager = $this->createAndGetUser(
            firstName: 'manager',
            lastName: 'manager',
            roles: ['ROLE_MANAGER'],
            email: self::EMAIL_MANAGER,
        );
    }

    /**
     * Tests access control for announcements based on user roles,
     * verifying that the number of visible announcements matches expectations.
     *
     * @dataProvider provideUserRoleCanSeeExpectedAnnouncements
     *
     * @throws \DateMalformedStringException
     * @throws ORMException
     */
    public function testUserRoleCanSeeExpectedAnnouncements(
        array $roles,
        array $tutorAnnouncements,
        int $expectedCount,
        array $expectedItems
    ): void {
        $timezone = new \DateTimeZone('Europe/Madrid');

        $course = $this->createAndGetCourse(
            name: 'Course 1',
        );

        $announcement1 = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, $timezone),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, $timezone),
            subsidized: true,
            code: 'announcement-1',
            createdBy: $this->user,
        );

        $announcement2 = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, $timezone),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, $timezone),
            subsidized: true,
            code: 'announcement-2',
            createdBy: $this->userManager,
        );

        $announcement3 = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, $timezone),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, $timezone),
            subsidized: true,
            code: 'announcement-3',
            createdBy: $this->userManager,
        );

        $this->createAndGetAnnouncementGroup(
            announcement: $announcement1,
        );

        $this->createAndGetAnnouncementGroup(
            announcement: $announcement2,
        );

        $this->createAndGetAnnouncementGroup(
            announcement: $announcement3,
        );

        $em = $this->getEntityManager();
        $em->refresh($this->userManager);
        $this->userManager->setRoles($roles);

        if (!empty($tutorAnnouncements)) {
            foreach ($tutorAnnouncements as $tutorAnnouncement) {
                $announcement = $em->getRepository(Announcement::class)->find($tutorAnnouncement['announcementId']);
                if (!$announcement) {
                    $this->fail('Announcement not found: ' . $tutorAnnouncement['announcementId']);
                }
                $group = $em->getRepository(AnnouncementGroup::class)->find($tutorAnnouncement['groupId']);
                if (!$group) {
                    $this->fail('Announcement group not found: ' . $tutorAnnouncement['groupId']);
                }

                $this->createAndGetAnnouncementTutor(
                    announcement: $announcement,
                    announcementGroup: $group,
                    tutor: $this->userManager,
                );
            }
        }

        $em->persist($this->userManager);
        $em->flush();

        $userToken = $this->loginAndGetToken(
            email: self::EMAIL_MANAGER,
        );

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementsEndpoint(),
            bearerToken: $userToken
        );
        $this->assertEquals(200, $response->getStatusCode());

        $data = $this->extractResponseData($response);

        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total-items', $data);

        $this->assertCount($expectedCount, $data['items']);
        $this->assertEquals($expectedCount, $data['total-items']);
        $this->assertEquals($expectedItems, $data['items']);
    }

    public static function provideUserRoleCanSeeExpectedAnnouncements(): \Generator
    {
        $timezone = new \DateTimeZone('Europe/Madrid');

        $startAt = (new \DateTimeImmutable(self::START_AT, $timezone))->format('c');

        $finishAt = (new \DateTimeImmutable(self::FINISH_AT, $timezone))->format('c');

        yield 'Admin user can see all announcements regardless of authorship or tutoring' => [
            'roles' => ['ROLE_ADMIN'],
            'tutorAnnouncements' => [],
            'expectedCount' => 3,
            'expectedItems' => [
                [
                    'id' => 3,
                    'announcementCode' => 'announcement-3',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
                [
                    'id' => 2,
                    'announcementCode' => 'announcement-2',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
                [
                    'id' => 1,
                    'announcementCode' => 'announcement-1',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
            ],
        ];

        yield 'Manager user can see announcements they have created' => [
            'roles' => ['ROLE_MANAGER'],
            'tutorAnnouncements' => [],
            'expectedCount' => 2,
            'expectedItems' => [
                [
                    'id' => 3,
                    'announcementCode' => 'announcement-3',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
                [
                    'id' => 2,
                    'announcementCode' => 'announcement-2',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
            ],
        ];

        yield 'User with both Manager and Tutor roles can see announcements they created and those they tutor' => [
            'roles' => ['ROLE_MANAGER', 'ROLE_TUTOR'],
            'tutorAnnouncements' => [],
            'expectedCount' => 2,
            'expectedItems' => [
                [
                    'id' => 3,
                    'announcementCode' => 'announcement-3',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
                [
                    'id' => 2,
                    'announcementCode' => 'announcement-2',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
            ],
        ];

        yield 'Tutor user can see announcements where they are assigned as tutor' => [
            'roles' => ['ROLE_TUTOR'],
            'tutorAnnouncements' => [
                [
                    'announcementId' => 1,
                    'groupId' => 1,
                ],
            ],
            'expectedCount' => 1,
            'expectedItems' => [
                [
                    'id' => 1,
                    'announcementCode' => 'announcement-1',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
            ],
        ];

        yield 'Creator role does not have access to any announcements' => [
            'roles' => ['ROLE_CREATOR'],
            'tutorAnnouncements' => [],
            'expectedCount' => 0,
            'expectedItems' => [],
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws \DateMalformedStringException
     */
    #[DataProvider('providerAnnouncementsVisibilityWithSettingManagerSharing')]
    public function testAnnouncementsVisibilityWithSettingManagerSharing(
        bool $sharingEnabled,
        int $expectedCount,
        array $expectedItems
    ): void {
        $course = $this->createAndGetCourse(
            name: 'Course 1'
        );

        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: $sharingEnabled ? 'true' : 'false',
        );

        $announcement1 = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, new \DateTimeZone('Europe/Madrid')),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, new \DateTimeZone('Europe/Madrid')),
            code: 'announcement-1',
            createdBy: $this->user,
        );

        $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, new \DateTimeZone('Europe/Madrid')),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, new \DateTimeZone('Europe/Madrid')),
            code: 'announcement-2',
            createdBy: $this->userManager,
        );

        $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable(self::START_AT, new \DateTimeZone('Europe/Madrid')),
            finishAt: new \DateTimeImmutable(self::FINISH_AT, new \DateTimeZone('Europe/Madrid')),
            code: 'announcement-3',
            createdBy: $this->user,
        );

        $this->createAndGetAnnouncementManager(
            announcement: $announcement1,
            user: $this->userManager,
        );

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementsEndpoint(),
            bearerToken: $this->loginAndGetTokenForUser($this->userManager)
        );

        $this->assertEquals(200, $response->getStatusCode());

        $data = $this->extractResponseData($response);

        $this->assertEquals($expectedCount, $data['total-items']);

        // Verify that we have the expected number of announcements
        $this->assertCount($expectedCount, $data['items']);
        $this->assertEquals($expectedItems, $data['items']);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public static function providerAnnouncementsVisibilityWithSettingManagerSharing(): \Generator
    {
        $timezone = new \DateTimeZone('Europe/Madrid');

        $startAt = (new \DateTimeImmutable(self::START_AT, $timezone))->format('c');

        $finishAt = (new \DateTimeImmutable(self::FINISH_AT, $timezone))->format('c');

        yield 'When settings announcement managers sharing is enabled' => [
            'sharingEnabled' => true,
            'expectedCount' => 2,
            'expectedItems' => [
                [
                    'id' => 2,
                    'announcementCode' => 'announcement-2',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
                [
                    'id' => 1,
                    'announcementCode' => 'announcement-1',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
            ],
        ];

        yield 'When settings announcement managers sharing is disabled' => [
            'sharingEnabled' => false,
            'expectedCount' => 1,
            'expectedItems' => [
                [
                    'id' => 2,
                    'announcementCode' => 'announcement-2',
                    'startAt' => $startAt,
                    'finishAt' => $finishAt,
                    'courseCode' => 'TestCourseCode-1',
                    'idTypeCourse' => 1,
                    'modality' => 'Teleformación',
                    'denomination' => 'INTERN',
                    'courseName' => 'Course 1',
                    'total' => 0,
                    'image' => null,
                    'status' => 'FINISHED',
                    'timezone' => 'Europe/Madrid',
                    'source' => 'INTERN',
                ],
            ],
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    public function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );

        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            AnnouncementManager::class,
            UserLogin::class,
            UserManage::class,
        ]);

        // Hard delete user created for the test
        if (!empty($this->userManager)) {
            $this->hardDeleteUsersByIds([
                $this->userManager->getId(),
            ]);
        }

        parent::tearDown();
    }
}
