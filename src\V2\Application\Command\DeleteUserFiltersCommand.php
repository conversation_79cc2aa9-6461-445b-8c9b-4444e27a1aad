<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\Entity\User;
use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;

readonly class DeleteUserFiltersCommand implements Command
{
    public function __construct(
        private Id $userId,
        private IdCollection $filterIds,
        private User $requestedBy,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getFilterIds(): IdCollection
    {
        return $this->filterIds;
    }

    public function getRequestedBy(): User
    {
        return $this->requestedBy;
    }
}
