security:
    access_denied_url: /login
    enable_authenticator_manager: true
    password_hashers:
        App\Entity\User:
            algorithm: auto
        App\Entity\AnnouncementInspectorAccess:
            algorithm: auto
    role_hierarchy:
        ROLE_SUPER_ADMIN: [ROLE_ADMIN]
        ROLE_ADMIN: [R<PERSON><PERSON>_USER, ROLE_MANAGER, R<PERSON><PERSON>_ALLOWED_TO_SWITCH]
        ROLE_MANAGER_EDITOR: [ROLE_MANAGER]
    # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
        api_user_provider:
            entity:
                class: App\Entity\User
                property: email
        api_inspector_provider:
            entity:
                class: App\Entity\AnnouncementInspectorAccess
                property: user
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        login:
            pattern: ^/api/login
            stateless: true
            provider: app_user_provider
            # Uncomment code when changing to authenticator
            custom_authenticators:
                - App\Security\ApiLoginAuthenticator

        inspector_login:
            pattern: ^/inspector/login
            stateless: true
            provider: api_inspector_provider

        api:
            pattern:   ^/api(?!/(login|logout))
            stateless: true
            provider: api_user_provider
            entry_point: jwt
            jwt: ~
            refresh_jwt:
                check_path: /api/token/refresh
                provider: api_user_provider

        api_inspector:
            pattern: ^/inspector(?!/(login|logout))
            stateless: true
            provider: api_inspector_provider
            entry_point: jwt
            jwt: ~
            refresh_jwt:
                check_path: /api/token/refresh
                provider: api_inspector_provider

        video:
            pattern:   ^/admin/(createVideoByVimeo|updatePackageVideo)
            stateless: true
            provider: api_user_provider
            jwt: ~
        main:
            login_throttling: true
            remember_me:
                secret:   '%kernel.secret%'
                lifetime: 604800 # 1 week in seconds
                path:     /
                # by default, the feature is enabled by checking a
                # checkbox in the login form (see below), uncomment the
                # following line to always enable it.
                always_remember_me: true
            lazy: true
            provider: app_user_provider
            user_checker: App\Security\UserChecker
            entry_point: App\Security\AppAuthenticator
            custom_authenticators:
                - App\Security\AppAuthenticator
                - App\Security\OAuth2\Factorial\FactorialAuthenticator # Executes only on specific conditions
            logout:
                path: app_logout
                target: app_login
                delete_cookies: ['BEARER']

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#firewalls-authentication

            # https://symfony.com/doc/current/security/impersonating_user.html
            switch_user:
                role: CAN_SWITCH_USER

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/admin/(createVideoByVimeo|updatePackageVideo|images-slider|videoquiz\/create), roles: PUBLIC_ACCESS }
        - { path: ^/admin, roles: [ ROLE_ADMIN,ROLE_SUPER_ADMIN, ROLE_MANAGER, ROLE_SUBSIDIZER, ROLE_TEAM_MANAGER, ROLE_MANAGER_EDITOR, ROLE_TUTOR, ROLE_CREATOR, ROLE_SUPPORT ] }
        - { path: ^/api/login, roles: PUBLIC_ACCESS  }
        - { path: ^/api/logout, roles: PUBLIC_ACCESS  }
        - { path: ^/api/url-shortener/long-url, roles: PUBLIC_ACCESS  }
        - { path: ^/api/registration/extra-fields, roles: PUBLIC_ACCESS }
        - { path: ^/api/user-register , roles: PUBLIC_ACCESS  }
        - { path: ^/api/languages , roles: PUBLIC_ACCESS  }
        - { path: ^/admin/languages , roles: IS_AUTHENTICATED_FULLY  }
        - { path: ^/api/reset-password , roles: PUBLIC_ACCESS  }

        - { path: ^/api/token/refresh, roles: PUBLIC_ACCESS }

        - { path: ^/inspector/login, roles: IS_AUTHENTICATED_ANONYMOUSLY}
        - { path: ^/inspector/logout, roles: IS_AUTHENTICATED_ANONYMOUSLY}
        - { path: ^/inspector/view, roles: IS_AUTHENTICATED_ANONYMOUSLY}
        - { path: ^/inspector, roles: [ROLE_INSPECTOR] }

        - { path: ^/api/v1, roles: PUBLIC_ACCESS }
        - { path: ^/api/v2, roles: PUBLIC_ACCESS }
        - { path: ^/api, roles: IS_AUTHENTICATED_FULLY }
