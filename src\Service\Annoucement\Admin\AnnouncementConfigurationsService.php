<?php

namespace App\Service\Annoucement\Admin;

use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\Announcement;
use App\Entity\SurveyAnnouncement;
use App\Entity\TypeDiploma;
use App\Entity\Task;
use Doctrine\ORM\EntityManagerInterface;

class AnnouncementConfigurationsService
{
    private const TYPES_NOTIFICATIONS = [
        "CHAT" => 3,
        "NOTIFICATION" => 4,
        "MESSAGE" => 5,
        "FORUM" => 6,
    ];

    private $em;

    public function __construct(
        EntityManagerInterface $em
    ) {
        $this->em = $em;
    }

    public function isConfigurationEnabled(Announcement $announcement, int $configurationId): bool
    {
        $configurationType = $this->em
            ->getRepository(AnnouncementConfigurationType::class)
            ->find($configurationId);

        if (!$configurationType) {
            return false;
        }

        if (!$configurationType->isActive()) {
            return false;
        }

        $configuration = $this->em->getRepository(AnnouncementConfiguration::class)->findOneBy([
            'announcement' => $announcement,
            'configuration' => $configurationType
        ]);

        return null !== $configuration;
    }

    public function hasBonification(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE);
    }

    public function hasDiploma(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_CERTIFICATE);
    }

    public function hasIncludeObjectAndContentDiploma(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled(
            $announcement,
            AnnouncementConfigurationType::ID_INCLUDE_OBJ_CONTENT_CERTIFICATE
        );
    }

    public function hasDniInDiploma(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled(
            $announcement,
            AnnouncementConfigurationType::ID_INCLUDE_DNI_IN_CERTIFICATE
        );
    }

    public function hasSurvey(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_SURVEY);
    }

    public function hasAlert(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_ALERTS_TUTOR);
    }

    public function hasTemporalization(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION);
    }

    public function hasAccessContentAfterFinish(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled(
            $announcement,
            AnnouncementConfigurationType::ID_ALLOW_ACTIVE_COURSE_AT_END
        );
    }

    public function hasChat(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_CHAT);
    }

    public function hasNotification(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION);
    }

    public function hasMessage(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_SMS);
    }

    public function hasForum(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_ENABLE_FORUM);
    }

    public function hasDigitalSignature(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_DIGITAL_SIGNATURE);
    }

    private function isConfigurationGlobal(int $idConfiguration): bool
    {
        $configurationType = $this->em->getRepository(AnnouncementConfigurationType::class)->find($idConfiguration);

        return $configurationType &&
            $configurationType->isActive();
    }

    public function hasEmailNotificationOnAnnouncement(): bool
    {
        return $this->isConfigurationGlobal(
            AnnouncementConfigurationType::ID_ENABLE_EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
        );
    }

    public function hasNotificationOnAnnouncement(): bool
    {
        return $this->isConfigurationGlobal(AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION_ON_ANNOUNCEMENT);
    }

    public function hasCost(Announcement $announcement): bool
    {
        return $this->isConfigurationEnabled($announcement, AnnouncementConfigurationType::ID_COST);
    }

    public function getSurveyName(Announcement $announcement)
    {
        if ($this->hasSurvey($announcement)) {
            $surveyAnnouncement = $this->em
                ->getRepository(SurveyAnnouncement::class)
                ->findOneBy(['announcement' => $announcement]);

            if ($surveyAnnouncement) {
                return $surveyAnnouncement->getSurvey()->getName();
            }
        }

        return 'Fundae';
    }

    public function getPreviewDiploma(Announcement $announcement)
    {
        $previewDefault = 'assets_announcement/preview_type_diploma/easylearning.pdf';

        $typeDiplomaAnnouncement = $announcement->getTypeDiploma() ? $announcement->getTypeDiploma()->getId() : 1;
        $typeDiploma = $this->em->getRepository(TypeDiploma::class)->find($typeDiplomaAnnouncement);


        $extra = json_decode($typeDiploma->getExtra(), true);
        if ($extra == null) {
            return $previewDefault;
        }

        return $extra['previewDiploma'] ?? $previewDefault;
    }

    public function getComunications(Announcement $announcement): array
    {
        $comunications = [];
        foreach (self::TYPES_NOTIFICATIONS as $key => $value) {
            $comunications[$key] = $this->isConfigurationEnabled($announcement, $value);
        }

        return $comunications;
    }

    public function hasComunication(Announcement $announcement): bool
    {
        $comunications = $this->getComunications($announcement);
        foreach ($comunications as $value) {
            if ($value) {
                return true;
            }
        }

        return false;
    }

    public function hasEnrollmentTemplate(): bool
    {
        return $this->isConfigurationGlobal(AnnouncementConfigurationType::ID_TEMPLATE_XLSX_IBEROSTAR);
    }

    public function hasExportReportZip(): bool
    {
        return $this->isConfigurationGlobal(AnnouncementConfigurationType::ID_REPORT_ZIP);
    }

    public function hasReportTask(): bool
    {
        $taskRepository = $this->em->getRepository(Task::class);

        $taskExists = $taskRepository->findOneBy(['task' => 'export-stats-file']);

        return $taskExists !== null;
    }


    public function getConfigurationAnnouncement(Announcement $announcement)
    {

        return [
            'hasBonification' => $this->hasBonification($announcement),
            'hasTemporalization' => $this->hasTemporalization($announcement),
            'hasSurvey' => $this->hasSurvey($announcement),
            'comunications'  => $this->getComunications($announcement),
            'hasComunication' => $this->hasComunication($announcement),
            'hasDiploma' => $this->hasDiploma($announcement),
            'hasAlertTutor' => $this->hasAlert($announcement),
            'hasDigitalSignature' => $this->hasDigitalSignature($announcement),
            'hasReportZip' => $this->hasExportReportZip($announcement)
        ];
    }
}
