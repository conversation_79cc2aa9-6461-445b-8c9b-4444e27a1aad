<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetPurchasableItemsQuery;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Purchase\PurchasableItemCriteriaTransformer;
use App\V2\Infrastructure\Purchase\PurchasableItemTransformer;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Admin\GetPurchasableItemsValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetPurchasableItemsController extends QueryBusAccessor
{
    /**
     * @throws InvalidCurrencyCodeException
     * @throws CollectionException
     * @throws ValidatorException
     */
    public function __invoke(Request $request): Response
    {
        $queryParameters = $request->query->all();

        GetPurchasableItemsValidator::validateGetPurchasableItemsRequest($queryParameters);

        $criteria = PurchasableItemCriteriaTransformer::fromArray($queryParameters);

        $purchasableItems = $this->ask(
            new GetPurchasableItemsQuery(criteria: $criteria)
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                PurchasableItemTransformer::fromCollectionToArray($purchasableItems->getCollection())
            )
                ->addPaginationMetadata($purchasableItems, $criteria)
                ->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
