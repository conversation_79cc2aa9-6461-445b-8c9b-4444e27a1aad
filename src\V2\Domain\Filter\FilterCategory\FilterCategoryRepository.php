<?php

declare(strict_types=1);

namespace App\V2\Domain\Filter\FilterCategory;

use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface FilterCategoryRepository
{
    /**
     * @throws FilterCategoryNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(FilterCategoryCriteria $criteria): FilterCategory;

    /**
     * @throws FilterCategoryNotFoundException
     * @throws InfrastructureException
     */
    public function findBy(FilterCategoryCriteria $criteria): FilterCategoryCollection;
}
