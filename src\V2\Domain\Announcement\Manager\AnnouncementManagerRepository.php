<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Manager;

use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface AnnouncementManagerRepository
{
    /**
     * @throws InfrastructureException
     */
    public function insert(AnnouncementManager $announcementManager): void;

    /**
     * @throws AnnouncementManagerNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(AnnouncementManagerCriteria $criteria): AnnouncementManager;

    /**
     * @throws InfrastructureException
     */
    public function findBy(AnnouncementManagerCriteria $criteria): AnnouncementManagerCollection;

    /**
     * @throws InfrastructureException
     */
    public function delete(AnnouncementManager $announcementManager): void;
}
