<?php

declare(strict_types=1);

namespace App\V2\Domain\User\ManagerFilter;

use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;

/**
 * @extends Criteria<ManagerFilterCriteria>
 */
class ManagerFilterCriteria extends Criteria
{
    private ?Id $userId = null;
    private ?Id $filterId = null;
    private ?IdCollection $filterIds = null;

    public function isEmpty(): bool
    {
        return null === $this->userId && null === $this->filterId && null === $this->filterIds;
    }

    public function filterByUserId(Id $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function filterByFilterId(Id $filterId): self
    {
        $this->filterId = $filterId;

        return $this;
    }

    public function filterByFilterIds(IdCollection $filterIds): self
    {
        $this->filterIds = $filterIds;

        return $this;
    }

    public function getUserId(): ?Id
    {
        return $this->userId;
    }

    public function getFilterId(): ?Id
    {
        return $this->filterId;
    }

    public function getFilterIds(): ?IdCollection
    {
        return $this->filterIds;
    }
}
