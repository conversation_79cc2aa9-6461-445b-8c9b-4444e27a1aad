<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter\FilterCategory;

use App\Tests\V2\Domain\Filter\FilterCategory\FilterCategoryRepositoryTestCase;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Infrastructure\Persistence\Filter\FilterCategory\InMemoryFilterCategoryRepository;

class InMemoryFilterCategoryRepositoryTest extends FilterCategoryRepositoryTestCase
{
    private ?InMemoryFilterCategoryRepository $repository = null;

    protected function getRepository(): InMemoryFilterCategoryRepository
    {
        $this->repository = new InMemoryFilterCategoryRepository();

        return $this->repository;
    }

    protected function addFilterCategory(FilterCategory $category): void
    {
        if (null === $this->repository) {
            $this->fail('call getRepository() before adding filter category');
        }
        $this->repository->add($category);
    }
}
