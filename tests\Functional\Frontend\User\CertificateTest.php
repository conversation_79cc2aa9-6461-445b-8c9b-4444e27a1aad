<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\UserCourse;
use App\Tests\Functional\FunctionalTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

class CertificateTest extends FunctionalTestCase
{
    private const TEST_ANNOUNCEMENT_CODE_1 = 'TEST-ANN-1';
    private const TEST_ANNOUNCEMENT_CODE_2 = 'TEST-ANN-2';
    private const TEST_DEBUG_MODE = false;

    protected function setUp(): void
    {
        parent::setUp();
        self::$debugMode = self::TEST_DEBUG_MODE;
        $this->loadAnnouncementData();
    }

    /**
     * @dataProvider certificateRequestProvider
     */
    public function testFetchDiplomasWithDifferentScenarios(
        string $testCase,
        ?string $token,
        int $expectedStatusCode,
        ?bool $expectedError,
        array $expectedDataStructure,
        ?string $locale = 'es'
    ): void {
        $this->setLocaleForUser($locale);

        if ('VALID_TOKEN' === $token) {
            $token = $this->loginAndGetToken();
        }

        $response = $this->makeFrontendApiRequest('GET', '/passport', [], [], [], $token);

        $this->assertSame(
            $expectedStatusCode,
            $response->getStatusCode(),
            "Caso de prueba '$testCase': Se esperaba código $expectedStatusCode, se obtuvo {$response->getStatusCode()}."
        );

        if (Response::HTTP_OK === $expectedStatusCode) {
            $responseData = json_decode($response->getContent(), true);

            $this->assertArrayHasKey('status', $responseData, "La respuesta no contiene la clave 'status'.");
            $this->assertArrayHasKey('error', $responseData, "La respuesta no contiene la clave 'error'.");
            $this->assertArrayHasKey('data', $responseData, "La respuesta no contiene la clave 'data'.");

            if (null !== $expectedError) {
                $this->assertSame($expectedError, $responseData['error'], "El valor 'error' no coincide con lo esperado.");
            }

            if (!empty($expectedDataStructure)) {
                $this->verifyDataStructure($responseData['data'], $expectedDataStructure);
            }
        }
    }

    /**
     * Specific test to verify that only certificates are included
     * for announcements with diplomaEnabled = true.
     */
    public function testFilterCertificatesByDiplomaEnabled(): void
    {
        // Set up an announcement with diploma enabled and another without a diploma
        $this->configureDiplomaSettings();

        $em = $this->getEntityManager();
        $announcement1 = $em->getRepository(Announcement::class)->findOneBy(['code' => self::TEST_ANNOUNCEMENT_CODE_1]);
        $announcement2 = $em->getRepository(Announcement::class)->findOneBy(['code' => self::TEST_ANNOUNCEMENT_CODE_2]);

        $token = $this->loginAndGetToken();

        $response = $this->makeFrontendApiRequest('GET', '/passport', [], [], [], $token);

        $this->assertSame(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $certificates = $responseData['data'];

        // Verify that only certificates with diploma enabled are included
        $foundAnn1 = false;
        $foundAnn2 = false;

        foreach ($certificates as $certificate) {
            if (isset($certificate['announcement']['id'])) {
                $certAnnouncementId = $certificate['announcement']['id'];

                if ($certAnnouncementId === $announcement1->getId()) {
                    $foundAnn1 = true;
                }
                if ($certAnnouncementId === $announcement2->getId()) {
                    $foundAnn2 = true;
                }
            }
        }

        $this->assertTrue($foundAnn1, 'No se encontró el certificado para TEST-ANN-1 con diploma habilitado');
        $this->assertFalse($foundAnn2, 'Se encontró el certificado para TEST-ANN-2 con diploma deshabilitado');
    }

    /**
     * Test that verifies the complete structure of the certificate.
     */
    public function testCertificateStructureComplete(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeFrontendApiRequest('GET', '/passport', [], [], [], $token);

        $this->assertSame(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $certificates = $responseData['data'];
        $this->assertIsArray($certificates, 'Los certificados no son un array');

        if (!empty($certificates)) {
            $firstCertificate = $certificates[0];

            // Verify certificate structure
            $this->assertArrayHasKey('id', $firstCertificate);
            $this->assertArrayHasKey('announcement', $firstCertificate);
            $this->assertArrayHasKey('user', $firstCertificate);
            $this->assertArrayHasKey('course', $firstCertificate);

            // Verify announcement structure
            $this->assertIsArray($firstCertificate['announcement']);
            $this->assertArrayHasKey('id', $firstCertificate['announcement']);

            // Verify course structure
            $this->assertIsArray($firstCertificate['course']);
            $this->assertArrayHasKey('id', $firstCertificate['course']);
            $this->assertArrayHasKey('name', $firstCertificate['course']);

            // Verify date fields
            $this->assertArrayHasKey('startedAt', $firstCertificate);
            $this->assertArrayHasKey('finishedAt', $firstCertificate);
            $this->assertArrayHasKey('startedAtNotFormatted', $firstCertificate);
            $this->assertArrayHasKey('finishedAtNotFormatted', $firstCertificate);
        }
    }

    /**
     * Verifies the structure of the certificates in the response.
     */
    private function verifyDataStructure(array $data, array $expectedKeys): void
    {
        $this->assertIsArray($data, "El campo 'data' no es un array.");

        if (empty($data)) {
            return;  // If there is no data, we do not verify the structure
        }

        $firstCertificate = $data[0];
        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $firstCertificate, "El certificado no contiene la clave '$key'.");
        }
    }

    public static function certificateRequestProvider(): \Generator
    {
        yield 'Sin token' => [
            'testCase' => 'Sin token',
            'token' => null,
            'expectedStatusCode' => Response::HTTP_UNAUTHORIZED,
            'expectedError' => null,
            'expectedDataStructure' => [],
        ];

        yield 'Con token válido' => [
            'testCase' => 'Con token válido',
            'token' => 'VALID_TOKEN',
            'expectedStatusCode' => Response::HTTP_OK,
            'expectedError' => false,
            'expectedDataStructure' => [
                'id', 'notified', 'announcement', 'user', 'course',
                'timeZoneActual', 'startedAt', 'finishedAt',
                'startedAtNotFormatted', 'finishedAtNotFormatted',
            ],
        ];

        yield 'Con token válido (locale inglés)' => [
            'testCase' => 'Con token válido (locale inglés)',
            'token' => 'VALID_TOKEN',
            'expectedStatusCode' => Response::HTTP_OK,
            'expectedError' => false,
            'expectedDataStructure' => [
                'id', 'notified', 'announcement', 'user', 'course',
                'timeZoneActual', 'startedAt', 'finishedAt',
                'startedAtNotFormatted', 'finishedAtNotFormatted',
            ],
            'locale' => 'en',
        ];
    }

    /**
     * Set up settings to enable/disable diplomas.
     */
    private function configureDiplomaSettings(): void
    {
        /** @var EntityManagerInterface $em */
        $em = $this->getEntityManager();

        $announcement1 = $em->getRepository(Announcement::class)->findOneBy(['code' => self::TEST_ANNOUNCEMENT_CODE_1]);
        $announcement2 = $em->getRepository(Announcement::class)->findOneBy(['code' => self::TEST_ANNOUNCEMENT_CODE_2]);

        if (!$announcement1 || !$announcement2) {
            throw new \Exception('No se encontraron los anuncios de prueba');
        }

        $certificateConfig = $em->getRepository(AnnouncementConfigurationType::class)
            ->find(AnnouncementConfigurationType::ID_ENABLE_CERTIFICATE);

        if (!$certificateConfig) {
            throw new \Exception('No se encontró la configuración para certificados');
        }

        // Create new configuration to enable diplomas in announcement 1
        $config1 = new AnnouncementConfiguration();
        $config1->setAnnouncement($announcement1);
        $config1->setConfiguration($certificateConfig);
        $em->persist($config1);

        $em->flush();

        $checkConfig1 = $em->getRepository(AnnouncementConfiguration::class)
            ->findOneBy([
                'announcement' => $announcement1,
                'configuration' => $certificateConfig,
            ]);

        $checkConfig2 = $em->getRepository(AnnouncementConfiguration::class)
            ->findOneBy([
                'announcement' => $announcement2,
                'configuration' => $certificateConfig,
            ]);

        if (!$checkConfig1) {
            throw new \Exception('No se pudo configurar el diploma para TEST-ANN-1');
        }

        if ($checkConfig2) {
            throw new \Exception('No se pudo deshabilitar el diploma para TEST-ANN-2');
        }

        $em->clear();
    }

    private function loadAnnouncementData(): void
    {
        /** @var EntityManagerInterface $em */
        $em = $this->getEntityManager();

        $existingAnnouncements = $em->getRepository(Announcement::class)->findAll();
        if (\count($existingAnnouncements) > 0) {
            return;
        }

        $typeCourse = $this->getTypeCourse();
        $course = $this->createAndGetCourse(
            name: 'Curso de Prueba para Announcement',
            code: 'courseCode-1',
            typeCourse: $typeCourse,
            description: 'Descripción de curso de prueba',
            locale: 'es',
            active: true,
            open: true,
            isNew: true,
            openVisible: true
        );

        $announcement1 = new Announcement();
        $announcement1->setCourse($course)
            ->setStartAt(new \DateTimeImmutable('-2 days'))
            ->setFinishAt(new \DateTimeImmutable('+2 days'))
            ->setStatus(Announcement::STATUS_ACTIVE)
            ->setSubsidized(false)
            ->setCode(self::TEST_ANNOUNCEMENT_CODE_1);

        $announcement2 = new Announcement();
        $announcement2->setCourse($course)
            ->setStartAt(new \DateTimeImmutable('-1 week'))
            ->setFinishAt(new \DateTimeImmutable('+1 week'))
            ->setStatus(Announcement::STATUS_ACTIVE)
            ->setSubsidized(true)
            ->setCode(self::TEST_ANNOUNCEMENT_CODE_2);

        $em->persist($announcement1);
        $em->persist($announcement2);
        $em->flush();

        $defaultUser = $this->getDefaultUser();

        $announcementUser1 = new AnnouncementUser();
        $announcementUser1->setAnnouncement($announcement1);
        $announcementUser1->setUser($defaultUser);
        $announcementUser1->setValuedCourseAt(new \DateTimeImmutable());
        $announcementUser1->setAproved(true);

        $announcementUser2 = new AnnouncementUser();
        $announcementUser2->setAnnouncement($announcement2);
        $announcementUser2->setUser($defaultUser);
        $announcementUser2->setAproved(true);

        $em->persist($announcementUser1);
        $em->persist($announcementUser2);
        $em->flush();

        $userCourse = new UserCourse();
        $userCourse->setUser($defaultUser);
        $userCourse->setCourse($course);
        $userCourse->setStartedAt(new \DateTimeImmutable('-1 month'));
        $userCourse->setFinishedAt(new \DateTimeImmutable('-1 day'));

        $em->persist($userCourse);
        $em->flush();
    }

    protected function tearDown(): void
    {
        $this->clearAnnouncementData();
        parent::tearDown();
    }

    private function clearAnnouncementData(): void
    {
        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            UserCourse::class,
            Course::class,
            AnnouncementConfiguration::class,
        ]);
    }
}
