<?php

declare(strict_types=1);

namespace App\V2\Application\Resource;

use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;

readonly class CompositeResourceProvider implements ResourceProvider
{
    /**
     * @param iterable<ResourceProvider> $providers
     */
    public function __construct(
        private iterable $providers,
    ) {
    }

    public function supports(ResourceType $type): bool
    {
        foreach ($this->providers as $provider) {
            if ($provider->supports($type)) {
                return true;
            }
        }

        return false;
    }

    public function getEntity(Resource $resource): mixed
    {
        foreach ($this->providers as $provider) {
            if ($provider->supports($resource->getType())) {
                return $provider->getEntity($resource);
            }
        }

        throw new ResourceNotFoundException($resource);
    }
}
