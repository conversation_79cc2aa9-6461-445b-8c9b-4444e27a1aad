<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetFilterCategoriesQuery;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Filter\FilterCategory\FilterCategoryCriteriaTransformer;
use App\V2\Infrastructure\Filter\FilterCategory\FilterCategoryTransformer;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Admin\FilterCategoryValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetFilterCategoriesController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     */
    public function __invoke(Request $request): Response
    {
        $params = $request->query->all();

        FilterCategoryValidator::validateGetFilterCategories($params);

        $criteria = FilterCategoryCriteriaTransformer::fromArray($params);

        $collection = $this->ask(
            new GetFilterCategoriesQuery(
                criteria: $criteria
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                FilterCategoryTransformer::fromCollectionToArray($collection)
            )->toArray(),
            status: Response::HTTP_OK
        );
    }
}
