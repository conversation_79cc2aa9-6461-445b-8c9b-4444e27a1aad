<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\Service\SettingsService;
use App\V2\Application\Query\Admin\GetLocalesQuery;

readonly class GetLocalesQueryHandler
{
    private SettingsService $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function handle(GetLocalesQuery $query): array
    {
        return $this->settingsService->get('app.languages.admin', []);
    }
}
