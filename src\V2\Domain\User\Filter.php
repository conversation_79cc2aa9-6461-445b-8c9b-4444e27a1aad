<?php

declare(strict_types=1);

namespace App\V2\Domain\User;

use App\V2\Domain\Shared\Id\Id;

class Filter
{
    public function __construct(
        private readonly Id $id,
        private readonly string $name,
        private readonly Id $categoryId,
    ) {
    }

    public function getId(): Id
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCategoryId(): Id
    {
        return $this->categoryId;
    }
}
