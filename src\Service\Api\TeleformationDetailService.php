<?php

declare(strict_types=1);

namespace App\Service\Api;

use App\Campus\Service\User\TokenExtractorService;
use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Chapter;
use App\Entity\ChatChannel;
use App\Entity\ChatServer;
use App\Entity\CommentTask;
use App\Entity\Course;
use App\Entity\FilesHistoryTask;
use App\Entity\FilesTask;
use App\Entity\HistoryDeliveryTask;
use App\Entity\MaterialCourse;
use App\Entity\TaskCourse;
use App\Entity\TaskUser;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Enum\ChapterContent;
use App\Repository\AnnouncementRepository;
use App\Repository\ChapterRepository;
use App\Repository\ChatChannelRepository;
use App\Repository\ItineraryRepository;
use App\Repository\NpsRepository;
use App\Repository\UserCourseChapterRepository;
use App\Repository\UserCourseRepository;
use App\Service\Annoucement\Admin\AnnouncementAprovedCriteriaService;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementGroupSessionService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Annoucement\Admin\TaskUserService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class TeleformationDetailService extends AbstractBaseService
{
    private User $user;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        private readonly AnnouncementRepository $announcementRepository,
        private readonly UserCourseChapterRepository $userCourseChapterRepository,
        private readonly NpsRepository $npsRepository,
        private readonly ChapterRepository $chapterRepository,
        private readonly UserCourseRepository $userCourseRepository,
        private readonly ItineraryRepository $itineryRepository,
        private readonly RequestStack $requestStack,
        private readonly ApiAnnouncementService $apiAnnouncementService,
        private readonly AnnouncementConfigurationsService $announcementConfigurationsService,
        private readonly AnnouncementAprovedCriteriaService $announcementAprovedCriteriaService,
        private readonly AnnouncementUserService $announcementUserService,
        private readonly TaskUserService $taskUserService,
        private readonly AnnouncementGroupSessionService $announcementGroupSessionService,
        private readonly TokenExtractorService $tokenExtractorService
    ) {
        parent::__construct($em, $settings);
    }

    /**
     * @throws \Exception
     */
    public function buildCourseResponse(Course $course, User $user): array
    {
        $request = $this->requestStack->getCurrentRequest();
        $isNew = $request->get('new', false);
        $this->user = $user;

        $announcement = $this->getAnnouncementCourse($course, $user);

        $userCourse = $this->userCourseRepository->findOneBy([
            'user' => $user,
            'course' => $course,
            'announcement' => ($announcement) ?: null,
        ]);

        $announcementUser = $this->em->getRepository(AnnouncementUser::class)
            ->findOneBy(['announcement' => $announcement, 'user' => $user]);

        $dateFinishAnnouncement = $announcementUser ? $announcementUser->getDateApproved() : null;

        $finishUserCourse = $userCourse?->getFinishedAt();

        $seasonsData = $this->getSeasonsData($course, $userCourse);
        $averageTime = $this->userCourseChapterRepository->getAverageTimeSpentByCourse($course);

        $queryParameters = [
            'courseId' => $course->getId(),
            'typeCodeCourse' => $course->getTypeCourse() ? $course->getTypeCourse()->getCode() : TypeCourse::CODE_ONLINE,
            'user' => $user,
            'isEmptyComment' => $this->settings->get('app.survey.hide.empty_comment'),
        ];

        if ($announcement) {
            $queryParameters['announcementId'] = $announcement->getId();
        }

        $totalOpinions = $this->npsRepository->countOpinionsCourse($queryParameters);
        $currentSession = $announcement ? $this->getCurrentSession($announcement, $announcementUser) : null;

        $courseData = [
            'course' => $this->getDataCourse($course, $announcement, $user),
            'points' => $user->getPoints(),
            'userCourseId' => $userCourse ? $userCourse->getId() : '',
            'finished' => $announcement ? $dateFinishAnnouncement : $finishUserCourse,
            'valued' => $this->isCourseValued($userCourse, $user, $announcement),
            'totalOpinions' => $totalOpinions > 0 ? \intval($totalOpinions) : 0,
            'averageTime' => $averageTime,
            'progress' => $userCourse ? $userCourse->getProgress() : 0,
            'announcement' => $announcement ? $announcement->getId() : null,
            'materials' => $announcement ? $this->fetchMaterialAnnoucement($announcement)
                : $this->fetchMaterialCourse($course),
            'tasks' => $announcement ? $this->fetchTaskAnnouncement($announcement, $user) : [],
            'tutor' => $announcement ? $this->fetchInformationTutor($announcement, $user) : [],
            'seasons' => $announcement ? $this->seasonDataAnnouncement($announcement, $user) : $seasonsData,
            'channels' => $this->getCourseChatChannels($course, $user),
            'infoAnnouncement' => $this->informationAditionalAnnouncement($announcement),
            'isReadDidacticGuide' => $announcement ? $this->isReadDidactidGuideUser($announcement, $user) : false,
            'configurations' => [
                'SECTIONS' => $this->getConfigurationCourse($announcement, $user),
                'CRITERIA' => $this->getCriteriaCourse($announcement),
            ],
            'hasDigitalSignature' => $announcement ?
                $this->apiAnnouncementService->theCourseHasSignatureDigitalPending($announcement, $user) : false,
            'dateDigitalSignature' => $announcement ? $currentSession['digitalSignatureTime'] : null,
            'canStart' => $announcement ? $this->canStart($announcement) : true,
        ];

        if (!$isNew) {
            $courseData['itineraries'] = $this->itineryRepository->findByUserAndCourse($user, $course);
            $courseData['messages'] = [];
        }

        return $courseData;
    }

    private function getCurrentSession(Announcement $announcement, AnnouncementUser $announcementUser): array
    {
        $response = [
            'hasDigitalSignature' => false,
            'digitalSignatureTime' => null,
        ];

        if (!$announcement) {
            return $response;
        }

        $session = $this->announcementGroupSessionService->getCurrentSession($announcement, $announcementUser);

        if (!$session) {
            return $response;
        }

        $hasDigitalSignature = $this->announcementConfigurationsService->hasDigitalSignature($announcement);
        $sessionSignature = $hasDigitalSignature ?
            $this->announcementGroupSessionService->getSessionSignature($announcement, $session, $announcementUser) : null;
        $digitalSignatureTime = $sessionSignature ? $sessionSignature->getCreatedAt()->format('H:i') : null;

        return [
            'hasDigitalSignature' => $hasDigitalSignature,
            'digitalSignatureTime' => $digitalSignatureTime,
        ];
    }

    private function getAnnouncementCourse(Course $course, User $user)
    {
        $request = $this->requestStack->getCurrentRequest();
        $isNew = $request->get('new', false);
        $idAnnouncement = $request->get('idAnnouncement', null);

        if ($isNew) {
            return $idAnnouncement ? $this->announcementRepository->find($idAnnouncement) : null;
        }

        return $this->announcementRepository->findAnnouncementUserNotified($course, $user);
    }

    private function getDataCourse(Course $course, ?Announcement $announcement = null, User $user): array
    {
        $request = $this->requestStack->getCurrentRequest();
        $isNew = $request->get('new', false);

        $baseUrl = $request->getSchemeAndHttpHost();

        $userCourse = $this->userCourseRepository->findOneBy([
            'user' => $user,
            'course' => $course,
            'announcement' => ($announcement) ?: null,
        ]);

        $seasonsData = $this->getSeasonsData($course, $userCourse);
        $typeCourseData = $this->getTypeCourseData($course, $user);

        $data = [
            'id' => $course->getId(),
            'code' => $course->getCode(),
            'name' => $course->getName(),
            'description' => $course->getDescription(),
            'image' => $course->getImage(),
            'generalInformation' => $this->settings->get('app.courseInfoGeneral') ? $course->getGeneralInformation() : null,
            'documentation' => $course->getDocumentation(),
            'new' => $course->getIsNew(),
            'imageUrl' => $this->getImageUrl($course, $isNew, $baseUrl),
            'thumbnail' => $course->getThumbnailUrl(),
            'thumbnails' => $course->getThumbnailUrls(),
            'hasOwnQuestions' => $course->getHasOwnQuestions(),
            'typeCourse' => $typeCourseData,
        ];

        if (!$isNew) {
            $data['categories'] = $course->getCategories();
            $data['category'] = $course->getCategory();
            $data['seasons'] = $announcement ? $this->seasonDataAnnouncement($announcement, $user) : $seasonsData;
        }

        return $data;
    }

    private function getTypeCourseData(Course $course, User $user): array
    {
        $typeCourse = $course->getTypeCourse();
        $translation = $typeCourse->translate($user->getLocaleCampus());

        return [
            'id' => $typeCourse ? $typeCourse->getId() : 1,
            'name' => $translation->getName() ?? $typeCourse->getName(),
        ];
    }

    private function getImageUrl(Course $course, $isNew, string $baseUrl): ?string
    {
        if ($isNew && $course->getImageUrl()) {
            return $baseUrl . '/' . $course->getImageUrl();
        }

        return $course->getImageUrl();
    }

    public function seasonDataAnnouncement(Announcement $announcement, User $user): array
    {
        $chapters = $this->apiAnnouncementService->getTemporalizationAndSessionsAnnouncement($announcement, $user);

        $seasons = [];
        $seasons[] = [
            'id' => 1,
            'name' => 'annoucement',
            'type' => 'announcement',
            'chapters' => $chapters,
        ];

        return $seasons;
    }

    private function getSeasonsData(Course $course, ?UserCourse $userCourse): array
    {
        $seasons = $course->getSeasons();

        $seasonsData = [];

        foreach ($seasons as $season) {
            $chapters = $this->chapterRepository
                ->findBy(
                    criteria: [
                        'season' => $season,
                        'isActive' => true,
                        'deletedAt' => null,
                    ],
                    orderBy: [
                        'position' => 'ASC',
                    ],
                )
            ;

            if (0 === \count($chapters)) {
                // No chapter is active, no reason to show the season
                continue;
            }

            $chapters = array_filter(
                $chapters,
                fn (Chapter $chapter) => $chapter->isIsActive()
                    && $chapter->hasContentCompleted()
                    && $chapter->getType()->isActive()
            );

            if (0 === \count($chapters)) {
                // No chapter has content completed or the type is active. No reason to show the season
                continue;
            }

            foreach ($chapters as $chapter) {
                $this->updateChapterStatus($userCourse, $chapter);
            }

            $seasonsData[] = [
                'id' => $season->getId(),
                'name' => $season->getName(),
                'type' => $season->getType(),
                'chapters' => $this->getDataSeasonsChapter($chapters),
            ];
        }

        return $seasonsData;
    }

    private function getDataSeasonsChapter($seasonsChapters): array
    {
        $request = $this->requestStack->getCurrentRequest();

        $isNew = $request->get('new', false);
        $baseUrl = $request->getSchemeAndHttpHost();

        $chapters = [];

        $dataToReplace = [
            '%userId' => \is_null($this->user->getId()) ? 0 : $this->user->getId(),
        ];

        foreach ($seasonsChapters as $chapter) {
            $isActiveChapterType = $chapter->getType()->isActive();
            $isActiveChapter = $chapter->isIsActive();
            $hasContentCompleted = $chapter->hasContentCompleted();

            if ($isActiveChapterType && $isActiveChapter && $hasContentCompleted) {
                $chapters[] = [
                    'id' => $chapter->getId(),
                    'title' => $chapter->getTitle(),
                    'description' => $chapter->getDescription(),
                    'type' => [
                        'id' => $chapter->getType()->getId(),
                        'name' => $chapter->getType()->getNormalizedName(),
                    ],
                    'playerUrl' => ChapterContent::LTI_TYPE === $chapter->getType()->getId() ?
                        str_replace(array_keys($dataToReplace), array_values($dataToReplace), $chapter->getPlayerUrl()) : $chapter->getPlayerUrl(),
                    'status' => $chapter->getStatus(),
                    'image' => $chapter->getImage(),
                    'imageUrl' => $isNew && $chapter->getImageUrl() ? $baseUrl . '/' . $chapter->getImageUrl() : $chapter->getImageUrl(),
                    'thumbnail' => $chapter->getThumbnailUrl(),
                    'thumbnails' => $chapter->getThumbnailUrls(),
                    'order' => $chapter->getOrder(),
                ];
            }
        }

        return $chapters;
    }

    private function updateChapterStatus(?UserCourse $userCourse, Chapter $chapter): void
    {
        $userCourseChapter = $this->userCourseChapterRepository->findOneBy([
            'userCourse' => $userCourse,
            'chapter' => $chapter,
        ]);

        if (!$userCourse || !$userCourseChapter || \is_null($userCourseChapter->getFinishedAt())) {
            $chapter->setStatus(false);
        } else {
            $chapter->setStatus(true);
        }
    }

    private function isCourseValued($userCourse, User $user, ?Announcement $announcement = null): bool
    {
        if ($announcement) {
            $announcementUser = $this->em->getRepository(AnnouncementUser::class)
                ->findOneBy(['announcement' => $announcement, 'user' => $user]);

            if (!$announcementUser) {
                return false;
            }

            if (!$this->announcementConfigurationsService->hasSurvey($announcement)) {
                return true;
            }

            if (null == $announcementUser->getDateApproved()) {
                return false;
            }

            return $announcementUser && $announcementUser->getValuedCourseAt() && $announcementUser->getDateApproved();
        }

        if (!$this->settings->get('app.opinions.platform')) {
            return true;
        }

        return $userCourse && $userCourse->getValuedAt();
    }

    public function getCourseChatChannels(Course $course, User $user): array
    {
        $typeCourse = $course->getTypeCourse();
        if (TypeCourse::TYPE_PRESENCIAL === $typeCourse->getId() || TypeCourse::TYPE_AULA_VIRTUAL === $typeCourse->getId()) {
            return [
                'forumChannel' => null,
                'chatChannel' => null,
            ];
        }

        $info = ['forumChannel' => null, 'chatChannel' => null];

        /** @var Announcement|null $announcement */
        $announcement = $this->getAnnouncementCourse($course, $user);

        if (!$announcement) {
            return $info;
        }
        $server = $this->em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_ANNOUNCEMENT, $announcement->getId(), true);

        /** @var AnnouncementGroup|null $announcementGroup */
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->findAnnouncementGroupUser($announcement, $user);

        /** @var ChatChannelRepository $chatChannelRepository */
        $chatChannelRepository = $this->em->getRepository(ChatChannel::class);
        //        $forumChannel = $chatChannelRepository->getChannelByType($server, Announcement::CHAT_CHANNEL_FORUM);
        $forumChannel = $chatChannelRepository->getChannelByEntityType(
            $server,
            Announcement::CHAT_CHANNEL_FORUM,
            $announcementGroup ? $announcementGroup->getId() : null,
            AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP
        );

        $info['forumChannel'] = $forumChannel ? $forumChannel->getId() : null;
        $info['chat'] = [];

        $mainChatChannel = $chatChannelRepository->getChannelByType($server, Announcement::CHAT_CHANNEL_DIRECT);
        $tutor = $announcementGroup ? $announcementGroup->getAnnouncementTutor() : null;

        if ($tutor) {
            $chatChannel = $chatChannelRepository->getDirectChatChannel($mainChatChannel, $user, $tutor->getTutor());
            if ($chatChannel) {
                $chatChannel->setName($user->getFullName())
                    ->setEntityId($announcementGroup->getId())
                    ->setEntityType(AnnouncementGroup::TYPE_ANNOUNCEMENT_DIRECT);
                $this->em->persist($chatChannel);
                $this->em->flush();
                $info['chatChannel'] = $chatChannel->getId();
                $info['chat']['tutor'] = [
                    'id' => $chatChannel->getId(),
                    'name' => $tutor->getTutor()->getFullName(),
                ];
            }

            $publicChannel = $chatChannelRepository->getChannelByEntityType(
                $server,
                Announcement::CHAT_CHANNEL_GROUP_CHAT,
                $announcementGroup->getId(),
                AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP
            );
            if ($publicChannel) {
                $userChannelGroups = $this->em->getRepository(ChatChannel::class)->createQueryBuilder('cc')
                    ->select('cc.id', 'cc.name', 'cc.type')
                    ->join('cc.chatChannelUsers', 'cu')
                    ->join('cu.user', 'u')
                    ->where('cc.server =:server')
                    ->andWhere('cc.parent = :parent')
                    ->andWhere('u.id = :userId')
                    ->setParameters([
                        'server' => $server,
                        'parent' => $publicChannel,
                        'userId' => $user->getId(),
                    ])
                    ->getQuery()
                    ->getResult();
                $info['chat']['public'] = [
                    'id' => $publicChannel->getId(),
                    'name' => $publicChannel->getName(),
                ];
                $info['chat']['groups'] = $userChannelGroups;
            }
        }

        return $info;
    }

    private function fetchMaterialAnnoucement(Announcement $announcement): array
    {
        $materialAnnouncement = $this->em->getRepository(MaterialCourse::class)->findBy([
            'announcement' => $announcement,
            'isActive' => true,
        ]);

        return $this->generateMaterialCourseData($materialAnnouncement);
    }

    /**
     * @param MaterialCourse[] $materials
     */
    private function generateMaterialCourseData($materials): array
    {
        $data = [];
        foreach ($materials as $m) {
            $data[] = [
                'id' => $m->getId(),
                'name' => $m->getName(),
                'url' => $m->getUrl(),
                'createdAt' => $m->getCreatedAt()->format('c'),
                'typeMaterial' => $m->getTypeMaterial(),
                'mimeType' => $m->getMimeType(),
                'urlMaterial' => $m->getUrlMaterial(),
                'isDownload' => $m->getIsDownload(),
                'isVisible' => $m->getIsVisible(),
                'filename' => $m->getFilename(),
                'fileSize' => $m->getFileSize(),
            ];
        }

        return $data;
    }

    private function fetchMaterialCourse(Course $course): array
    {
        $materialCourse = $this->em->getRepository(MaterialCourse::class)
            ->findBy(['course' => $course, 'announcement' => null, 'isActive' => true]);

        return $this->generateMaterialCourseData($materialCourse);
    }

    private function fetchTaskAnnouncement(Announcement $announcement, User $user): array
    {
        $request = $this->requestStack->getCurrentRequest();

        $isNew = $request->get('new', false);
        $taskAll = [];

        $tasks = $this->taskUserService->getTaskAnnouncementGroup($announcement, $user);
        $unassignedTasks = $this->taskUserService->getUnassignedGroupTasks($announcement);
        $tasks = array_merge($tasks, $unassignedTasks);

        foreach ($tasks as $t) {
            $t->setDateDeliveryAnnouncement($t->getDateDeliveryAnnouncement() ? $t->getDateDeliveryAnnouncement() : $t->getDateDelivery());

            $taskUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $t, 'user' => $user]);
            $historyTask = $this->em->getRepository(HistoryDeliveryTask::class)->findOneBy(['taskUser' => $taskUser]);
            $status = $historyTask ? \intval($historyTask->getState()) : 0;

            if ($isNew) {
                $task = $this->structureTask($t, $status);
                $taskFiles = $this->getFilesTaskCourse($t);
                $fileAttachments = $historyTask ? $this->fileAttachmentsTaskUser($taskUser) : [];
                $comments = $historyTask ? $this->commentTaskUser($taskUser) : [];

                $taskArray = array_merge(
                    $task,
                    ['taskFiles' => $taskFiles,  'fileAttachments' => $fileAttachments, 'comments' => $comments, 'isNew' => $isNew]
                );

                $taskAll[] = $taskArray;
            } else {
                $taskUserAll = $this->em->getRepository(TaskUser::class)->findBy(['task' => $t, 'user' => $user]);
                $taskAll[] = [
                    'task' => $t,
                    'historyTask' => $taskUserAll,
                    'lastHistory' => [
                        'state' => $historyTask ? \intval($historyTask->getState()) : 0,
                        'files' => $historyTask ? $historyTask->getFilesHistoryTasks() : [],
                    ],
                ];
            }
        }

        return $taskAll;
    }

    private function structureTask(TaskCourse $taskCourse, $status): array
    {
        return [
            'id' => $taskCourse->getId(),

            'title' => $taskCourse->getTitle(),
            'startDate' => $taskCourse->getStartDate() ? $taskCourse->getStartDate()->format('c') : null,
            'description' => $taskCourse->getDescription(),
            'deliveryDate' => $taskCourse->getDateDeliveryAnnouncement() ? $taskCourse->getDateDeliveryAnnouncement()->format('c') : null,
            'expanded' => false,
            'status' => $status,
        ];
    }

    private function getFilesTaskCourse(TaskCourse $taskCourse): array
    {
        $filesTask = $taskCourse->getFilesTasks();
        $files = [];
        foreach ($filesTask as $file) {
            $files[] = $this->structureTaskFile($file);
        }

        return $files;
    }

    private function structureTaskFile(FilesTask $filesTask): array
    {
        return [
            'id' => $filesTask->getId(),
            'fileOriginalName' => $filesTask->getOriginalName(),
            'fileMimeType' => $filesTask->getMimeType(),
            'fileSize' => $filesTask->getFileSize(),
            'createdAt' => $filesTask->getCreatedAt()->format('c'),
            'filename' => $filesTask->getFilename(),
            'typeMaterial' => $filesTask->getTypeMaterial(),
        ];
    }

    private function commentTaskUser(TaskUser $taskUser): array
    {
        $historyTask = $this->em->getRepository(HistoryDeliveryTask::class)->findOneBy(['taskUser' => $taskUser]);

        $comments = $historyTask->getCommentTasks();

        $commentsData = [];

        foreach ($comments as $comment) {
            $commentsData[] = $this->structureComment($comment);
        }

        return $commentsData;
    }

    private function structureComment(CommentTask $commentTask): array
    {
        $user = $commentTask->getCreatedBy();

        return [
            'id' => $commentTask->getId(),
            'message' => $commentTask->getComment() ?? null,
            'createdAt' => $commentTask->getCreatedAt()->format('c'),
            'userId' => $user->getId(),
            'avatar' => $user->getAvatar() ? $this->settings->get('app.avatar_uploads_path') . '/' . $user->getAvatar() : null,
            'name' => $user ? $user->getFullName() : null,
        ];
    }

    private function fileAttachmentsTaskUser(TaskUser $taskUser): array
    {
        $historyTask = $this->em->getRepository(HistoryDeliveryTask::class)->findOneBy(['taskUser' => $taskUser]);
        $filesHistoryTask = $historyTask->getFilesHistoryTasks();

        $filesData = [];
        foreach ($filesHistoryTask as $file) {
            $filesData[] = $this->structureAttachmentsTaskUser($file);
        }

        return $filesData;
    }

    private function structureAttachmentsTaskUser(FilesHistoryTask $fileHistoryTask): array
    {
        return [
            'id' => $fileHistoryTask->getId(),
            'fileOriginalName' => $fileHistoryTask->getOriginalName(),
            'fileMimeType' => $fileHistoryTask->getMimeType(),
            'fileSize' => $fileHistoryTask->getFileSize(),
            'createdAt' => $fileHistoryTask->getCreatedAt()->format('c'),
            'filename' => $fileHistoryTask->getFilename(),
        ];
    }

    private function fetchInformationTutor(Announcement $announcement, User $user): array|string
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);
        $announcementGroup = $announcementUser ? $announcementUser->getAnnouncementGroup() : null;

        if (!$announcementGroup) {
            return '';
        }

        $createdBy = $announcementGroup ? $this->getAnnouncementTutor($announcementGroup) : $announcement->getCreatedBy();
        if (!$createdBy) {
            return '';
        }

        $avatar = $createdBy->getAvatar() ? $this->settings->get('app.avatar_uploads_path') . '/' . $createdBy->getAvatar() : null;

        return [
            'id' => $createdBy->getId(),
            'firstName' => $createdBy->getFirstName(),
            'lastName' => $createdBy->getLastName(),
            'avatar' => $avatar,
        ];
    }

    private function getAnnouncementTutor(AnnouncementGroup $announcementGroup)
    {
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcementGroup' => $announcementGroup]);

        return $announcementTutor ? $announcementTutor->getTutor() : null;
    }

    private function informationAditionalAnnouncement(?Announcement $announcement = null): array
    {
        $guide = $announcement && $announcement->getDidaticGuide() ? $announcement->getDidaticGuide() : null;
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        return [
            'id' => $announcement ? $announcement->getId() : null,
            'guideURL' => $guide ? $host . '/' . $this->settings->get('app.announcement_didactic_guide') . '/' . $guide->getFilename() : null,
            'totalHours' => $announcement ? $announcement->getTotalHours() : 0,
        ];
    }

    private function isReadDidactidGuideUser(Announcement $announcement, User $user)
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);
        if (!$announcementUser) {
            return false;
        }

        if (null == $announcementUser->isReadDidacticGuide()) {
            return false;
        }

        return $announcementUser->isReadDidacticGuide();
    }

    private function getConfigurationCourse(?Announcement $announcement = null, User $user): array
    {
        $sectionsBD = $this->settings->get('app.course.sections');
        $sections = [
            'INFORMATION' => false,
            'CONTENT' => false,
            'OPINIONS' => false,
        ];

        foreach ($sectionsBD as $value) {
            $sections[$value] = true;
        }

        $sections['MATERIALS'] = true;
        $sections['FORUM'] = false;
        $sections['TASK'] = false;
        $sections['CHAT'] = false;

        $timezone = $announcement && $announcement->getTimezone() ? $announcement->getTimezone() : $this->settings->get('app.default_timezone');

        $now = new \DateTime('now', new \DateTimeZone($timezone));

        if ($announcement && $announcement->getStartAt() > $now) {
            $sections['MATERIALS'] = false;
            $sections['FORUM'] = false;
            $sections['TASK'] = false;
            $sections['CHAT'] = false;

            return $sections;
        }

        if ($announcement) {
            $sections['MATERIALS'] = $this->hasMaterialCourse($announcement);
            $sections['FORUM'] = $this->announcementConfigurationsService->hasForum($announcement);
            $sections['TASK'] = $this->hasTaskAnnouncment($announcement, $user);
            $sections['CHAT'] = $this->announcementConfigurationsService->hasChat($announcement);

            return $sections;
        }

        return $sections;
    }

    private function hasMaterialCourse(Announcement $announcement): bool
    {
        $materialCourse = $this->fetchMaterialAnnoucement($announcement);
        $isMaterial = $this->settings->get('app.course.materials.enabled');

        return \count($materialCourse) > 0 && $isMaterial;
    }

    private function hasTaskAnnouncment(Announcement $announcement, User $user): bool
    {
        $isTaks = $this->settings->get('app.course.tasks.enabled');
        $taskAannouncement = $this->fetchTaskAnnouncement($announcement, $user);

        return \count($taskAannouncement) > 0 && $isTaks;
    }

    private function getCriteriaCourse(?Announcement $announcement = null)
    {
        $criteriaCourse = $this->settings->get('app.course.criteria');

        if ($announcement && $this->announcementAprovedCriteriaService->hasMaximumInactivityTime($announcement)) {
            $maximiunInactivityTime = $this->announcementAprovedCriteriaService->getValueMaximumInactivityTime($announcement);
            $criteriaCourse['MAXIMUM_INACTIVITY_TIME'] = max($maximiunInactivityTime, 0);
        }

        return $criteriaCourse;
    }

    private function canStart(Announcement $announcement): bool
    {
        $timezone = $announcement->getTimezone() ?? $this->settings->get('app.default_timezone');

        $now = new \DateTime('now', new \DateTimeZone($timezone));

        $start = $announcement->getStartAt();

        return $start <= $now;
    }
}
