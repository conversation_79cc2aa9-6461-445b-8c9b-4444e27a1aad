<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\User\ManagerFilter;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterHydrationCriteria;

class ManagerFilterHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (
            !$collection instanceof ManagerFilterCollection
            || !$criteria instanceof ManagerFilterHydrationCriteria
        ) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
