<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Task;
use App\Entity\User;
use App\Tests\Mother\Entity\TaskMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait TaskHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetTask(
        ?string $taskName = null,
        ?string $type = null,
        ?string $status = null,
        array $params = [],
        ?\DateTimeInterface $createdAt = null,
        ?\DateTimeInterface $finishedAt = null,
        ?\DateTimeInterface $startedAt = null,
        ?User $createdBy = null,
    ): Task {
        $em = $this->getEntityManager();

        $task = TaskMother::create(
            taskName: $taskName,
            type: $type,
            status: $status,
            params: $params,
            createdBy: $createdBy,
            createdAt: $createdAt,
            finishedAt: $finishedAt,
            startedAt: $startedAt,
        );

        $em->persist($task);
        $em->flush();

        return $task;
    }
}
