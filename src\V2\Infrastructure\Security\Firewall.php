<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Security;

use App\Entity\User;
use App\V2\Domain\Security\FirewallInterface;
use App\V2\Infrastructure\Service\RouteMethodChecker;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Symfony\Component\Security\Core\Role\RoleHierarchy;
use Symfony\Component\Security\Core\User\UserInterface;

class Firewall implements FirewallInterface
{
    public const string PUBLIC_ACCESS = 'PUBLIC_ACCESS';
    public const string IS_AUTHENTICATED_FULLY = 'IS_AUTHENTICATED_FULLY';

    private readonly RoleHierarchy $roleHierarchy;

    public function __construct(
        private readonly array $security,
        private readonly RouteMethodChecker $routeChecker,
        array $roleHierarchy = [],
    ) {
        $this->roleHierarchy = new RoleHierarchy($roleHierarchy);
    }

    private function getAccessControl(): array
    {
        return $this->security['access_control'] ?? [];
    }

    public function isGranted(Request $request, ?UserInterface $user = null): bool
    {
        if (Request::METHOD_OPTIONS === $request->getMethod()) {
            if ($this->routeChecker->isOptionsOnlyRoute($request->getPathInfo())) {
                throw new RouteNotFoundException(
                    'No route found for "' . $request->getMethod() . ' ' . $request->getPathInfo() . '"'
                );
            }

            return true;
        }

        if (!str_starts_with($request->getPathInfo(), $this->security['prefix'])) {
            return true;
        }

        $accessControl = $this->getAccessControl();

        foreach ($accessControl as $control) {
            $path = trim(str_replace('\/', '/', $control['path'] ?? ''));
            if ($this->requestMatches($request, $path)) {
                // Check first matching rule, no other rules should be checked
                $accessRoles = $control['roles'] ?? null;

                if ($this->checkRoles($accessRoles, self::PUBLIC_ACCESS)) {
                    return true;
                }

                if (!$user) {
                    throw new UnauthorizedHttpException('Unauthorized');
                }

                // Check for authenticated access
                if ($user && $this->checkRoles($accessRoles, self::IS_AUTHENTICATED_FULLY)) {
                    return true;
                }

                // Check user roles
                if ($user) {
                    if ($this->checkRolesInHierarchy($user, $accessRoles)) {
                        return true;
                    }
                }

                return false;
            }
        }

        // By default, requires the user to be authenticated and have the ROLE_ADMIN, to avoid security breaches
        return null !== $user && $this->checkRoles($user->getRoles(), User::ROLE_ADMIN);
    }

    private function checkRolesInHierarchy(UserInterface $user, array $roles): bool
    {
        // Get all roles the user have access to
        $reachableRoles = $this->roleHierarchy->getReachableRoleNames($user->getRoles());

        // If one of the required roles is available inside $reachableRoles, return true
        foreach ($roles as $role) {
            if (\in_array($role, $reachableRoles, true)) {
                return true;
            }
        }

        return false;
    }

    private function checkRoles(mixed $roles, string $role): bool
    {
        if (\is_array($roles) && \in_array($role, $roles)) {
            return true;
        }

        return $roles === $role;
    }

    private function requestMatches(Request $request, string $regexp): bool
    {
        return (bool) preg_match('{' . $regexp . '}', rawurldecode($request->getPathInfo()));
    }
}
