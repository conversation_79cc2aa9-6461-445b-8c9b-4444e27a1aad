<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Service\SettingsService;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Filter\FilterMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Application\Command\DeleteManagerFiltersCommand;
use App\V2\Application\CommandHandler\DeleteManagerFiltersCommandHandler;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\DeleteManagerFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class DeleteManagerFiltersCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?UserRepository $userRepository = null,
        ?FilterRepository $filterRepository = null,
        ?ManagerFilterRepository $managerFilterRepository = null,
        ?SettingsService $settingsService = null,
    ): DeleteManagerFiltersCommandHandler {
        return new DeleteManagerFiltersCommandHandler(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            filterRepository: $filterRepository ?? $this->createMock(FilterRepository::class),
            managerFilterRepository: $managerFilterRepository ?? $this->createMock(ManagerFilterRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class),
        );
    }

    /**
     * @throws UserForbiddenAction
     * @throws InfrastructureException
     * @throws Exception
     * @throws DeleteManagerFiltersCommandHandlerException
     * @throws CriteriaException
     * @throws CollectionException
     * @throws UserNotFoundException
     */
    public function testHandle(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(UserMother::create(id: 1));

        $filterRepository = $this->createMock(FilterRepository::class);
        $filterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new FilterCollection([
                FilterMother::create(id: new Id(1), filterCategoryId: new Id(1), name: 'Filter 1'),
                FilterMother::create(id: new Id(2), filterCategoryId: new Id(1), name: 'Filter 2'),
                FilterMother::create(id: new Id(3), filterCategoryId: new Id(1), name: 'Filter 3'),
            ]));

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $managerFilterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]));

        $managerFilterRepository->expects($this->exactly(3))
            ->method('delete');

        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturn(true);

        $handler = $this->getHandler(
            userRepository: $userRepository,
            filterRepository: $filterRepository,
            managerFilterRepository: $managerFilterRepository,
            settingsService: $settingsService,
        );

        $handler->handle(
            new DeleteManagerFiltersCommand(
                userId: new Id(1),
                filterIds: new IdCollection([new Id(1), new Id(2), new Id(3)]),
            )
        );
    }

    public static function provideExceptionCases(): \Generator
    {
        yield 'disabled filters' => [
            'settingsCallable' => fn () => false,
            'userRepositoryCallable' => null,
            'filterRepositoryFindBy' => null,
            'managerFilterRepositoryFindBy' => null,
            'expectedException' => UserForbiddenAction::filtersNotEnabled(),
        ];

        yield 'user not found' => [
            'settingsCallable' => fn () => true,
            'userRepositoryCallable' => fn () => throw new UserNotFoundException(),
            'filterRepositoryFindBy' => null,
            'managerFilterRepositoryFindBy' => null,
            'expectedException' => new UserNotFoundException(),
        ];

        yield 'one of the filters does not exits' => [
            'settingsCallable' => fn () => true,
            'userRepositoryCallable' => fn () => UserMother::create(id: 1),
            'filterRepositoryFindBy' => fn () => new FilterCollection([
                FilterMother::create(id: new Id(1), name: 'Filter 1'),
            ]),
            'managerFilterRepositoryFindBy' => null,
            'expectedException' => DeleteManagerFiltersCommandHandlerException::filterDoesNotExist(),
        ];

        yield 'one of the filters is not assigned' => [
            'settingsCallable' => fn () => true,
            'userRepositoryCallable' => fn () => UserMother::create(id: 1),
            'filterRepositoryFindBy' => fn () => new FilterCollection([
                FilterMother::create(id: new Id(1), name: 'Filter 1'),
                FilterMother::create(id: new Id(2), name: 'Filter 2'),
                FilterMother::create(id: new Id(3), name: 'Filter 3'),
            ]),
            'managerFilterRepositoryFindBy' => fn () => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
            ]),
            'expectedException' => DeleteManagerFiltersCommandHandlerException::userFilterNotAssignedToUser(),
        ];
    }

    /**
     * @throws UserForbiddenAction
     * @throws InfrastructureException
     * @throws Exception
     * @throws DeleteManagerFiltersCommandHandlerException
     * @throws CriteriaException
     * @throws UserNotFoundException
     * @throws CollectionException
     */
    #[DataProvider('provideExceptionCases')]
    public function testExceptions(
        callable $settingsCallable,
        ?callable $userRepositoryCallable,
        ?callable $filterRepositoryFindBy,
        ?callable $managerFilterRepositoryFindBy,
        $expectedException,
    ): void {
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturnCallback($settingsCallable);

        $userRepository = $this->createMock(UserRepository::class);
        if (null !== $userRepositoryCallable) {
            $userRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($userRepositoryCallable);
        }

        $filterRepository = $this->createMock(FilterRepository::class);
        if (null !== $filterRepositoryFindBy) {
            $filterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($filterRepositoryFindBy);
        }

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        if (null !== $managerFilterRepositoryFindBy) {
            $managerFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($managerFilterRepositoryFindBy);
        }

        $handler = $this->getHandler(
            userRepository: $userRepository,
            filterRepository: $filterRepository,
            managerFilterRepository: $managerFilterRepository,
            settingsService: $settingsService,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle(
            new DeleteManagerFiltersCommand(
                userId: new Id(1),
                filterIds: new IdCollection([new Id(1), new Id(2), new Id(3)]),
            )
        );
    }
}
