<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TrueOrFalse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method TrueOrFalse|null find($id, $lockMode = null, $lockVersion = null)
 * @method TrueOrFalse|null findOneBy(array $criteria, array $orderBy = null)
 * @method TrueOrFalse[]    findAll()
 * @method TrueOrFalse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TrueOrFalseRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TrueOrFalse::class);
    }

    /**
     * Find a TrueOrFalse entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?TrueOrFalse
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $trueOrFalse = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $trueOrFalse;
    }
}
