<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Filter\FilterCategoryMother;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Persistence\Filter\FilterCategory\InMemoryFilterCategoryRepository;

trait FilterCategoryFixtureTrait
{
    private function getFilterCategoryRepository(): object
    {
        return $this->client->getContainer()
            ->get('App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository');
    }

    private function setAndGetFilterCategoryInRepository(
        ?Id $id = null,
        ?Id $parentId = null,
        ?string $name = null,
        int $sort = 0,
    ): FilterCategory {
        $category = FilterCategoryMother::create(
            id: $id,
            parentId: $parentId,
            name: $name,
            sort: $sort
        );

        /** @var InMemoryFilterCategoryRepository $repository */
        $repository = $this->getFilterCategoryRepository();
        $repository->add($category);

        return $category;
    }
}
