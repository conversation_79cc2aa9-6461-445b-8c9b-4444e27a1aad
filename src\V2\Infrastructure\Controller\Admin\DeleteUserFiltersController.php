<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\Entity\User;
use App\V2\Application\Command\DeleteUserFiltersCommand;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Validator\Admin\UserFilterValidator;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class DeleteUserFiltersController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws CollectionException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(Request $request, int $userId): Response
    {
        IdValidator::validateId($userId);
        $body = json_decode($request->getContent(), true);
        UserFilterValidator::validateDeleteUserFilters($body);

        $user = RequestAttributeExtractor::extractUser($request);

        $this->execute(
            new DeleteUserFiltersCommand(
                userId: new Id($userId),
                filterIds: new IdCollection(array_map(fn (int $id) => new Id($id), $body)),
                requestedBy: $user
            )
        );

        return new JsonResponse(
            status: Response::HTTP_NO_CONTENT,
        );
    }
}
