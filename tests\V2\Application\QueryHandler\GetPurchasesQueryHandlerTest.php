<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\V2\Application\Query\GetPurchases;
use App\V2\Application\QueryHandler\GetPurchasesQueryHandler;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PaginatedPurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetPurchasesQueryHandlerTest extends TestCase
{
    private PurchaseRepository&MockObject $purchaseRepository;
    private GetPurchasesQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchaseRepository = $this->createMock(PurchaseRepository::class);
        $this->handler = new GetPurchasesQueryHandler($this->purchaseRepository);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws \Exception
     */
    public function testSuccessfullyHandler(): void
    {
        // Arrange
        $criteria = new PurchaseCriteria();
        $criteria->filterByUserId(IdMother::create(123));
        $criteria->filterByStatus(PurchaseStatus::Pending);

        $query = new GetPurchases($criteria);

        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();
        $purchase3 = PurchaseMother::create();

        $purchaseCollection = new PurchaseCollection([
            $purchase1,
            $purchase2,
            $purchase3,
        ]);

        $totalItems = 10;

        // Act & Assert
        $this->purchaseRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($this->callback(function (PurchaseCriteria $actualCriteria) use ($criteria) {
                return $actualCriteria === $criteria;
            }))
            ->willReturn($purchaseCollection);

        $this->purchaseRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($this->callback(function (PurchaseCriteria $actualCriteria) use ($criteria) {
                return $actualCriteria === $criteria;
            }))
            ->willReturn($totalItems);

        $result = $this->handler->handle($query);

        $this->assertInstanceOf(PaginatedPurchaseCollection::class, $result);
        $this->assertSame($purchaseCollection, $result->getCollection());
        $this->assertCount(3, $result->getCollection()->all());
        $this->assertSame($totalItems, $result->getTotalItems());
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     */
    public function testHandlerWithEmptyCollection(): void
    {
        // Arrange
        $criteria = new PurchaseCriteria();
        $query = new GetPurchases($criteria);

        $emptyCollection = new PurchaseCollection([]);
        $totalItems = 0;

        // Act & Assert
        $this->purchaseRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($emptyCollection);

        $this->purchaseRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willReturn($totalItems);

        $result = $this->handler->handle($query);

        $this->assertInstanceOf(PaginatedPurchaseCollection::class, $result);
        $this->assertSame($emptyCollection, $result->getCollection());
        $this->assertCount(0, $result->getCollection()->all());
        $this->assertTrue($result->getCollection()->isEmpty());
        $this->assertSame($totalItems, $result->getTotalItems());
    }

    /**
     * @throws PurchaseRepositoryException
     */
    public function testThrowsPurchaseRepositoryExceptionWhenRepositoryFails(): void
    {
        // Arrange
        $criteria = new PurchaseCriteria();
        $query = new GetPurchases($criteria);

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willThrowException(new PurchaseRepositoryException('Database connection failed'));

        // The countBy method should not be called if findBy throws an exception
        $this->purchaseRepository
            ->expects($this->never())
            ->method('countBy');

        // Act & Assert
        $this->expectException(PurchaseRepositoryException::class);
        $this->expectExceptionMessage('Database connection failed');

        $this->handler->handle($query);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws \Exception
     */
    public function testHandlerWithComplexCriteria(): void
    {
        // Arrange
        $userId = IdMother::create(456);
        $criteria = new PurchaseCriteria();
        $criteria->filterByUserId($userId);
        $criteria->filterByStatus(PurchaseStatus::Completed);

        $query = new GetPurchases($criteria);

        $purchase = PurchaseMother::create(
            userId: $userId,
            status: PurchaseStatus::Completed
        );

        $purchaseCollection = new PurchaseCollection([$purchase]);
        $totalItems = 5;

        // Act & Assert
        $this->purchaseRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($this->callback(function (PurchaseCriteria $actualCriteria) use ($criteria, $userId) {
                return $actualCriteria === $criteria
                    && $actualCriteria->getUserId()?->equals($userId)
                    && PurchaseStatus::Completed === $actualCriteria->getStatus();
            }))
            ->willReturn($purchaseCollection);

        $this->purchaseRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($this->callback(function (PurchaseCriteria $actualCriteria) use ($criteria, $userId) {
                return $actualCriteria === $criteria
                    && $actualCriteria->getUserId()?->equals($userId)
                    && PurchaseStatus::Completed === $actualCriteria->getStatus();
            }))
            ->willReturn($totalItems);

        $result = $this->handler->handle($query);

        $this->assertInstanceOf(PaginatedPurchaseCollection::class, $result);
        $this->assertSame($purchaseCollection, $result->getCollection());
        $this->assertCount(1, $result->getCollection()->all());
        $this->assertSame($totalItems, $result->getTotalItems());
    }
}
