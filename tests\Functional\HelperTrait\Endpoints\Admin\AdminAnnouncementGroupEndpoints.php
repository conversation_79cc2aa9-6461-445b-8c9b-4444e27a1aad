<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class AdminAnnouncementGroupEndpoints
{
    public static function postAnnouncementGroupEndpoint(): string
    {
        return '/announcement/form/group-info';
    }

    public static function deleteAnnouncementGroupSession(): string
    {
        return '/announcement/form/delete-group-info-session';
    }

    public static function announcementGroupUserEndpoint(
        int $announcementId,
        int $groupId,
        int $userId
    ): string {
        return \sprintf('/api/v2/admin/announcements/%d/groups/%d/user/%d', $announcementId, $groupId, $userId);
    }
}
