<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Application\DTO\Purchase\PatchPurchasableItemInputDTO;
use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class PatchPurchasableItemCommand implements Command
{
    public function __construct(
        private Uuid $purchasableItemId,
        private PatchPurchasableItemInputDTO $input,
    ) {
    }

    public function getPurchasableItemId(): Uuid
    {
        return $this->purchasableItemId;
    }

    public function getInput(): PatchPurchasableItemInputDTO
    {
        return $this->input;
    }
}
