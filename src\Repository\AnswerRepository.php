<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Answer;
use App\Entity\Question;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Answer|null find($id, $lockMode = null, $lockVersion = null)
 * @method Answer|null findOneBy(array $criteria, array $orderBy = null)
 * @method Answer[]    findAll()
 * @method Answer[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnswerRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Answer::class);
    }

    public function findWithDeleted(int $id): ?Answer
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $entity = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $entity;
    }

    /**
     * @return Answer[]
     */
    public function findByAnswerQuestionId(Question $question)
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.question = :question_id')
            ->setParameter('question_id', $question->getId())
            ->orderBy('a.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getCorrectAnswer(int $questionId): ?string
    {

        return $this->createQueryBuilder('a')
            ->select('a.answer as solution')
            ->where('a.question = :questionId')
            ->andWhere('a.correct = 1')
            ->setParameter('questionId', $questionId)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult(Query::HYDRATE_SINGLE_SCALAR);
    }
}
