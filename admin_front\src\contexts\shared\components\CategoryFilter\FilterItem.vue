<template>
  <div
    class="FilterItem"
    :class="classes"
  >
    <span>{{ item.name }}</span>
    <Icon
      class="icon"
      :class="{ 'fa-spin': item.isUpdating }"
      :icon="icon"
      @click="emitChange"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { CategoryFilterOptionsModel } from '@/contexts/shared/models/categoryFilterOptions.model.js'

const emit = defineEmits(['change'])
const props = defineProps({
  item: { type: [CategoryFilterOptionsModel, Object], default: () => ({}) },
  disabled: { type: Boolean, default: false },
  type: { type: String, default: 'add', validator: (value) => ['add', 'remove'].includes(value) },
})

const icon = computed(() => {
  if (props.item.isUpdating) return 'spinner'
  return props.type === 'remove' ? 'minus' : 'plus'
})

const classes = computed(() => [
  props.item.status !== 'default' ? `status ${props.item.status}` : '',
  `type--${props.type}`,
  props.item.disabled || props.disabled ? 'disabled' : '',
])

function emitChange() {
  if (props.item.disabled || props.disabled || props.item.isUpdating) {
    return null
  }
  emit('change')
}
</script>

<style scoped lang="scss">
.FilterItem {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  padding: 0.8rem 1.5rem;
  gap: 1rem;
  border: 1px solid var(--color-neutral-mid);
  background-color: var(--color-neutral-lightest);
  border-radius: 5px;

  span {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &.status {
    border-width: 1px 1px 1px 5px;

    &.info {
      border-left-color: var(--color-primary);
    }

    &.success {
      border-left-color: var(--color-success);
    }

    &.warning {
      border-left-color: var(--color-warning);
    }

    &.danger {
      border-left-color: var(--color-danger);
    }
  }

  .icon {
    font-size: 1.5rem;
    cursor: pointer;
  }

  &.type--add .icon {
    color: var(--color-primary);
  }

  &.type--remove .icon {
    color: var(--color-danger);
  }

  &.disabled {
    .icon,
    span {
      color: var(--color-neutral-mid-dark);
    }
  }
}
</style>
