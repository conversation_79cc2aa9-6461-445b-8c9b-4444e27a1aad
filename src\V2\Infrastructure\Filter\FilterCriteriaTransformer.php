<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Filter;

use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Shared\Id\Id;

class FilterCriteriaTransformer
{
    public static function fromArray(array $params): FilterCriteria
    {
        $criteria = FilterCriteria::createEmpty();

        if (isset($params['search'])) {
            $criteria->filterBySearch($params['search']);
        }

        if (isset($params['parent_id'])) {
            $criteria->filterByParentId(new Id((int) $params['parent_id']));
        }

        if (isset($params['category_id'])) {
            $criteria->filterByCategoryId(new Id((int) $params['category_id']));
        }

        return $criteria;
    }
}
