<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\Announcement;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\Tests\V2\Mother\Announcement\Manager\ManagerMother;
use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydratorCollection;
use App\V2\Application\Query\Admin\GetAnnouncementManagers;
use App\V2\Application\QueryHandler\Admin\GetAnnouncementManagersHandler;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\GetAnnouncementManagerException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerHydrationCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class GetAnnouncementManagersHandlerTest extends TestCase
{
    private function getHandler(
        ?AnnouncementManagerRepository $announcementManagerRepository = null,
        ?LegacyAnnouncementRepository $legacyAnnouncementRepository = null,
        ?AnnouncementManagerHydratorCollection $hydratorCollection = null,
        ?SettingsService $settingsService = null,
    ): GetAnnouncementManagersHandler {
        return new GetAnnouncementManagersHandler(
            announcementManagerRepository: $announcementManagerRepository ?? $this->createMock(AnnouncementManagerRepository::class),
            legacyAnnouncementRepository: $legacyAnnouncementRepository ?? $this->createMock(LegacyAnnouncementRepository::class),
            hydratorCollection: $hydratorCollection ?? $this->createMock(AnnouncementManagerHydratorCollection::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws AnnouncementNotFoundException
     * @throws GetAnnouncementManagerException|ManagerNotAuthorizedException
     */
    #[DataProvider('provideExceptions')]
    public function testExceptions(
        bool $notAuthorizedException,
        bool $announcementNotFoundException,
        bool $hydratorException,
        \Exception $expectedException,
    ) {
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(!$notAuthorizedException);

        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);
        $legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        $legacyAnnouncementRepository->method('findOneBy')
            ->willReturn($announcementNotFoundException ? null : new Announcement());

        $hydratorCollection = $this->createMock(AnnouncementManagerHydratorCollection::class);
        $hydratorCollection->method('hydrate')
            ->willReturnCallback(function () use ($hydratorException) {
                if ($hydratorException) {
                    throw new HydratorException();
                }
            });

        $query = new GetAnnouncementManagers(
            criteria: AnnouncementManagerCriteria::createEmpty(),
            withManagers: true,
        );

        $handler = $this->getHandler(
            announcementManagerRepository: $announcementManagerRepository,
            legacyAnnouncementRepository: $legacyAnnouncementRepository,
            hydratorCollection: $hydratorCollection,
            settingsService: $settingsService,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle($query);
    }

    public static function provideExceptions(): \Generator
    {
        yield 'Manager not authorized' => [
            'notAuthorizedException' => true,
            'announcementNotFoundException' => false,
            'hydratorException' => false,
            'expectedException' => new ManagerNotAuthorizedException(),
        ];

        yield 'Announcement not found' => [
            'notAuthorizedException' => false,
            'announcementNotFoundException' => true,
            'hydratorException' => false,
            'expectedException' => new AnnouncementNotFoundException(),
        ];

        yield 'Hydrator exception' => [
            'notAuthorizedException' => false,
            'announcementNotFoundException' => false,
            'hydratorException' => true,
            'expectedException' => GetAnnouncementManagerException::fromPrevious(new HydratorException()),
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws AnnouncementNotFoundException
     * @throws GetAnnouncementManagerException
     * @throws CollectionException|ManagerNotAuthorizedException
     */
    public function testHandleWithManagers(): void
    {
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $manager1 = ManagerMother::create(
            id: new Id(1),
            email: EmailMother::create(email: '<EMAIL>'),
            firstName: 'Manager',
            lastName: 'One',
        );
        $manager2 = ManagerMother::create(
            id: new Id(2),
            email: EmailMother::create(email: '<EMAIL>'),
            firstName: 'Manager',
            lastName: 'Two',
        );

        $manager3 = ManagerMother::create(
            id: new Id(3),
            email: EmailMother::create(email: '<EMAIL>'),
            firstName: 'Manager',
            lastName: 'Three',
        );

        $hydratorCollection = $this->createMock(AnnouncementManagerHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(function (
                AnnouncementManagerCollection $collection,
                AnnouncementManagerHydrationCriteria $criteria
            ) use (
                $manager1,
                $manager2,
                $manager3
            ) {
                foreach ($collection->all() as $announcementManager) {
                    $announcementManager->setManager(
                        match ($announcementManager->getUserId()->value()) {
                            $manager1->getId()->value() => $manager1,
                            $manager2->getId()->value() => $manager2,
                            $manager3->getId()->value() => $manager3,
                            default => $this->fail('Unknown manager'),
                        }
                    );
                }
            });

        $announcementManager1 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));
        $announcementManager2 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(1));
        $announcementManager3 = AnnouncementManagerMother::create(userId: new Id(3), announcementId: new Id(1));

        $legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        $legacyAnnouncementRepository->method('findOneBy')
            ->willReturn(AnnouncementMother::create(id: 1));

        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);
        $announcementManagerRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new AnnouncementManagerCollection([$announcementManager1, $announcementManager2, $announcementManager3]));

        $query = new GetAnnouncementManagers(
            criteria: AnnouncementManagerCriteria::createEmpty()
                ->filterByAnnouncementId(1),
            withManagers: true,
        );

        $handler = $this->getHandler(
            announcementManagerRepository: $announcementManagerRepository,
            legacyAnnouncementRepository: $legacyAnnouncementRepository,
            hydratorCollection: $hydratorCollection,
            settingsService: $settingsService,
        );

        $result = $handler->handle($query);
        $this->assertCount(3, $result);

        $this->assertNotNull($announcementManager1->getManager());
        $this->assertEquals($manager1, $announcementManager1->getManager());

        $this->assertNotNull($announcementManager2->getManager());
        $this->assertEquals($manager2, $announcementManager2->getManager());

        $this->assertNotNull($announcementManager3->getManager());
        $this->assertEquals($manager3, $announcementManager3->getManager());
    }
}
