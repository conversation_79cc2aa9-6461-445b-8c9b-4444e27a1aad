<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Hydrator\HydrationCriteria;

class PurchaseHydrationCriteria extends HydrationCriteria
{
    private bool $withPurchaseItem = false;
    private bool $withPurchasableItem = false;

    public function isEmpty(): bool
    {
        return false === $this->withPurchaseItem;
    }

    public function withPurchaseItem(bool $withPurchasableItem = false): self
    {
        $this->withPurchaseItem = true;
        $this->withPurchasableItem = $withPurchasableItem;

        return $this;
    }

    public function needsPurchaseItem(): bool
    {
        return $this->withPurchaseItem;
    }

    public function needsPurchasableItem(): bool
    {
        return $this->withPurchasableItem;
    }
}
