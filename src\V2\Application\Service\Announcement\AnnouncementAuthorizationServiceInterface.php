<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Announcement;

use App\Entity\Announcement as LegacyAnnouncement;
use App\Entity\Course as LegacyCourse;
use App\Entity\User;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface AnnouncementAuthorizationServiceInterface
{
    /**
     * @throws ManagerNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): void;

    /**
     * @throws ManagerNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanCreateAnnouncement(User $user, LegacyCourse $course): void;
}
