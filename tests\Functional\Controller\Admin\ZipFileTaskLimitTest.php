<?php

declare(strict_types=1);

namespace App\Tests\Functional\Controller\Admin;

use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Service\TaskLimitService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Backend\BackendZipFileTaskEndpoints;
use Symfony\Component\HttpFoundation\Response;

class ZipFileTaskLimitTest extends FunctionalTestCase
{
    private ?string $token = null;
    private ?int $testCourseId = null;

    protected function setUp(): void
    {
        parent::setUp();

        $typeCourse = $this->getTypeCourse();

        $course = $this->createAndGetCourse(
            'Test Course', // name
            'courseCode-1', // code
            $typeCourse, // typeCourse
            'Test course description', // description
            'es', // locale
            true, // active
            true, // open
            true, // isNew
            true, // openVisible
            null // CourseCategory
        );
        $this->testCourseId = $course->getId();

        $this->truncateEntities([ZipFileTask::class]);
        $this->token = $this->loginAndGetToken();
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([ZipFileTask::class]);
        parent::tearDown();
    }

    public function testZipFileTaskLimitExceeded(): void
    {
        $user = $this->getDefaultUser();
        $em = $this->getEntityManager();
        $taskLimitService = $this->getService(TaskLimitService::class);
        $limit = $taskLimitService->getLimit();

        // Create tasks until the limit minus one
        for ($i = 0; $i < $limit - 1; ++$i) {
            $task = new ZipFileTask();
            $task->setTask('test-zip-task')
                ->setType('test-type')
                ->setEntityId('123')
                ->setCreatedBy($user)
                ->setStatus(ZipFileTask::STATUS_PENDING)
                ->setOriginalName('test-file-' . $i . '.zip')
                ->setParams(['params' => ['param1' => 'value1']]);

            $em->persist($task);
        }

        $em->flush();

        // Verify we have one less task than the limit
        $taskCount = $this->countPendingZipFileTasksForUser($user);
        $this->assertEquals($limit - 1, $taskCount);

        // Make an API request to create one more task (should succeed)
        $response = $this->makeRequest(
            'POST',
            BackendZipFileTaskEndpoints::createCourseStatsXlsx($this->testCourseId),
            [],
            [
                'task' => 'test-zip-task',
                'params' => ['param1' => 'value1'],
                'type' => 'test-type',
                'originalName' => 'success-file.zip',
                'entityId' => '123',
            ],
            ['CONTENT_TYPE' => 'application/json'],
            $this->token
        );

        $responseContent = $response->getContent();
        $responseData = json_decode($responseContent, true);

        // Verify the request was successful
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        // Verify we've reached the limit
        $em->clear(); // Clear entity manager to force reload
        $taskCount = $this->countPendingZipFileTasksForUser($user);
        $this->assertEquals($limit, $taskCount);

        // Make another API request (should fail with 403)
        $response = $this->makeRequest(
            'POST',
            BackendZipFileTaskEndpoints::createCourseStatsXlsx($this->testCourseId),
            [],
            [
                'task' => 'test-zip-task',
                'params' => ['param1' => 'value1'],
                'type' => 'test-type',
                'originalName' => 'fail-file.zip',
                'entityId' => '123',
            ],
            ['CONTENT_TYPE' => 'application/json'],
            $this->token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $responseContent = $response->getContent();
        $responseData = json_decode($responseContent, true);

        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('ha alcanzado el límite', $responseData['message']);

        // Additional verification: check that the number of tasks hasn't increased
        $taskCount = $this->countPendingZipFileTasksForUser($user);
        $this->assertEquals($limit, $taskCount);
    }

    private function countPendingZipFileTasksForUser(User $user): int
    {
        $em = $this->getEntityManager();
        $qb = $em->getRepository(ZipFileTask::class)->createQueryBuilder('z')
            ->select('COUNT(z.id)')
            ->where('z.createdBy = :user')
            ->andWhere('z.deletedAt IS NULL')
            ->andWhere('z.status IN (:statuses)')
            ->setParameter('user', $user)
            ->setParameter('statuses', [
                ZipFileTask::STATUS_PENDING,
                ZipFileTask::STATUS_IN_PROGRESS,
                ZipFileTask::STATUS_TIMEOUT,
            ]);

        return (int) $qb->getQuery()->getSingleScalarResult();
    }
}
