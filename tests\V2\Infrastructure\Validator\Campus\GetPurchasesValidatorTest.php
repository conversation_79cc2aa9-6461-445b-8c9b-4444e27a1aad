<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Campus;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Campus\GetPurchasesValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetPurchasesValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        GetPurchasesValidator::validateGetPurchasesRequest($payload);
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Valid page and page_size' => [
            [
                'page' => '1',
                'page_size' => '10',
            ],
        ];

        yield 'Valid amount_min' => [
            [
                'amount_min' => '100',
            ],
        ];

        yield 'Valid amount_max' => [
            [
                'amount_max' => '1000',
            ],
        ];

        yield 'Valid status' => [
            [
                'status' => 'completed',
            ],
        ];

        yield 'Valid start_date' => [
            [
                'start_date' => '2023-01-01 00:00:00',
            ],
        ];

        yield 'Valid end_date' => [
            [
                'end_date' => '2023-12-31 23:59:59',
            ],
        ];

        yield 'Valid sort_by and sort_dir' => [
            [
                'sort_by' => 'created_at',
                'sort_dir' => 'desc',
            ],
        ];

        yield 'Valid all fields' => [
            [
                'page' => '1',
                'page_size' => '10',
                'amount_min' => '100',
                'amount_max' => '1000',
                'status' => 'completed',
                'start_date' => '2023-01-01 00:00:00',
                'end_date' => '2023-12-31 23:59:59',
                'sort_by' => 'created_at',
                'sort_dir' => 'desc',
            ],
        ];
    }

    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            GetPurchasesValidator::validateGetPurchasesRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Empty page' => [
            [
                'page' => '',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'page is not a digit' => [
            [
                'page' => 'abc',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'page is less than 1' => [
            [
                'page' => '0',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'page_size is required when page is provided' => [
            [
                'page' => '1',
            ],
            [
                '[page]' => [
                    'Page size is required when page is provided.',
                ],
            ],
        ];

        yield 'page_size is empty' => [
            [
                'page' => '1',
                'page_size' => '',
            ],
            [
                '[page_size]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'page_size is not a digit' => [
            [
                'page' => '1',
                'page_size' => 'abc',
            ],
            [
                '[page_size]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'page_size is less than 1' => [
            [
                'page' => '1',
                'page_size' => '0',
            ],
            [
                '[page_size]' => [
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'page is required when page_size is provided' => [
            [
                'page_size' => '10',
            ],
            [
                '[page_size]' => [
                    'Page is required when page size is provided.',
                ],
            ],
        ];

        yield 'amount_min is empty' => [
            [
                'amount_min' => '',
            ],
            [
                '[amount_min]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'amount_min is not a digit' => [
            [
                'amount_min' => 'abc',
            ],
            [
                '[amount_min]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'amount_max is empty' => [
            [
                'amount_max' => '',
            ],
            [
                '[amount_max]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'amount_max is not a digit' => [
            [
                'amount_max' => 'abc',
            ],
            [
                '[amount_max]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'status is empty' => [
            [
                'status' => '',
            ],
            [
                '[status]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'status is invalid' => [
            [
                'status' => 'invalid',
            ],
            [
                '[status]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'start_date is empty' => [
            [
                'start_date' => '',
            ],
            [
                '[start_date]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'start_date is not a string' => [
            [
                'start_date' => 123,
            ],
            [
                '[start_date]' => [
                    'This value should be of type string.',
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'start_date is invalid format' => [
            [
                'start_date' => 'not-a-date',
            ],
            [
                '[start_date]' => [
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'end_date is empty' => [
            [
                'end_date' => '',
            ],
            [
                '[end_date]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'end_date is not a string' => [
            [
                'end_date' => 123,
            ],
            [
                '[end_date]' => [
                    'This value should be of type string.',
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'end_date is invalid format' => [
            [
                'end_date' => 'not-a-date',
            ],
            [
                '[end_date]' => [
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'sort_by is empty' => [
            [
                'sort_by' => '',
                'sort_dir' => 'asc',
            ],
            [
                '[sort_by]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'sort_by is not a string' => [
            [
                'sort_by' => 123,
                'sort_dir' => 'asc',
            ],
            [
                '[sort_by]' => [
                    'This value should be of type string.',
                ],
            ],
        ];

        yield 'sort_by without sort_dir' => [
            [
                'sort_by' => 'created_at',
            ],
            [
                '[sort_by]' => [
                    'Sort direction is required when sort by is provided.',
                ],
            ],
        ];

        yield 'sort_dir is empty' => [
            [
                'sort_by' => 'created_at',
                'sort_dir' => '',
            ],
            [
                '[sort_dir]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_dir is not a string' => [
            [
                'sort_by' => 'created_at',
                'sort_dir' => 123,
            ],
            [
                '[sort_dir]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_dir without sort_by' => [
            [
                'sort_dir' => 'asc',
            ],
            [
                '[sort_dir]' => [
                    'Sort by is required when sort direction is provided.',
                ],
            ],
        ];

        yield 'sort_dir is invalid' => [
            [
                'sort_by' => 'created_at',
                'sort_dir' => 'invalid',
            ],
            [
                '[sort_dir]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'multiple validation errors' => [
            [
                'page' => '0',
                'page_size' => 'abc',
                'status' => 'invalid',
                'start_date' => 'not-a-date',
                'sort_by' => '',
                'sort_dir' => 'invalid',
            ],
            [
                '[page]' => [
                    'Page must be greater than 0.',
                ],
                '[page_size]' => [
                    'This value should be of type digit.',
                ],
                '[status]' => [
                    'The value you selected is not a valid choice.',
                ],
                '[start_date]' => [
                    'This value is not a valid datetime.',
                ],
                '[sort_by]' => [
                    'This value should not be blank.',
                ],
                '[sort_dir]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'user_id is not expected' => [
            [
                'user_id' => 1,
            ],
            [
                '[user_id]' => [
                    'This field was not expected.',
                ],
            ],
        ];
    }
}
