<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\VirtualMeeting;

use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;

final class VirtualMeetingDTOMother
{
    /**
     * @throws \DateMalformedStringException
     */
    public static function create(
        ?VirtualMeetingType $type = null,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishAt = null,
        ?string $url = null,
    ): VirtualMeetingDTO {
        $now = new \DateTimeImmutable();

        return new VirtualMeetingDTO(
            $type ?? VirtualMeetingType::Fixed,
            $startAt ?? $now->modify('+1 day')->setTime(10, 0),
            $finishAt ?? $now->modify('+2 days')->setTime(22, 0),
            $url,
        );
    }
}
