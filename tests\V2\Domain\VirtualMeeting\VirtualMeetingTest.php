<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\VirtualMeeting;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class VirtualMeetingTest extends TestCase
{
    /**
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    public function testVirtualMeetingCreationAndGetters(): void
    {
        $id = UuidMother::create();
        $type = VirtualMeetingType::Fixed;
        $startAt = new \DateTimeImmutable('2025-08-01 10:00:00');
        $finishAt = new \DateTimeImmutable('2025-08-01 12:00:00');
        $url = 'https://example.com/meeting';
        $createdAt = new \DateTimeImmutable('2025-07-24 09:00:00');
        $updatedAt = null;
        $deletedAt = null;

        $meeting = new VirtualMeeting(
            $id,
            $type,
            $startAt,
            $finishAt,
            $url,
            $createdAt,
            $updatedAt,
            $deletedAt
        );

        $this->assertSame($id, $meeting->getId());
        $this->assertSame($type, $meeting->getType());
        $this->assertEquals($startAt, $meeting->getStartAt());
        $this->assertEquals($finishAt, $meeting->getFinishAt());
        $this->assertSame($url, $meeting->getUrl());
        $this->assertEquals($createdAt, $meeting->getCreatedAt());
        $this->assertNull($meeting->getUpdatedAt());
        $this->assertNull($meeting->getDeletedAt());
    }

    /**
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    #[DataProvider('invalidDataProvider')]
    public function testVirtualMeetingThrowsException(
        \DateTimeImmutable $startAt,
        \DateTimeImmutable $finishAt,
        VirtualMeetingType $type,
        ?string $url,
        InvalidVirtualMeetingException $expectedException,
    ): void {
        $id = UuidMother::create();
        $createdAt = new \DateTimeImmutable('2025-07-24 09:00:00');

        $this->expectExceptionObject($expectedException);

        new VirtualMeeting(
            $id,
            $type,
            $startAt,
            $finishAt,
            $url,
            $createdAt
        );
    }

    public static function invalidDataProvider(): \Generator
    {
        yield 'finishAt before startAt' => [
            'startAt' => new \DateTimeImmutable('2025-08-01 10:00:00'),
            'finishAt' => new \DateTimeImmutable('2025-08-01 09:00:00'),
            'type' => VirtualMeetingType::Fixed,
            'url' => 'https://example.com/meeting',
            'expectedException' => InvalidVirtualMeetingException::finishAtMustBeAfterStartAt(
                new \DateTimeImmutable('2025-08-01 10:00:00'),
                new \DateTimeImmutable('2025-08-01 09:00:00'),
            ),
        ];

        yield 'finishAt equals startAt' => [
            'startAt' => new \DateTimeImmutable('2025-08-01 10:00:00'),
            'finishAt' => new \DateTimeImmutable('2025-08-01 10:00:00'),
            'type' => VirtualMeetingType::Fixed,
            'url' => 'https://example.com/meeting',
            'expectedException' => InvalidVirtualMeetingException::finishAtMustBeAfterStartAt(
                new \DateTimeImmutable('2025-08-01 10:00:00'),
                new \DateTimeImmutable('2025-08-01 10:00:00'),
            ),
        ];

        yield 'invalid URL for Fixed type' => [
            'startAt' => new \DateTimeImmutable('2025-08-01 10:00:00'),
            'finishAt' => new \DateTimeImmutable('2025-08-01 12:00:00'),
            'type' => VirtualMeetingType::Fixed,
            'url' => null, // URL is required for Fixed type
            'expectedException' => InvalidVirtualMeetingException::urlRequiredForFixedType(),
        ];
    }
}
