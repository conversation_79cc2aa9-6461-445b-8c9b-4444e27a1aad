<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\User\ManagerFilter;

use App\Tests\V2\Domain\User\ManagerFilter\ManagerFilterRepositoryTestCase;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Infrastructure\Persistence\User\ManagerFilter\InMemoryManagerFilterRepository;

class InMemoryManagerFilterRepositoryTest extends ManagerFilterRepositoryTestCase
{
    protected function getRepository(): ManagerFilterRepository
    {
        return new InMemoryManagerFilterRepository();
    }
}
