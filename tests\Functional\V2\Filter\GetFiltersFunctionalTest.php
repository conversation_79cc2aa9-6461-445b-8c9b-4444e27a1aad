<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Filter;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\FilterEndpoints;
use App\Tests\Functional\V2\Fixtures\FilterCategoryFixtureTrait;
use App\Tests\Functional\V2\Fixtures\FilterFixtureTrait;
use App\Tests\Functional\V2\Fixtures\ManagerFilterFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetFiltersFunctionalTest extends FunctionalTestCase
{
    use FilterCategoryFixtureTrait;
    use FilterFixtureTrait;
    use ManagerFilterFixtureTrait;

    private int $managerId;
    private int $tutorId;

    protected function setUp(): void
    {
        parent::setUp();
        $manager = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );
        $tutor = $this->createAndGetUser(
            roles: [User::ROLE_TUTOR],
            email: '<EMAIL>',
        );
        $this->managerId = $manager->getId();
        $this->tutorId = $tutor->getId();
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testAsAdmin(): void
    {
        $filter1 = $this->setAndGetFilterInRepository(
            id: new Id(1),
            filterCategoryId: new Id(1),
            name: 'Filter 1',
            code: 'Filter 1',
            sort: 1
        );

        $filter2 = $this->setAndGetFilterInRepository(
            id: new Id(2),
            filterCategoryId: new Id(1),
            name: 'Filter 2',
            code: 'Filter 2',
            sort: 2
        );

        $filter3 = $this->setAndGetFilterInRepository(
            id: new Id(3),
            filterCategoryId: new Id(2),
            name: 'Filter 3',
            code: 'Filter 3',
            sort: 4
        );

        $filter4 = $this->setAndGetFilterInRepository(
            id: new Id(4),
            filterCategoryId: new Id(2),
            name: 'Filter 4',
            code: 'Filter 4',
            sort: 3,
            parentId: new Id(3)
        );

        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(4, $data);
        $this->assertEquals([
            [
                'id' => 1,
                'name' => 'Filter 1',
            ],
            [
                'id' => 2,
                'name' => 'Filter 2',
            ],
            [
                'id' => 4,
                'name' => 'Filter 4',
            ],
            [
                'id' => 3,
                'name' => 'Filter 3',
            ],
        ], $data);

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(categoryId: 2),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(2, $data);
        $this->assertEquals([
            [
                'id' => 4,
                'name' => 'Filter 4',
            ],
            [
                'id' => 3,
                'name' => 'Filter 3',
            ],
        ], $data);

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(parentId: 3),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(1, $data);
        $this->assertEquals([
            [
                'id' => 4,
                'name' => 'Filter 4',
            ],
        ], $data);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testAsManager(): void
    {
        $this->setAndGetFilterInRepository(
            id: new Id(1),
            filterCategoryId: new Id(1),
            name: 'Filter 1',
            code: 'Filter 1',
            sort: 1
        );

        $this->setAndGetFilterInRepository(
            id: new Id(2),
            filterCategoryId: new Id(1),
            name: 'Filter 2',
            code: 'Filter 2',
            sort: 2
        );

        $this->setAndGetFilterInRepository(
            id: new Id(3),
            filterCategoryId: new Id(2),
            name: 'Filter 3',
            code: 'Filter 3',
            sort: 4
        );

        $this->setAndGetFilterInRepository(
            id: new Id(4),
            filterCategoryId: new Id(2),
            name: 'Filter 4',
            code: 'Filter 4',
            sort: 3,
            parentId: new Id(3)
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // No assigned filters
        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(0, $data);

        // Manager filters
        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->managerId),
            filterId: new Id(2)
        );

        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->managerId),
            filterId: new Id(4)
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(2, $data);
        $this->assertEquals([
            [
                'id' => 2,
                'name' => 'Filter 2',
            ],
            [
                'id' => 4,
                'name' => 'Filter 4',
            ],
        ], $data);
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(search: ''),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[search]' => 'This value should not be blank.',
            ],
        ], $content['metadata']);
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>',
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilters(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([$this->managerId, $this->tutorId]);
        parent::tearDown();
    }
}
