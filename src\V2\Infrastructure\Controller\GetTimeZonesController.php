<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller;

use App\V2\Application\Query\GetTimeZones;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\DTO\TimeZonesDtoTransformer;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetTimeZonesController extends QueryBusAccessor
{
    public function __invoke(Request $request): Response
    {
        $timeZones = $this->ask(new GetTimeZones());

        return new JsonResponse(
            [
                'data' => TimeZonesDtoTransformer::toArray($timeZones),
            ],
            Response::HTTP_OK
        );
    }
}
