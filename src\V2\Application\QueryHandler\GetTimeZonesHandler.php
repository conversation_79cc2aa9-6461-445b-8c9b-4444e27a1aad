<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\Service\SettingsService;
use App\V2\Application\DTO\TimeZonesDto;
use App\V2\Application\Query\GetTimeZones;
use App\V2\Domain\Shared\Exception\DefaultTimeZoneException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

readonly class GetTimeZonesHandler
{
    public function __construct(
        protected SettingsService $settingsService
    ) {
    }

    /**
     * @throws InfrastructureException
     */
    public function handle(GetTimeZones $query): TimeZonesDto
    {
        $timeZones = $this->settingsService->get('app.timezones');
        $defaultTimeZone = $this->settingsService->get('app.default_timezone');

        if (!\in_array($defaultTimeZone, $timeZones, true)) {
            throw new DefaultTimeZoneException();
        }

        return new TimeZonesDto(
            timezones: $timeZones,
            default: $defaultTimeZone,
        );
    }
}
