<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Categorize;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Categorize>
 *
 * @method Categorize|null find($id, $lockMode = null, $lockVersion = null)
 * @method Categorize|null findOneBy(array $criteria, array $orderBy = null)
 * @method Categorize[]    findAll()
 * @method Categorize[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CategorizeRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Categorize::class);
    }

    public function add(Categorize $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Categorize $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find a Categorize entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?Categorize
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $categorize = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $categorize;
    }
}
