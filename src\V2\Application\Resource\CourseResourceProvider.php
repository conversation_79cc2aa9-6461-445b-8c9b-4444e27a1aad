<?php

declare(strict_types=1);

namespace App\V2\Application\Resource;

use App\Entity\Course;
use App\Repository\CourseRepository;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;

readonly class CourseResourceProvider implements ResourceProvider
{
    public function __construct(
        private CourseRepository $courseRepository,
    ) {
    }

    public function supports(ResourceType $type): bool
    {
        return ResourceType::Course === $type;
    }

    public function getEntity(Resource $resource): Course
    {
        $course = $this->courseRepository->find($resource->getId()->value());

        if (!$course) {
            throw new ResourceNotFoundException($resource);
        }

        return $course;
    }
}
