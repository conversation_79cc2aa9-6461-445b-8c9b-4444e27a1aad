<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\VirtualMeeting;

use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use App\V2\Infrastructure\VirtualMeeting\VirtualMeetingTypeTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class VirtualMeetingTypeTransformerTest extends TestCase
{
    public function testFromVirtualMeetingTypeToStringWithFixed(): void
    {
        $result = VirtualMeetingTypeTransformer::fromVirtualMeetingTypeToString(VirtualMeetingType::Fixed);

        $this->assertSame('fixed', $result);
    }

    #[DataProvider('virtualMeetingTypeProvider')]
    public function testAllVirtualMeetingTypesAreHandled(VirtualMeetingType $type): void
    {
        $this->assertIsString(
            VirtualMeetingTypeTransformer::fromVirtualMeetingTypeToString($type)
        );
    }

    public static function virtualMeetingTypeProvider(): \Generator
    {
        foreach (VirtualMeetingType::cases() as $type) {
            yield [$type];
        }
    }
}
