<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\VirtualMeeting;

use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use App\V2\Infrastructure\Shared\DateTimeTransformer;

class VirtualMeetingDTOTransformer
{
    /**
     * Transforms an array of parameters into a VirtualMeetingDTO object.
     *
     * @param array $parameters The parameters to transform
     *
     * @return VirtualMeetingDTO The transformed DTO
     */
    public static function fromPayload(array $parameters): VirtualMeetingDTO
    {
        return new VirtualMeetingDTO(
            type: self::transformType($parameters['type']),
            startAt: DateTimeTransformer::fromInput($parameters['start_at']),
            finishAt: DateTimeTransformer::fromInput($parameters['finish_at']),
            url: $parameters['url'] ?? null,
        );
    }

    /**
     * Transforms a string type to a VirtualMeetingType enum value.
     *
     * @param string $type The type as a string
     *
     * @return VirtualMeetingType The corresponding enum value
     */
    private static function transformType(string $type): VirtualMeetingType
    {
        return match (strtolower($type)) {
            'fixed' => VirtualMeetingType::Fixed,
            default => throw new \InvalidArgumentException(\sprintf('Invalid virtual meeting type: %s', $type)),
        };
    }
}
