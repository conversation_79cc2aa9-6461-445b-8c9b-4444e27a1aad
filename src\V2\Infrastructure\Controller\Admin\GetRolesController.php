<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetRoles;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Validator\Admin\GetRolesValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetRolesController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(Request $request): Response
    {
        $queryParameters = $request->query->all();
        GetRolesValidator::validateGetRolesRequest($queryParameters);
        $allowedToManage = isset($queryParameters['allowed_to_manage']) && 'true' === $queryParameters['allowed_to_manage'];
        $user = RequestAttributeExtractor::extractUser($request);

        $roles = $this->ask(new GetRoles(
            requestUser: $user,
            allowedToManage: $allowedToManage,
        ));

        return new JsonResponse(
            data: $roles,
            status: Response::HTTP_OK,
        );
    }
}
