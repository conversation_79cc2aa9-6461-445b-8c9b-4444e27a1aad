<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\User;
use App\Repository\AnnouncementGroupRepository as LegacyAnnouncementGroupRepository;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Repository\AnnouncementUserRepository as LegacyAnnouncementUserRepository;
use App\V2\Application\Command\DeleteAnnouncementGroupUserCommand;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationServiceInterface;
use App\V2\Domain\Announcement\Exception\AnnouncementGroupNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\DeleteAnnouncementGroupUserCommandHandlerException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Filter\FilterCollection;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class DeleteAnnouncementGroupUserCommandHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private LegacyAnnouncementRepository $legacyAnnouncementRepository,
        private LegacyAnnouncementGroupRepository $legacyAnnouncementGroupRepository,
        private LegacyAnnouncementUserRepository $legacyAnnouncementUserRepository,
        private AnnouncementAuthorizationServiceInterface $announcementAuthorizationService,
    ) {
        // TODO: Migrate legacy repositories to V2
    }

    /**
     * @throws ManagerNotAuthorizedException
     * @throws AnnouncementGroupNotFoundException
     * @throws InfrastructureException
     * @throws DeleteAnnouncementGroupUserCommandHandlerException
     * @throws AnnouncementNotFoundException
     * @throws UserNotFoundException
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function handle(DeleteAnnouncementGroupUserCommand $command): void
    {
        $announcement = $this->legacyAnnouncementRepository->findOneBy([
            'id' => $command->getAnnouncementId()->value(),
        ]);
        if (null === $announcement) {
            throw new AnnouncementNotFoundException();
        }

        $group = $this->legacyAnnouncementGroupRepository->findOneBy([
            'id' => $command->getGroupId()->value(),
            'announcement' => $command->getAnnouncementId()->value(),
        ]);

        if (null === $group) {
            throw new AnnouncementGroupNotFoundException();
        }

        $user = $this->getUser($command);

        /**
         * All required entities found.
         **/
        $announcementUser = $this->legacyAnnouncementUserRepository->findOneBy([
            'announcement' => $announcement,
            'user' => $user,
        ]);

        if (null === $announcementUser) {
            throw DeleteAnnouncementGroupUserCommandHandlerException::announcementUserNotFound();
        }

        if ($announcementUser->getAnnouncementGroup()->getId() !== $group->getId()) {
            throw DeleteAnnouncementGroupUserCommandHandlerException::userNotInGroup();
        }

        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $command->getRequestUser(),
            announcement: $announcement,
        );

        $this->legacyAnnouncementUserRepository->delete($announcementUser);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotFoundException
     * @throws CollectionException
     * @throws CriteriaException
     */
    private function getUser(DeleteAnnouncementGroupUserCommand $command): User
    {
        $userCriteria = UserCriteria::createById($command->getUserId());
        // If request user is manager (not admin), validate they can manage this user
        if ($command->getRequestUser()->isManager() && !$command->getRequestUser()->isAdmin()) {
            $userCriteria->filterByFilters(
                filters: new FilterCollection($command->getRequestUser()->getFilters()->toArray()),
                addCreatedBy: $command->getRequestUser()->getId(),
            );
        }

        return $this->userRepository->findOneBy($userCriteria);
    }
}
