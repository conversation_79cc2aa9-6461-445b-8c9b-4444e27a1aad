<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\Chapter;
use App\Entity\Question;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Question|null find($id, $lockMode = null, $lockVersion = null)
 * @method Question|null findOneBy(array $criteria, array $orderBy = null)
 * @method Question[]    findAll()
 * @method Question[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;

    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Question::class);
    }

    /**
     * @return Question[]
     */
    public function findByChapterId(Chapter $chapter)
    {
        return $this->createQueryBuilder('q')
            ->andWhere('q.chapter = :chapter_id')
            ->setParameter('chapter_id', $chapter->getId())
            ->orderBy('q.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function countOriginalQuestions($conditions)
    {
        $query = $this->createQueryBuilder('q')
            ->select('count(q.id)')
            ->andWhere('q.deletedAt IS NULL');

        if (!empty($conditions['category'])) {
            $query->leftJoin('q.chapter', 'ch');
            $query->leftJoin('ch.course', 'c');

            $this->setProfessionalCategoriesQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('q.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('q.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getSingleScalarResult();
    }

    /**
     * Find a Question entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?Question
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $question = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $question;
    }
}
