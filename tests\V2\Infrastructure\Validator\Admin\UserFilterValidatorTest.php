<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\UserFilterValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class UserFilterValidatorTest extends ValidatorTestCase
{
    #[DataProvider('provideValidFiltersIds')]
    public function testValidatePostUserFilters(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        UserFilterValidator::validatePostUserFilters($payload);
    }

    #[DataProvider('provideInvalidFiltersIds')]
    public function testValidateInvalidPostUserFilters(array $payload, array $violations): void
    {
        try {
            UserFilterValidator::validatePostUserFilters($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    #[DataProvider('provideValidFiltersIds')]
    public function testValidateDeleteUserFilters(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        UserFilterValidator::validatePostUserFilters($payload);
    }

    #[DataProvider('provideInvalidFiltersIds')]
    public function testValidateInvalidDeleteUserFilters(array $payload, array $violations): void
    {
        try {
            UserFilterValidator::validatePostUserFilters($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideValidFiltersIds(): \Generator
    {
        yield 'one id' => [
            'payload' => [1, 2, 3, 99999],
        ];
    }

    public static function provideInvalidFiltersIds(): \Generator
    {
        yield 'Empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
            ],
        ];

        yield 'less than 0' => [
            'payload' => [0, -1],
            'violations' => [
                '[0]' => 'This value should be greater than or equal to 1.',
                '[1]' => 'This value should be greater than or equal to 1.',
            ],
        ];

        yield 'different datatype' => [
            'payload' => ['0', '-1'],
            'violations' => [
                '[0]' => [
                    'This value should be of type integer.',
                    'This value should be greater than or equal to 1.',
                ],
                '[1]' => [
                    'This value should be of type integer.',
                    'This value should be greater than or equal to 1.',
                ],
            ],
        ];
    }
}
