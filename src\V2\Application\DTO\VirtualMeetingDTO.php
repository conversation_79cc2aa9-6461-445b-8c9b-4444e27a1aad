<?php

declare(strict_types=1);

namespace App\V2\Application\DTO;

use App\V2\Domain\VirtualMeeting\VirtualMeetingType;

readonly class VirtualMeetingDTO
{
    public function __construct(
        private VirtualMeetingType $type,
        private \DateTimeImmutable $startAt,
        private \DateTimeImmutable $finishAt,
        private ?string $url = null,
    ) {
    }

    public function getType(): VirtualMeetingType
    {
        return $this->type;
    }

    public function getStartAt(): \DateTimeImmutable
    {
        return $this->startAt;
    }

    public function getFinishAt(): \DateTimeImmutable
    {
        return $this->finishAt;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }
}
