<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Purchase;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;

class PurchaseHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof PurchaseCollection || !$criteria instanceof PurchaseHydrationCriteria) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
