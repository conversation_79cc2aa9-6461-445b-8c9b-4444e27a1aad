<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\User\ManagerFilter;

use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Filter as FilterProjection;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterHydrationCriteria;

class ManagerFilterFilterHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly FilterRepository $filterRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof ManagerFilterHydrationCriteria && $criteria->needsFilters();
    }

    /**
     * @throws InfrastructureException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof ManagerFilterCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $filterIds = $collection->reduce(
            callback: function (array $carry, ManagerFilter $managerFilter) {
                $carry[] = $managerFilter->getFilterId();

                return $carry;
            },
            initial: []
        );

        $filterIds = array_unique($filterIds);

        $filters = $this->filterRepository->findBy(
            FilterCriteria::createByIds(new IdCollection($filterIds)),
        );

        $filtersById = $filters->allIndexedById();

        foreach ($collection->all() as $managerFilter) {
            $filter = $filtersById[$managerFilter->getFilterId()->value()] ?? null;
            if (null === $filter) {
                continue;
            }
            $managerFilter->setFilter(
                $this->getFilterProjection($filter)
            );
        }
    }

    private function getFilterProjection(Filter $filter): FilterProjection
    {
        return new FilterProjection(
            id: $filter->getId(),
            name: $filter->getName(),
            categoryId: $filter->getFilterCategoryId(),
        );
    }
}
