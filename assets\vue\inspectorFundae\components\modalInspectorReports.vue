<template>
  <div class="modalInspectorReports">
    <BaseModalInspector
      :identifier="tag + 'modalInspectorReports'"
      :title="`${$t('ANNOUNCEMENT.MODALS.REPORT_TITLE')}`"         
      padding="1rem"
      size="modal-lg"
    >
      <div class="row">
        <div class="col-12 align-self-stretch">
          <ReportBox
            tag="announcement-group-report"
            :title="`${$t('ANNOUNCEMENT.MODALS.REPORT_CHECKALL')}`"
            :options="options"
            @toggle="updateOptions"
            @active="updateSelectAll"
            :value="all"
            class="w-100 h-100"
          />
        </div>
      </div>
      <div
        class="text-center mt-4"
        v-show="exportOptions.excel || exportOptions.pdf"
      >
        <button type="button" class="btn btn-sm btn-primary" @click="generateDocs">
          <i class="fa fa-file mr-2"></i>{{ $t('GENERATE_REPORT') }}
        </button>
      </div>

      <hr v-if="generatedReports.length > 0">
      <div class="row mt-3" style="height: 10rem; overflow: auto" v-if="generatedReports.length > 0">
        <h5 class="text-center w-100">{{ $t('ANNOUNCEMENT.MODALS.REPORT_TITLE') }}</h5>
        <table class="table">
          <thead>
          <tr>
            <th>{{ $t('FILE') }}</th>
            <th>{{ $t('CREATED_BY') }}</th>
            <th>{{ $t('CREATED_AT') }}</th>
            <th>{{ $t('STATUS') }}</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="r in generatedReports" :key="`group-report-${groupId}-generate-report-${r.taskId}`">
            <td>
              <a v-if="zipIsCompleted(r.status)" :href="`/files/${r.filename}`" target="_blank">{{ r.originalName }}</a>
              <span v-else>
              {{ r.originalName }}
            </span>
            </td>
            <td>{{ r.createdByName }}</td>
            <td>{{ getDateTimeFormatted(r.createdAt) }}</td>
            <td class="text-center">
              <loader style="padding: 0 !important;" v-if="zipIsPending(r.status) || zipIsInProgress(r.status)" :is-loaded="zipIsInProgress(r.status)" />
              <span v-if="zipIsCompleted(r.status)" class="badge bg-success text-white">{{ $t('STATUS_INFORMATION.COMPLETED') }}</span>
              <span v-if="zipIsFailed(r.status)" class="badge bg-danger text-white">{{ $t('ERROR') }}</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </BaseModalInspector>
  </div>
</template>

<script>
import BaseModalInspector from "./BaseModalInspector";
import ReportBox from "./reportBox";
import {get} from "vuex-pathify";
import Loader from "../../admin/components/Loader.vue";
import zipFileTaskStatusMixin from "../../common/constants/ZipFileTaskStatus";
import dateTimeFormatterMixin from "../../common/mixins/dateTimeFormatterMixin";
import TaskQueueService from "../../common/services/TaskQueueService";
import TaskQueueMixin from "../../mixins/TaskQueueMixin";

export default {
  name: "modalInspectorReports",
  components: {Loader, ReportBox, BaseModalInspector },
  mixins: [zipFileTaskStatusMixin, dateTimeFormatterMixin, TaskQueueMixin],
  props: {
    tag: { type: String, default: "" },
    groupId: { type: Number, default: 0 },
    announcementId: { type: Number, default: undefined },
    announcementType: { type: String, default: ''}
  },
  data() {
    return {
      availableForDownload: false,
      reportCreated: false,
      reportStatus: 0,// 0 Pending, 1 in progress, -1 failed, 2 successful (show datetime)
      reportStatusAsString: null,
      reportDate: null,
      intervalId: null,

      onsiteOptions: [
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_GROUP_RESUME_INDIVIDUAL')}:`,
          options: [
            {
              key: "resumeIndividual",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_GROUP_RESUME_INDIVIDUAL_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION1')}:`,
          options: [
            {
              key: "groupInfo",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_GROUP_INFO_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_CONNECTIONS')}:`,
          options: [
            {
              key: "connections",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_CONNECTIONS_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC4')}:`,
          options: [
            {
              key: "assistance",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_ASSISTANCE_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_SURVEY')}:`,
          options: [
            {
              key: "survey",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_SURVEY_DESC')}`,
              active: false,
            },
          ],
        },

      ],
      onlineOptions: [
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_GROUP_RESUME_INDIVIDUAL')}:`,
          options: [
            {
              key: "resumeIndividual",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_GROUP_RESUME_INDIVIDUAL_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION1')}:`,
          options: [
            {
              key: "groupInfo",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_GROUP_INFO_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_CONNECTIONS')}:`,
          options: [
            {
              key: "connections",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_CONNECTIONS_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_SURVEY')}:`,
          options: [
            {
              key: "survey",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_SURVEY_DESC')}`,
              active: false,
            },
          ],
        },

        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_CHAT')}:`,
          options: [
            {
              key: "chat",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC3')}`,
              active: false,
            },
          ],
        },// pENDING FIX
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_FORUM')}:`,
          options: [
            {
              key: "forum",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_FORUM_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION3')}:`,
          options: [
            {
              key: "activities",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_ACTIVITIES_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_MATERIALS')}:`,
          options: [
            {
              key: "materials",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_MATERIALS_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_NOTIFICATIONS')}:`,
          options: [
            {
              key: "notifications",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_NOTIFICATIONS_DESC')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_TASKS')}:`,
          options: [
            {
              key: "tasks",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_TASKS_DESC')}`,
              active: false,
            },
          ],
        },
      ],
      // options: [],
      all: false,

      excelOptions: [
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION1')}:`,
          options: [
            {
              key: "groupData",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC1')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.FORUM')}:`,
          options: [
            {
              key: "forum",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC2')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.INFOTAB.CHAT')}:`,
          options: [
            {
              key: "chat",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC3')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION2')}:`,
          options: [
            {
              key: "videoconference",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC4')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('CONNECTIONS')}:`,
          options: [
            {
              key: "connection",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC5')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION3')}:`,
          options: [
            {
              key: "activities",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC6')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.INFOTAB.MESSAGES')}:`,
          options: [
            {
              key: "messages",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC7')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION4')}:`,
          options: [
            {
              key: "teaching",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC8')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION5')}:`,
          options: [
            {
              key: "assistance",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC4')}`,
              active: false,
            },
          ],
        },
      ],
      pdfOptions: [
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION1')}:`,
          options: [
            {
              key: "groupData",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC1')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.FORUM')}:`,
          options: [
            {
              key: "forum",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC2')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.INFOTAB.CHAT')}:`,
          options: [
            {
              key: "chat",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC3')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION2')}:`,
          options: [
            {
              key: "videoconference",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC4')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('CONNECTIONS')}:`,
          options: [
            {
              key: "connection",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC5')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION3')}:`,
          options: [
            {
              key: "activities",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC6')}`,
              active: false,
            },
            {
              key: "activitiesDetails",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC9')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.INFOTAB.MESSAGES')}:`,
          options: [
            {
              key: "messages",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC7')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION4')}:`,
          options: [
            {
              key: "teaching",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC8')}`,
              active: false,
            },
            {
              key: "teacherInterventions",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC10')}`,
              active: false,
            },
          ],
        },
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_OPTION5')}:`,
          options: [
            {
              key: "assistance",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT_DESC4')}`,
              active: false,
            },
          ],
        },

      ],
      exportOptions: { excel: false, pdf: true },
    };
  },
  computed: {
    options() {
      if (this.announcementType === 'on_site') return this.onsiteOptions;
      if (this.announcementType === 'online') return this.onlineOptions;
      return [];
    },
    allGeneratedReports: get("announcementReportModule/groupsReports"),
    generatedReports() {
      return this.allGeneratedReports[this.groupId] ?? [];
    }
  },
  watch: {
    groupId: {
      immediate: true,
      handler: function (val, oldVal) {
        if (this.groupId > 0){
          this.initOptions();
        }
      }
    }
  },
  beforeDestroy() {
    if (this.intervalId != null) clearInterval(this.intervalId);
  },
  methods: {
    initOptions() {
      let data = [
        {
          title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.GROUP_INFO')}:`,
          options: [
            {
              key: "groupInfo",
              description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.GROUP_INFO_DESC')}`,
              active: false,
            },
          ],
        }
      ];
      data.push({
        title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.CONNECTIONS')}:`,
        options: [
          {
            key: "connections",
            description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.CONNECTIONS_DESC')}`,
            active: false,
          },
        ],
      });
      data.push({
        title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.ASSISTANCE')}:`,
        options: [
          {
            key: "assistance",
            description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.ASSISTANCE_DESC')}`,
            active: false,
          },
        ],
      });
      data.push({
        title: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.SURVEY')}:`,
        options: [
          {
            key: "survey",
            description: `${this.$t('ANNOUNCEMENT.MODALS.REPORT.SURVEY_DESC')}`,
            active: false,
          },
        ],
      });

      // this.options = data;
    },

    updateOptions(data) {
      this.updateOption("options", data);
    },

    updateExcelOption(data) {
      console.log(data);
      this.updateOption("options", data);
    },

    updatePDFOption(data) {
      this.updateOption("pdfOptions", data);
    },

    updateSelectAll(value) { 
      this.all = value;
      if (!value) {
        if (this.announcementType === 'on_site'){
          this.onsiteOptions.forEach(option => {
            option.options[0].active = value;
          });
        }

        if (this.announcementType === 'online'){
          this.onlineOptions.forEach(option => {
            option.options[0].active = value;
          });
        }
      }
    },
    updatePDFBoxOptions(value) {
      this.exportOptions.pdf = value;
    },
    updateOption(objKey, data) {
      const [cardIndex, optionIndex, value] = data;
      this[objKey][cardIndex].options[optionIndex].active = value;
    },
    getActiveKeys(objKey) {
      return this[objKey].reduce(
        (acc, cur) => ({
          ...acc,
          ...(cur.options.filter((option) => option.active) || []).reduce(
            (acc2, option) => ({ ...acc2, [option.key]: true }),
            {}
          ),
        }),
        {}
      );
    },

    generateDocs() {
      const data = {
        ...{
          announcementId: this.announcementId,
          groupId: this.groupId
        },
        ...this.getActiveKeys('options')
      };

      this.enqueueTask({
        url: `admin/generate-report/announcement/${this.announcementId}`,
        data: data,
        messages: {
          success: this.$t('Reporte generado con éxito'),
          error: this.$t('Error al generar el reporte')
        },
        onSuccess: () => {
          this.$store.dispatch('announcementReportModule/loadAnnouncementGroupReports', this.announcementId);
        }
      });
    },

    downloadReport() {
      this.$store.dispatch('announcementModule/downloadAnnouncementGroupReport', this.groupId);
      // const link = document.createElement("a");
      // link.href = data;
      // link.download = fileName;
      // link.click();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.modalInspectorReports {
  .gap {
    gap: 1rem;
  }
}
</style>
