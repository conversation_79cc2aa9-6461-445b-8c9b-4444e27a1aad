<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetFiltersQuery;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Filter\FilterCriteriaTransformer;
use App\V2\Infrastructure\Filter\FilterTransformer;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Admin\FilterValidator;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetFiltersController extends QueryBusAccessor
{
    /**
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(Request $request): Response
    {
        $params = $request->query->all();

        FilterValidator::validateGetFilters($params);

        $criteria = FilterCriteriaTransformer::fromArray($params);

        $user = RequestAttributeExtractor::extractUser($request);

        $collection = $this->ask(
            new GetFiltersQuery(
                criteria: $criteria,
                requestedBy: $user,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                FilterTransformer::fromCollectionToArray($collection)
            )->toArray(),
            status: Response::HTTP_OK
        );
    }
}
