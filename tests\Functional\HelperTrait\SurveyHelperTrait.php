<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\NpsQuestion;
use App\Entity\Survey;
use App\Entity\User;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create, update, and delete Course.
 * Assumes that if your Course requires a TypeCourse, it is injected as a parameter
 * or created separately using the TypeCourseHelperTrait.
 */
trait SurveyHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetSurvey(
        string $name = 'Test Season',
        bool $active = true,
        string $description = 'Test description',
        int $applyTo = 1,
        ?bool $isMain = false,
        ?User $createdBy = null,
    ): Survey {
        $em = $this->getEntityManager();
        $survey = new Survey();
        $survey
            ->setName($name)
            ->setActive($active)
            ->setDescription($description)
            ->setApplyTo($applyTo)
            ->setIsMain($isMain)
            ->setCreatedBy($createdBy)
            ->setCreatedAt(new \DateTime());
        $em->persist($survey);
        $npsQuestion = new NpsQuestion();
        $npsQuestion->setSurvey($survey)
            ->setQuestion('Test Question')
            ->setType('text')
            ->setActive(true)
            ->setMain(true)
            ->setPosition(1)
            ->setSource(1)
            ->setCreatedBy($createdBy)
            ->setCreatedAt(new \DateTime());
        $em->persist($npsQuestion);
        $em->flush();

        return $survey;
    }
}
