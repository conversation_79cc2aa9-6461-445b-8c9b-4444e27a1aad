<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\DeleteAnnouncementGroupUserCommand;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class DeleteAnnouncementGroupUserController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(Request $request, int $announcementId, int $groupId, int $userId): Response
    {
        IdValidator::validateId($announcementId);
        IdValidator::validateId($groupId);
        IdValidator::validateId($userId);

        $user = RequestAttributeExtractor::extractUser($request);

        $this->execute(
            new DeleteAnnouncementGroupUserCommand(
                announcementId: new Id($announcementId),
                groupId: new Id($groupId),
                userId: new Id($userId),
                requestUser: $user,
            )
        );

        return new JsonResponse(
            status: Response::HTTP_NO_CONTENT,
        );
    }
}
