<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Task;
use App\Entity\User;

class TaskMother
{
    public const string DEFAULT_TASK = 'export-file';

    public static function create(
        ?int $id = null,
        ?string $taskName = null,
        ?string $type = null,
        ?int $status = null,
        ?array $params = null,
        ?User $createdBy = null,
        ?\DateTimeInterface $createdAt = null,
        ?\DateTimeInterface $finishedAt = null,
        ?\DateTimeInterface $startedAt = null,
    ): Task {
        $task = new Task();

        if (null !== $id) {
            $task->setId($id);
        }

        $task->setTask($taskName ?? self::DEFAULT_TASK)
            ->setType($type)
            ->setStatus($status ?? Task::TASK_PENDING)
            ->setCreatedAt(new \DateTime())
            ->setFinishedAt($finishedAt)
            ->setStartedAt($startedAt)
            ->setParams($params)
            ->setCreatedBy($createdBy)
        ;

        return $task;
    }
}
