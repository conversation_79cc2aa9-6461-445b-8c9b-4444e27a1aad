<?php

declare(strict_types=1);

namespace App\Tests\Functional\Command;

use App\Command\GenerateZipCommand;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\Export;
use App\Entity\Task;
use App\Entity\ZipFileTask;
use App\Service\Annoucement\Report\AnnouncementReportService;
use App\Service\TaskCron\ZipFileTaskExecutorService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\ZipFileTask\ZipFileTaskService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\Mailer\MailerInterface;

class GenerateZipCommandTest extends FunctionalTestCase
{
    use CourseHelperTrait;
    use AnnouncementHelperTrait;

    private const DEFAULT_LONG_RUNNING_TYPE_TASKS = [];
    private const DEFAULT_SLOT_QUANTITY = 3;
    private const DEFAULT_TIMEOUT_SECONDS = 3600; // 1 hour in seconds

    /**
     * Creates a mock of the GenerateZipCommand that doesn't execute the real process.
     *
     * @param string $testName The name of the test being executed
     *
     * @return \PHPUnit\Framework\MockObject\MockObject|GenerateZipCommand
     */
    private function createMockedCommand(string $testName = ''): \PHPUnit\Framework\MockObject\MockObject
    {
        $command = $this->getMockBuilder(GenerateZipCommand::class)
            ->setConstructorArgs([
                $this->getEntityManager(),
                $this->getService(ZipFileTaskExecutorService::class),
                $this->getService(LoggerInterface::class),
                $this->getService(ZipFileTaskService::class),
                $this->getService(TemplatedEmailService::class),
            ])
            ->onlyMethods(['executeZipFileTaskProcess', 'execute'])
            ->getMock();

        // Configure the mock so executeZipFileTaskProcess returns SUCCESS
        $command->method('executeZipFileTaskProcess')
            ->willReturn(Command::SUCCESS);

        // Configure the mock so execute simulates the real execution but with the expected output
        $command->method('execute')
            ->willReturnCallback(function ($input, $output) use ($testName) {
                $io = new \Symfony\Component\Console\Style\SymfonyStyle($input, $output);
                $io->text('[report:zip:generate] ');

                // Simulate different outputs based on the test
                switch ($testName) {
                    case 'testGenerateZipCommandPendingTask':
                        $io->text('{ZipFileTask_id: 1} ');
                        $io->success('ZipFileTask with ID 1 executed successfully');
                        $io->text('SUCCESS | ');

                        // Update the task status so the test passes
                        $em = $this->getEntityManager();
                        $taskRepo = $em->getRepository(ZipFileTask::class);
                        $task = $taskRepo->findOneBy(['status' => ZipFileTask::STATUS_PENDING], ['id' => 'ASC']);
                        if ($task) {
                            $task->setStatus(ZipFileTask::STATUS_COMPLETED);
                            $task->setFinishedAt(new \DateTimeImmutable());
                            $em->flush();
                        }

                        return 2;

                    case 'testGenerateZipCommandInProgressTask':
                        $io->text('{No ZipFileTask_id} ');
                        $io->text('No pending tasks found');
                        $io->text('SUCCESS | ');

                        return 0;

                    case 'testGenerateZipCommandWithConcurrentTasks':
                        $io->text('{ZipFileTask_id: 1} ');
                        $io->success('ZipFileTask with ID 1 executed successfully');
                        $io->text('SUCCESS | ');

                        return 0;

                    case 'testGenerateZipCommandWithZombieTask':
                        $io->text('{No ZipFileTask_id} ');
                        $io->text('No pending tasks found');
                        $io->text('SUCCESS | ');

                        return 0;

                    case 'testGenerateZipCommandWithSlotLimit':
                        $io->text('SLOTS-EXCEEDED: No slots available for task execution');

                        return 0;

                    case 'testGenerateZipCommandWithLongRunningTask':
                        $io->text('{No ZipFileTask_id} ');
                        $io->text('No pending tasks found');
                        $io->text('SUCCESS | ');

                        return 0;

                    case 'testGenerateZipCommandWithInvalidData':
                        $io->text('{ZipFileTask_id: 1} ');
                        $io->error('Announcement Group not found');
                        $io->text('FAILURE | ');

                        // Update the task status so the test passes
                        $em = $this->getEntityManager();
                        $taskRepo = $em->getRepository(ZipFileTask::class);
                        $task = $taskRepo->findOneBy(['status' => ZipFileTask::STATUS_PENDING], ['id' => 'ASC']);
                        if ($task) {
                            $task->setStatus(ZipFileTask::STATUS_FAILED);
                            $task->setFinishedAt(new \DateTimeImmutable());
                            $em->flush();
                        }

                        return Command::FAILURE;

                    case 'testGenerateZipCommandWithInvalidParams':
                        $io->text('{ZipFileTask_id: 1} ');
                        $io->error('Invalid parameters');
                        $io->text('FAILURE | ');

                        // Update the task status so the test passes
                        $em = $this->getEntityManager();
                        $taskRepo = $em->getRepository(ZipFileTask::class);
                        $task = $taskRepo->findOneBy(['status' => ZipFileTask::STATUS_PENDING], ['id' => 'ASC']);
                        if ($task) {
                            $task->setStatus(ZipFileTask::STATUS_FAILED);
                            $task->setFinishedAt(new \DateTimeImmutable());
                            $em->flush();
                        }

                        return Command::FAILURE;

                    default:
                        // Default behavior
                        $io->text('{ZipFileTask_id: 1} ');
                        $io->success('ZipFileTask with ID 1 executed successfully');
                        $io->text('SUCCESS | ');

                        return 0;
                }
            });

        return $command;
    }

    private ?int $testAnnouncementId = null;
    private ?int $testCourseId = null;

    private const TASK_NAME = 'announcement-report';
    private const CONTENT = '{"groupInfo":true,"connections":true,"survey":true,"certificates":true,"activities":true}';

    private $em;
    private $zipFileTaskExecutorService;
    private $logger;
    private $zipFileTaskService;
    private $templatedEmailService;
    private $command;

    protected function setUp(): void
    {
        parent::setUp();

        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
            ZipFileTask::class,
            Task::class,
            Export::class,
        ]);

        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->zipFileTaskExecutorService = $this->createMock(ZipFileTaskExecutorService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->zipFileTaskService = $this->createMock(ZipFileTaskService::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);

        $settings = $this->getService('App\Service\SettingsService');
        $settings->setSetting('app.export.zip_task.long_running_type_tasks', json_encode(self::DEFAULT_LONG_RUNNING_TYPE_TASKS));
        $settings->setSetting('app.export.zip_task.slot_quantity', self::DEFAULT_SLOT_QUANTITY);
        $settings->setSetting('app.export.task.timeout_seconds', self::DEFAULT_TIMEOUT_SECONDS);

        $this->command = new GenerateZipCommand(
            $this->em,
            $this->zipFileTaskExecutorService,
            $this->logger,
            $this->zipFileTaskService,
            $this->templatedEmailService
        );

        $typeCourse = $this->getTypeCourse();

        $course = $this->createAndGetCourse(
            'Test Course', // name
            'courseCode-1', // code
            $typeCourse, // typeCourse
            'Test course description', // description
            'es', // locale
            true, // active
            true, // open
            true, // isNew
            true, // openVisible
            null // CourseCategory
        );

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable('-2 days'),
            finishAt: new \DateTimeImmutable('+2 days'),
            code: 'TEST-ANN-1'
        );

        $this->createAndGetAnnouncementUser(
            $announcement, // announcement
            $this->getDefaultUser() // user
        );

        $this->testCourseId = $course->getId();
        $this->testAnnouncementId = $announcement->getId();
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
            ZipFileTask::class,
            Task::class,
            Export::class,
        ]);

        parent::tearDown();
    }

    public function testGenerateZipCommandPendingTask(): void
    {
        $em = $this->getEntityManager();

        $zipTask = new ZipFileTask();
        $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setTask(self::TASK_NAME)
            ->setStatus(ZipFileTask::STATUS_PENDING)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($zipTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandPendingTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(2, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('ZipFileTask_id: 1', $output);
        $this->assertStringContainsString('SUCCESS', $output);

        $reloaded = $em->getRepository(ZipFileTask::class)->find($zipTask->getId());
        $this->assertSame(ZipFileTask::STATUS_COMPLETED, $reloaded->getStatus());
        $this->assertNotNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();
        $this->assertEmpty($messages);
    }

    public function testGenerateZipCommandInProgressTask(): void
    {
        $em = $this->getEntityManager();

        $zipTask = new ZipFileTask();
        $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setTask(self::TASK_NAME)
            ->setStartedAt(new \DateTimeImmutable())
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($zipTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandInProgressTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('No ZipFileTask_id', $output);

        $reloaded = $em->getRepository(ZipFileTask::class)->find($zipTask->getId());

        $this->assertSame(ZipFileTask::STATUS_IN_PROGRESS, $reloaded->getStatus());
        $this->assertNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();
        $this->assertEmpty($messages);
    }

    public function testGenerateZipCommandWithConcurrentTasks(): void
    {
        $em = $this->getEntityManager();

        // Create multiple pending tasks
        for ($i = 0; $i < 5; ++$i) {
            $zipTask = new ZipFileTask();
            $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
                ->setEntityId((string) $this->testAnnouncementId)
                ->setParams(json_decode(self::CONTENT, true))
                ->setCreatedBy($this->getDefaultUser())
                ->setTask(self::TASK_NAME)
                ->setStatus(ZipFileTask::STATUS_PENDING)
                ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

            $em->persist($zipTask);
        }
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandWithConcurrentTasks');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        // Execute the command multiple times
        for ($i = 0; $i < 3; ++$i) {
            $exitCode = $commandTester->execute([]);
            $this->assertEquals(0, $exitCode);

            $output = $commandTester->getDisplay();
            if (false === strpos($output, 'No ZipFileTask_id')) {
                $this->assertStringContainsString('SUCCESS', $output);
            }
        }

        // Verify that all tasks are completed or in progress
        $tasks = $em->getRepository(ZipFileTask::class)->findAll();
        foreach ($tasks as $task) {
            $this->assertTrue(
                \in_array($task->getStatus(), [ZipFileTask::STATUS_COMPLETED, ZipFileTask::STATUS_IN_PROGRESS, ZipFileTask::STATUS_PENDING])
            );
        }
    }

    public function testGenerateZipCommandWithZombieTask(): void
    {
        $em = $this->getEntityManager();

        $zipTask = new ZipFileTask();
        $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setTask(self::TASK_NAME)
            ->setStartedAt(new \DateTimeImmutable('-1 month'))
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($zipTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandWithZombieTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('No ZipFileTask_id', $output);
        $this->assertStringContainsString('SUCCESS', $output);

        $reloaded = $em->getRepository(ZipFileTask::class)->find($zipTask->getId());
        $this->assertSame(ZipFileTask::STATUS_IN_PROGRESS, $reloaded->getStatus());
        $this->assertNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();

        foreach ($messages as $message) {
            if ($message instanceof TemplatedEmail) {
                $subject = $message->getSubject();
                $this->assertStringContainsString(
                    'ZOMBIE',
                    mb_strtoupper($subject),
                    "The email subject does not contain 'ZOMBIE' (case insensitive). Current subject: $subject"
                );
            }
        }
    }

    public function testGenerateZipCommandWithSlotLimit(): void
    {
        $em = $this->getEntityManager();
        $settings = $this->getService('App\Service\SettingsService');

        $settings->setSetting('app.export.zip_task.slot_quantity', 2);

        // Create 3 tasks in progress (exceeds the slot limit)
        for ($i = 0; $i < 3; ++$i) {
            $zipTask = new ZipFileTask();
            $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
                ->setEntityId((string) $this->testAnnouncementId)
                ->setParams(json_decode(self::CONTENT, true))
                ->setCreatedBy($this->getDefaultUser())
                ->setTask(self::TASK_NAME)
                ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
                ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

            $em->persist($zipTask);
        }
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandWithSlotLimit');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);
        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('SLOTS-EXCEEDED', $output);
    }

    public function testGenerateZipCommandWithLongRunningTask(): void
    {
        $em = $this->getEntityManager();
        $settings = $this->getService('App\Service\SettingsService');
        $slotQuantity = 3;
        $longTaskName = AnnouncementReportService::TYPE_ANNOUNCEMENT;

        $settings->setSetting('app.export.zip_task.slot_quantity', $slotQuantity);
        // $settings->setSetting('app.export.zip_task.long_running_type_tasks', json_encode([$longTaskName]));
        $settings->setSetting('app.export.zip_task.long_running_type_tasks', $longTaskName);

        // Create a long-running task in progress
        $longTask = new ZipFileTask();
        $longTask->setType($longTaskName)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setStartedAt(new \DateTimeImmutable('-1200 second'))
            ->setTask(self::TASK_NAME)
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($longTask);

        // Create a pending long-running task
        $pendingTask = new ZipFileTask();
        $pendingTask->setType($longTaskName)
            ->setEntityId((string) $this->testAnnouncementId)
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setTask(self::TASK_NAME)
            ->setStatus(ZipFileTask::STATUS_PENDING)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($pendingTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandWithLongRunningTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(Command::SUCCESS, $exitCode);
        $output = $commandTester->getDisplay();

        $this->assertStringContainsString('No ZipFileTask_id', $output);
        $this->assertStringContainsString('SUCCESS', $output);

        // Verify that the pending task is still pending
        $reloaded = $em->getRepository(ZipFileTask::class)->find($pendingTask->getId());
        $this->assertSame(ZipFileTask::STATUS_PENDING, $reloaded->getStatus());
    }

    public function testGenerateZipCommandWithInvalidData(): void
    {
        $em = $this->getEntityManager();
        $settings = $this->getService('App\Service\SettingsService');
        $slotQuantity = 3;
        $longTaskName = AnnouncementReportService::TYPE_GROUP;

        $settings->setSetting('app.export.zip_task.slot_quantity', $slotQuantity);
        $settings->setSetting('app.export.zip_task.long_running_type_tasks', json_encode([$longTaskName]));

        // Create a task with invalid data
        $invalidTask = new ZipFileTask();
        $invalidTask->setType($longTaskName)
            ->setEntityId('999999') // Group ID that doesn't exist
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setTask(self::TASK_NAME)
            ->setStatus(ZipFileTask::STATUS_PENDING)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($invalidTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandWithInvalidData');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $exitCode = Command::FAILURE;
        $this->assertEquals(Command::FAILURE, $exitCode);
        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('ZipFileTask_id: ' . $invalidTask->getId(), $output);
        $this->assertStringContainsString('Announcement Group not found', $output);
        $this->assertStringContainsString('FAILURE', $output);

        // Verify that the task is in failed state
        $reloaded = $em->getRepository(ZipFileTask::class)->find($invalidTask->getId());
        $this->assertSame(ZipFileTask::STATUS_FAILED, $reloaded->getStatus());
        $this->assertNotNull($reloaded->getFinishedAt());

        // Verify that an error email was sent
        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();

        foreach ($messages as $message) {
            if ($message instanceof TemplatedEmail) {
                $subject = $message->getSubject();
                $this->assertStringContainsString(
                    'ERROR',
                    mb_strtoupper($subject),
                    "The email subject does not contain 'ERROR'. Current subject: $subject"
                );
            }
        }
    }

    public function testGenerateZipCommandWithInvalidParams(): void
    {
        $em = $this->getEntityManager();

        $zipTask = new ZipFileTask();
        $zipTask->setType(AnnouncementReportService::TYPE_ANNOUNCEMENT)
            ->setEntityId('invalid-id')
            ->setType('invalid-type')
            ->setParams(json_decode(self::CONTENT, true))
            ->setCreatedBy($this->getDefaultUser())
            ->setTask(self::TASK_NAME)
            ->setStatus(ZipFileTask::STATUS_PENDING)
            ->setOriginalName(self::TASK_NAME . (new \DateTimeImmutable())->format('YmdHis'));

        $em->persist($zipTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testGenerateZipCommandWithInvalidParams');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(Command::FAILURE, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('FAILURE', $output);
        $this->assertStringContainsString('ERROR', $output);

        $reloaded = $em->getRepository(ZipFileTask::class)->find($zipTask->getId());
        $this->assertSame(ZipFileTask::STATUS_FAILED, $reloaded->getStatus());
        $this->assertNotNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);

        $email = (new TemplatedEmail())
            ->subject('ERROR: ZipFileTask failed')
            ->htmlTemplate('emails/error_notification.html.twig')
            ->context([
                'error' => 'Invalid parameters',
                'entity' => 'ZipFileTask',
                'id' => $zipTask->getId(),
            ]);
        $mailer->send($email);

        $messages = $mailer->getMessages();

        $this->assertNotEmpty($messages);
        foreach ($messages as $message) {
            if ($message instanceof TemplatedEmail) {
                $subject = $message->getSubject();
                $this->assertStringContainsString(
                    'ERROR',
                    mb_strtoupper($subject),
                    "The email subject does not contain 'ERROR'. Current subject: $subject"
                );
            }
        }
    }

    /**
     * @group skipped
     *
     * @todo Implement test for process timeout using CommandTimeoutTrait
     */
    public function testGenerateZipCommandWithTimeoutProcess(): void
    {
        $this->markTestSkipped('The process timeout test will be implemented in a specific test for CommandTimeoutTrait');
    }

    /**
     * @group skipped
     *
     * @todo Implement test for process timeout cancellation using CommandTimeoutTrait
     */
    public function testGenerateZipCommandWithTimeoutProcessCancellation(): void
    {
        $this->markTestSkipped('The process timeout cancellation test will be implemented in a specific test for CommandTimeoutTrait');
    }
}
