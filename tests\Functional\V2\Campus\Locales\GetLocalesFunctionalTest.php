<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Campus\Locales;

use App\Entity\Setting;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Backend\BackendLocalesEndpoints;
use Doctrine\ORM\Exception\NotSupported;
use Symfony\Component\HttpFoundation\Response;

class GetLocalesFunctionalTest extends FunctionalTestCase
{
    /**
     * @throws NotSupported
     */
    public function testCampusLocales(): void
    {
        $newLAdminLocales = ['fr', 'jp'];
        $settingsRepository = $this->getRepository(Setting::class);
        $defaultAdminLocales = $settingsRepository->findOneBy(['code' => 'app.languages'])->getValue();

        $settingsService = static::getContainer()->get('App\Service\SettingsService');
        $settingsService->setSetting('app.languages', json_encode($newLAdminLocales));
        $settingsService->loadSettings();

        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendLocalesEndpoints::campusLocalesEndpoint(),
        );
        $responseData = $this->extractResponseData($response);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertNotEmpty($responseData);
        $this->assertIsArray($responseData);

        foreach ($responseData as $locale) {
            $this->assertIsString($locale);
            $this->assertContains($locale, $newLAdminLocales);
        }

        $settingsService->setSetting('app.languages', $defaultAdminLocales);
    }
}
