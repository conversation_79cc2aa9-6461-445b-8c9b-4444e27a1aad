<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Campus\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Campus\CampusPurchaseEndpoints;
use App\Tests\Functional\HelperTrait\PurchasableItemHelperTrait;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetPurchasesFunctionalTest extends FunctionalTestCase
{
    use PurchasableItemHelperTrait;

    private PurchaseRepository $purchaseRepository;
    private User $testUser;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        /** @var PurchaseRepository $purchaseRepository */
        $purchaseRepository = $this->getService(PurchaseRepository::class);
        $this->purchaseRepository = $purchaseRepository;

        $this->testUser = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'test.purchases.' . uniqid() . '@example.com',
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        if (!empty($this->testUser)) {
            $this->hardDeleteUsersByIds([$this->testUser->getId()]);
        }

        parent::tearDown();
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testGetPurchasesSuccessfully(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create multiple purchases for the user
        $purchase1 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
        );

        $purchase2 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 3000, currency: Currency::EUR())
        );

        $purchase3 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Cancelled,
            amount: MoneyMother::create(amount: 2000, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($purchase1);
        $this->purchaseRepository->put($purchase2);
        $this->purchaseRepository->put($purchase3);

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertIsArray($responseData['data']);
        $this->assertCount(3, $responseData['data']);

        // Verify the purchases data
        $purchaseIds = array_map(
            fn (array $purchase) => $purchase['id'],
            $responseData['data']
        );

        $this->assertContains($purchase1->getId()->value(), $purchaseIds);
        $this->assertContains($purchase2->getId()->value(), $purchaseIds);
        $this->assertContains($purchase3->getId()->value(), $purchaseIds);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testGetPurchasesWithPagination(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create multiple purchases for the user
        $purchases = [];
        for ($i = 0; $i < 5; ++$i) {
            $purchase = PurchaseMother::create(
                userId: new Id($this->testUser->getId()),
                status: PurchaseStatus::Completed,
                amount: MoneyMother::create(amount: 1000 * ($i + 1), currency: Currency::EUR())
            );
            $this->purchaseRepository->put($purchase);
            $purchases[] = $purchase;
        }

        // Request first page with 2 items
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . '?page=1&page_size=2',
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(2, $responseData['data']);

        $this->assertEquals($purchases[0]->getId()->value(), $responseData['data'][0]['id']);
        $this->assertEquals($purchases[1]->getId()->value(), $responseData['data'][1]['id']);

        // Request second page
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . '?page=2&page_size=2',
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(2, $responseData['data']);

        $this->assertEquals($purchases[2]->getId()->value(), $responseData['data'][0]['id']);
        $this->assertEquals($purchases[3]->getId()->value(), $responseData['data'][1]['id']);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testGetPurchasesWithFiltering(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create purchases with different statuses
        $completedPurchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
        );

        $pendingPurchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 3000, currency: Currency::EUR())
        );

        $cancelledPurchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Cancelled,
            amount: MoneyMother::create(amount: 2000, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($completedPurchase);
        $this->purchaseRepository->put($pendingPurchase);
        $this->purchaseRepository->put($cancelledPurchase);

        // Filter by status
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . '?status=completed',
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(1, $responseData['data']);
        $this->assertEquals($completedPurchase->getId()->value(), $responseData['data'][0]['id']);
        $this->assertEquals('completed', $responseData['data'][0]['status']);

        // Filter by amount range
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . '?amount_min=2500&amount_max=4000',
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(1, $responseData['data']);
        $this->assertEquals($pendingPurchase->getId()->value(), $responseData['data'][0]['id']);
        $this->assertEquals(3000, $responseData['data'][0]['amount']);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testGetPurchasesWithSorting(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create purchases with different amounts
        $purchase1 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR()),
            createdAt: new \DateTimeImmutable('today'),
        );

        $purchase2 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 3000, currency: Currency::EUR()),
            createdAt: new \DateTimeImmutable('tomorrow'),
        );

        $purchase3 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 7000, currency: Currency::EUR()),
            createdAt: new \DateTimeImmutable('yesterday'),
        );

        $this->purchaseRepository->put($purchase1);
        $this->purchaseRepository->put($purchase2);
        $this->purchaseRepository->put($purchase3);

        // Sort by created_at in descending order
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . '?sort_by=created_at&sort_dir=desc',
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(3, $responseData['data']);

        $this->assertEquals($purchase2->getId()->value(), $responseData['data'][0]['id']);
        $this->assertEquals($purchase1->getId()->value(), $responseData['data'][1]['id']);
        $this->assertEquals($purchase3->getId()->value(), $responseData['data'][2]['id']);

        // Sort by amount in ascending order
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . '?sort_by=amount&sort_dir=desc',
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(3, $responseData['data']);

        $this->assertEquals($purchase3->getId()->value(), $responseData['data'][0]['id']);
        $this->assertEquals($purchase1->getId()->value(), $responseData['data'][1]['id']);
        $this->assertEquals($purchase2->getId()->value(), $responseData['data'][2]['id']);
    }

    public function testGetPurchasesRequiresAuthentication(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint()
            // No bearer token
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testGetPurchasesWithEmptyResult(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // No purchases created for the user

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertIsArray($responseData['data']);
        $this->assertEmpty($responseData['data']);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     */
    public function testCorrectUserPurchases(): void
    {
        $defaultUser = $this->getDefaultUser();
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $purchase1 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
        );

        $purchase2 = PurchaseMother::create(
            userId: new Id($defaultUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 3000, currency: Currency::EUR())
        );

        $purchase3 = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2000, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($purchase1);
        $this->purchaseRepository->put($purchase2);
        $this->purchaseRepository->put($purchase3);

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertIsArray($responseData['data']);
        $this->assertCount(2, $responseData['data']);

        $purchaseIds = array_map(
            fn (array $purchase) => $purchase['id'],
            $responseData['data'],
        );

        $this->assertContains($purchase1->getId()->value(), $purchaseIds);
        $this->assertContains($purchase3->getId()->value(), $purchaseIds);
        $this->assertNotContains($purchase2->getId()->value(), $purchaseIds);
    }

    #[DataProvider('invalidParametersProvider')]
    public function testGetPurchasesWithInvalidParameters(string $queryString, int $expectedStatusCode): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusPurchaseEndpoints::purchasesEndpoint() . $queryString,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
    }

    public static function invalidParametersProvider(): \Generator
    {
        yield 'invalid status' => ['?status=invalid', Response::HTTP_BAD_REQUEST];
        yield 'page without page_size' => ['?page=1', Response::HTTP_BAD_REQUEST];
        yield 'page_size without page' => ['?page_size=10', Response::HTTP_BAD_REQUEST];
        yield 'invalid page' => ['?page=0&page_size=10', Response::HTTP_BAD_REQUEST];
        yield 'invalid page_size' => ['?page=1&page_size=0', Response::HTTP_BAD_REQUEST];
        yield 'invalid sort_by without sort_dir' => ['?sort_by=invalid', Response::HTTP_BAD_REQUEST];
        yield 'invalid sort_dir without sort_by' => ['?sort_dir=invalid', Response::HTTP_BAD_REQUEST];
        yield 'invalid sort_dir' => ['?sort_by=created_at&sort_dir=invalid', Response::HTTP_BAD_REQUEST];
        yield 'invalid date format' => ['?start_date=invalid-date', Response::HTTP_BAD_REQUEST];
    }
}
