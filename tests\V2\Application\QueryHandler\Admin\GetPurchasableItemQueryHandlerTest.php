<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Query\Admin\GetPurchasableItem;
use App\V2\Application\QueryHandler\Admin\GetPurchasableItemQueryHandler;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetPurchasableItemQueryHandlerTest extends TestCase
{
    private PurchasableItemRepository|MockObject $purchasableItemRepository;
    private GetPurchasableItemQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $this->handler = new GetPurchasableItemQueryHandler(
            purchasableItemRepository: $this->purchasableItemRepository,
        );
    }

    /**
     * @throws InvalidUuidException
     */
    private function getQuery(?Uuid $purchasableItemId = null): GetPurchasableItem
    {
        return new GetPurchasableItem(
            purchasableItemId: $purchasableItemId ?? UuidMother::create(),
        );
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws PurchasableItemNotFoundException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public function testHandleReturnsCorrectPurchasableItem(): void
    {
        $purchasableItemId = UuidMother::create();
        $expectedPurchasableItem = PurchasableItemMother::create(id: $purchasableItemId);
        $expectedCriteria = PurchasableItemCriteria::createById($purchasableItemId);

        $query = $this->getQuery(purchasableItemId: $purchasableItemId);

        $this->purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($expectedCriteria)
            ->willReturn($expectedPurchasableItem);

        $result = $this->handler->handle($query);

        $this->assertInstanceOf(PurchasableItem::class, $result);
        $this->assertTrue($result->getId()->equals($purchasableItemId));
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testHandleThrowsPurchasableItemNotFoundException(): void
    {
        $purchasableItemId = UuidMother::create();
        $expectedCriteria = PurchasableItemCriteria::createById($purchasableItemId);

        $query = $this->getQuery(purchasableItemId: $purchasableItemId);

        $this->purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($expectedCriteria)
            ->willThrowException(new PurchasableItemNotFoundException());

        $this->expectException(PurchasableItemNotFoundException::class);

        $this->handler->handle($query);
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     */
    public function testHandleThrowsPurchasableItemRepositoryException(): void
    {
        $purchasableItemId = UuidMother::create();
        $expectedCriteria = PurchasableItemCriteria::createById($purchasableItemId);

        $query = $this->getQuery(purchasableItemId: $purchasableItemId);

        $this->purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($expectedCriteria)
            ->willThrowException(new PurchasableItemRepositoryException('Repository error'));

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Repository error');

        $this->handler->handle($query);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleThrowsCriteriaException(): void
    {
        $purchasableItemId = UuidMother::create();
        $expectedCriteria = PurchasableItemCriteria::createById($purchasableItemId);

        $query = $this->getQuery(purchasableItemId: $purchasableItemId);

        $this->purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($expectedCriteria)
            ->willThrowException(new CriteriaException('Criteria error'));

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Criteria error');

        $this->handler->handle($query);
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws PurchasableItemNotFoundException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public function testHandleCallsRepositoryWithCorrectCriteria(): void
    {
        $purchasableItemId = UuidMother::create();
        $expectedPurchasableItem = PurchasableItemMother::create(id: $purchasableItemId);

        $query = $this->getQuery(purchasableItemId: $purchasableItemId);

        $this->purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(
                $this->callback(function (PurchasableItemCriteria $criteria) {
                    // Verify that the criteria was created with the correct ID
                    // We can't directly compare criteria objects, so we verify the behavior
                    return true; // The criteria creation is tested in the criteria tests
                })
            )
            ->willReturn($expectedPurchasableItem);

        $result = $this->handler->handle($query);

        $this->assertSame($expectedPurchasableItem, $result);
    }
}
