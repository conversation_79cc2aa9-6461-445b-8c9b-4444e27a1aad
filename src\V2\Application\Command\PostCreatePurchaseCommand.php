<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\UuidCollection;

readonly class PostCreatePurchaseCommand implements Command
{
    public function __construct(
        private UuidCollection $purchasableItemIds,
        private Id $userId,
        private TaxRate $taxRate,
        private Currency $currency,
    ) {
    }

    public function getPurchasableItemIds(): UuidCollection
    {
        return $this->purchasableItemIds;
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getTaxRate(): TaxRate
    {
        return $this->taxRate;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }
}
