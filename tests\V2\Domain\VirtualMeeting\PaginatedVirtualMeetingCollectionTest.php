<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\VirtualMeeting;

use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\PaginatedVirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use PHPUnit\Framework\TestCase;

class PaginatedVirtualMeetingCollectionTest extends TestCase
{
    /**
     * @throws \DateMalformedStringException
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    public function testPaginatedCollection()
    {
        $collection = new VirtualMeetingCollection([VirtualMeetingMother::create()]);
        $paginatedCollection = new PaginatedVirtualMeetingCollection($collection, 1);

        $this->assertSame($collection, $paginatedCollection->getCollection());
        $this->assertEquals(1, $paginatedCollection->getTotalItems());
    }

    public function testExceptionWithInvalidCollection(): void
    {
        $this->expectException(\TypeError::class);
        new PaginatedVirtualMeetingCollection(new \stdClass(), 1);
    }
}
