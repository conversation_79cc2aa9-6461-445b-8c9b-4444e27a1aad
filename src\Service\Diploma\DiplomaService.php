<?php

declare(strict_types=1);

namespace App\Service\Diploma;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\TypeDiploma;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Enum\DiplomaType;
use App\Enum\ZipFileTaskEnum;
use App\Exception\InvalidDataException;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\SettingsService;
use App\Service\StatsUser\StatsUserService;
use App\Utils\ToolsUtils;
use App\V2\Domain\Diploma\Helper\DateHelper;
use App\V2\Infrastructure\Utils\MpdfFactory;
use Doctrine\ORM\EntityManagerInterface;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class DiplomaService
{
    private const string DEFAULT_ORIENTATION = 'L';

    private string $baseDir;

    public function __construct(
        private MpdfFactory $mpdfFactory,
        private Environment $twig,
        private SettingsService $settings,
        private EntityManagerInterface $em,
        private AnnouncementConfigurationsService $announcemenConfigurationsService,
        private KernelInterface $kernelInterface,
        private DiplomaFactory $diplomaFactory,
        protected TranslatorInterface $translator,
        private StatsUserService $statsUserService
    ) {
        $this->baseDir = $kernelInterface->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . ZipFileTaskEnum::ZIP_PATH_DIPLOMAS;
    }

    private function createPdfObject(string $orientation = self::DEFAULT_ORIENTATION): Mpdf
    {
        $mpdf = $this->mpdfFactory->createMpdfObject([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => 0,
            'margin_footer' => 0,
            'margin_left' => 0,
            'margin_right' => 0,
            'margin_top' => 0,
            'margin_bottom' => 0,
            'orientation' => $orientation,
            'setAutoTopMargin' => false,
            'setAutoBottomMargin' => false,
        ]);

        // Additional configurations for better diploma rendering
        $mpdf->shrink_tables_to_fit = 0; // Prevent table shrinking
        $mpdf->keep_table_proportions = true; // Maintain table proportions
        $mpdf->use_kwt = true; // Keep-with-table to avoid page breaks in tables
        $mpdf->useSubstitutions = false; // Avoid character substitutions for better performance
        $mpdf->simpleTables = true; // Use simple tables for better performance
        $mpdf->autoPageBreak = true; // Enable automatic page breaks for content overflow
        $mpdf->img_dpi = 96; // Set consistent DPI for images

        return $mpdf;
    }

    /**
     * Extracts orientation from diploma data with fallback to default.
     */
    private function getOrientationFromDiplomaData(?array $diplomaData): string
    {
        $orientation = $diplomaData['orientation'] ?? self::DEFAULT_ORIENTATION;

        if (!\in_array($orientation, ['L', 'P'])) {
            return self::DEFAULT_ORIENTATION;
        }

        return $orientation;
    }

    /**
     * @throws MpdfException
     */
    public function generateUserRequestedDiploma(
        ?Announcement $announcement,
        User $user,
        bool $base64 = true,
        ?string $filename = null
    ): array {
        $mPdf = $this->getTypeOfDiploma($announcement, $user);
        $course = $announcement->getCourse();
        $diploma_title = $user->getFullName() . '_' . $course->getName() . '_' . (new \DateTimeImmutable())->format('d-m-Y') . '.pdf';

        return ['diploma' => $base64 ?
            base64_encode($mPdf->Output($diploma_title, 'S')) :
            $mPdf->Output($filename ?? $diploma_title, 'F'), 'nombre' => $diploma_title];
    }

    public function generateDiplomaDefault(array $request, User $user, ?\DateTimeInterface $courseStartedAt = null): array|string
    {
        $dataDiploma = $this->getMainDiplomaType($request['idCourse']);
        $diploma = $this->diplomaFactory->createDiploma($dataDiploma['className']);

        $orientation = $this->getOrientationFromDiplomaData($dataDiploma);
        $mPdf = $this->createPdfObject($orientation);

        $mPdf->WriteHTML($this->twig->render(
            $dataDiploma['templateBase'],
            $diploma->getContentCourseDiploma($request, $user)
        ));

        $course = $this->em->getRepository(Course::class)->find($request['idCourse']);

        if ($courseStartedAt) {
            $diploma_title = $user->getFullName() . '_' . $course->getName() . '_' . $courseStartedAt->format('d-m-Y') . '.pdf';
            $directoryFile = $this->baseDir . DIRECTORY_SEPARATOR . ToolsUtils::str_without_accents(trim($diploma_title));
            if ($course->isIsContentDiploma() && $this->settings->get('app.content.diploma')) {
                $this->generateContentIndex($mPdf, $course, $user);
            }
            $mPdf->Output($directoryFile, 'F');

            return $directoryFile;
        } else {
            $diploma_title = $user->getFullName() . '_' . $course->getName() . '_' . (new \DateTimeImmutable())->format('d-m-Y') . '.pdf';
            if ($course->isIsContentDiploma() && $this->settings->get('app.content.diploma')) {
                $this->generateContentIndex($mPdf, $course, $user);

                return ['diploma' => base64_encode($mPdf->Output($diploma_title, 'S')), 'nombre' => $diploma_title];
            }

            return ['diploma' => base64_encode($mPdf->Output('Mipdf.pdf', 'S')), 'nombre' => $diploma_title];
        }
    }

    public function generateUserDiplomaAnnouncement(
        ?array $announcementCourse,
        User $user
    ): string {
        $announcement = $this->em->getRepository(Announcement::class)->find($announcementCourse['extraAnnouncement']['idAnnouncement']);
        $mPdf = $this->getTypeOfDiploma($announcement, $user);
        $courseStartedAt = new \DateTime($announcementCourse['startAt']);

        $diploma_title = $user->getFullName() . '_' . $announcementCourse['name'] . '_' . $courseStartedAt->format('d-m-Y') . '.pdf';
        $directoryFile = $this->baseDir . DIRECTORY_SEPARATOR . ToolsUtils::str_without_accents(trim($diploma_title));
        $mPdf->Output($directoryFile, 'F');

        return $directoryFile;
    }

    public function generateDiplomas(array $conditions): string
    {
        $this->em->getFilters()->disable('softdeleteable');
        $users = [];

        $userFilters = $this->getFiltersIfManager($conditions);

        if (0 == $conditions['activeUsers'] || 1 == $conditions['activeUsers']) {
            $users = $this->em->getRepository(User::class)->findActiveOrInactiveUsers($conditions['activeUsers'], $userFilters);
        } else {
            $users = $this->em->getRepository(User::class)->getUsersByFilters($userFilters);
        }

        $userCourse = null;
        $diplomasFiles = 'diplomas-report';
        $courses = $this->em->getRepository(Course::class)->findCoursesDiplomaReport($conditions);
        if (!empty($users) && !empty($courses)) {
            foreach ($courses as $course) {
                foreach ($users as $user) {
                    $userCourse = $this->em->getRepository(UserCourse::class)->findCoursesByUsersDiploma($course->getId(), $user->getId(), $conditions);
                    if ($userCourse) {
                        $diplomasFiles .= '&& ' . $this->generateDiplomaDefault(['idCourse' => $userCourse->getCourse()->getId()], $user, $userCourse->getStartedAt());
                    }
                }
            }
        }
        $this->em->getFilters()->enable('softdeleteable');

        return $diplomasFiles;
    }

    /**
     * @throws InvalidDataException When no diplomas are found to generate
     */
    public function generateUserDiplomas(array $conditions): string
    {
        $this->em->getFilters()->disable('softdeleteable');
        $user = $this->em->getRepository(User::class)->find($conditions['userId']);

        $diplomasFiles = 'diplomas-report';
        $diplomasGenerated = false;

        // Search by itinerary
        if ('0' == $conditions['typeOfDiplomas']) {
            $itineraries = $this->statsUserService->getItinerariesUser($user);
            if ($itineraries) {
                foreach ($itineraries as $itinerary) {
                    if (!empty($itinerary['startAt']) && !empty($itinerary['finishAt']) && '1' == $itinerary['type']['id']) {
                        $diplomasFiles .= '&& ' . $this->generateDiplomaDefault(['idCourse' => $itinerary['id']], $user, new \DateTime($itinerary['startAt']));
                        $diplomasGenerated = true;
                    }
                }
            }
        }
        // Search by filters
        elseif ('1' == $conditions['typeOfDiplomas']) {
            $filters = $this->statsUserService->getCoursesByFilter($user);
            if ($filters) {
                foreach ($filters as $filter) {
                    if (!empty($filter['startAt']) && !empty($filter['finishAt']) && '1' == $filter['type']['id']) {
                        $diplomasFiles .= '&& ' . $this->generateDiplomaDefault(['idCourse' => $filter['id']], $user, new \DateTime($filter['startAt']));
                        $diplomasGenerated = true;
                    }
                }
            }
        }
        // Search by announcement
        else {
            $announcements = $this->statsUserService->getAnnouncementUser($user, true);
            if ($announcements) {
                foreach ($announcements as $announcementCourse) {
                    if (!empty($announcementCourse['startAt']) && !empty($announcementCourse['finishAt']) && '1' == $announcementCourse['type']['id']) {
                        $diplomasFiles .= '&& ' . $this->generateUserDiplomaAnnouncement($announcementCourse, $user);
                        $diplomasGenerated = true;
                    }
                }
            }
        }

        $this->em->getFilters()->enable('softdeleteable');

        if (!$diplomasGenerated) {
            throw new InvalidDataException('No diplomas were found to generate with the specified criteria');
        }

        return $diplomasFiles;
    }

    private function generateContentIndex(Mpdf $mPdf, Course $course, User $user): void
    {
        $indexTitles = $this->getIndexTitles($course);
        $indexTitle = $this->getIndexTitle($user);
        $contentData = $this->buildContentData($indexTitles, $course);

        $this->setupPageStylesForContent($mPdf, $course);
        $mPdf->AddPage();

        // Render all content at once - mPDF handles pagination automatically
        $pageContent = '
            <div style="text-align: center; min-height: 100vh;">
                <h2 style="margin-bottom: 0.4rem; font-size: 1.3rem;">' . $indexTitle . '</h2>
                <div style="padding: 0 4rem 2rem 4rem; line-height: 1.4; font-size: 1.3rem;">
                    ' . $contentData . '
                </div>
            </div>
        ';

        $mPdf->WriteHTML($pageContent);
    }

    /**
     * Sets up page styles including headers and backgrounds for content pages.
     */
    private function setupPageStylesForContent(Mpdf $mPdf, Course $course): void
    {
        $diplomaImages = $this->getDiplomaImages($course);

        // Configure automatic header for all pages
        if ($diplomaImages['header']) {
            $mPdf->SetHTMLHeader('
                <div style="text-align: center; padding-top: 3rem;">
                    <img src="' . $diplomaImages['header'] . '" style="width: 16rem" />
                </div>
            ');
        }

        // Configure automatic background for all pages using @page CSS
        $mPdf->WriteHTML('
            <style>
                @page {
                    background-image: url(' . $diplomaImages['footer'] . ');
                    background-position: bottom center;
                    background-repeat: no-repeat;
                    background-image-resize: 6;
                    margin-top: 8rem;
                    margin-bottom: 4rem;
                }
                body {
                    margin: 0;
                    padding: 0;
                }
            </style>
        ');
    }

    private function getDiplomaImages(Course $course): array
    {
        $diplomaData = $this->getMainDiplomaType($course->getId());
        $diplomaClass = strtolower($diplomaData['className']);

        $baseDir = $this->kernelInterface->getProjectDir() . DIRECTORY_SEPARATOR . 'public/assets/diploma/' . $diplomaClass;

        // Default: most clients use SVG footer
        $footerImage = $baseDir . '/footer_diploma.svg';
        $headerImage = $baseDir . '/logo_diploma.png';

        // Novomatic-specific adjustments
        if ('novomatic' === $diplomaClass) {
            $headerImage = null;
            $footerImage = $baseDir . '/footer_diploma.png';
        }

        // Fundae uses a different footer filename
        if ('fundae' === $diplomaClass) {
            $footerImage = $baseDir . '/f_fundae.svg';
        }

        return [
            'header' => $headerImage,
            'footer' => $footerImage,
        ];
    }

    private function getIndexTitles(Course $course): array
    {
        if (DiplomaType::DEFAULT == $course->getTypeIndexDiploma()) {
            $titles = [];
            foreach ($course->getChapters() as $chapter) {
                $titles[] = $chapter->getTitle();
            }

            return $titles;
        }

        return $course->getDescriptionContentDiploma() ? [$course->getDescriptionContentDiploma()] : [];
    }

    private function getIndexTitle(User $user): string
    {
        return $this->translator->trans('course.diploma.index', [], 'messages', $user->getLocale());
    }

    private function buildContentData(array $indexTitles, Course $course): string
    {
        if (DiplomaType::DEFAULT == $course->getTypeIndexDiploma()) {
            $data = '<div style="text-align: center; font-size: 1.3rem; margin-top: 0.8rem;">';
            foreach ($indexTitles as $title) {
                $data .= '<div style="margin-bottom: 5px;"> - ' . $title . '</div>';
            }
            $data .= '</div></div>';
        } else {
            $data = '<div style="text-align: center; font-size: 1.3rem; margin-top: 0.8rem;">';
            if ($course->getDescriptionContentDiploma()) {
                $manual_index = explode('<p>', $course->getDescriptionContentDiploma());
                foreach ($manual_index as $index) {
                    if ('' != $index) {
                        $data .= '<p>' . $index;
                    }
                }
            }
            $data .= '</div></div>';
        }

        return $data;
    }

    private function getMainDiplomaType(?int $course_id = null): array
    {
        $course = $course_id ? $this->em->getRepository(Course::class)->find($course_id) : null;
        $diploma = $this->em->getRepository(TypeDiploma::class)->findOneBy(['isMain' => true]);
        if ($course) {
            if ($course->getTypeDiploma() && $course->getTypeDiploma()->isActive()) {
                $diploma = $course->getTypeDiploma();
            }
        }

        if (!$diploma) {
            return [
                'templateBase' => ZipFileTaskEnum::DIPLOMADEFAULT,
                'orientation' => self::DEFAULT_ORIENTATION,
                'className' => 'Easylearning',
            ];
        }

        $extra = json_decode($diploma->getExtra(), true);
        if (JSON_ERROR_NONE !== json_last_error()) {
            error_log('JSON Error: ' . json_last_error_msg());

            return [
                'templateBase' => ZipFileTaskEnum::DIPLOMADEFAULT,
                'orientation' => self::DEFAULT_ORIENTATION,
                'className' => 'Easylearning',
            ];
        }

        return [
            'templateBase' => $extra['templateBase'] ?? ZipFileTaskEnum::DIPLOMADEFAULT,
            'orientation' => $extra['orientation'] ?? self::DEFAULT_ORIENTATION,
            'className' => $extra['className'] ?? 'Easylearning',
        ];
    }

    public function generateDiplomaAnnouncement(Announcement $announcement, User $user): \Symfony\Component\HttpFoundation\Response
    {
        $mPdf = $this->getTypeOfDiploma($announcement, $user);
        $course = $announcement->getCourse();
        $diploma_title = $user->getFullName() . '_' . $course->getName() . '_' . (new \DateTimeImmutable())->format('d-m-Y') . '.pdf';

        return $this->mpdfFactory->createDownloadResponse($mPdf, "{$diploma_title}.pdf");
    }

    private function getTypeOfDiploma(Announcement $announcement, User $user): Mpdf
    {
        $course = $announcement->getCourse();
        $type = 1;
        if ($announcement && $announcement->getTypeDiploma()) {
            $type = $announcement->getTypeDiploma()->getId();
        } elseif ($course->getTypeDiploma()) {
            $type = $course->getTypeDiploma()->getId();
        }

        $typeDiploma = $this->em->getRepository(TypeDiploma::class)->find($type);

        $extra = json_decode($typeDiploma->getExtra(), true);
        if (null == $extra) {
            error_log('JSON Error in getTypeOfDiploma: ' . json_last_error_msg());
        }

        $orientation = self::DEFAULT_ORIENTATION; // Default value

        if ($extra) {
            $templateDiploma = $extra['templateBase'];
            $templateContent = $extra['templateContent'];
            if (isset($extra['orientation'])) {
                $orientation = $extra['orientation'];
            }
        } else {
            $templateDiploma = ZipFileTaskEnum::DIPLOMADEFAULT;
            $templateContent = ZipFileTaskEnum::DIPLOMADEFAULTCONTENT;
        }

        if ($this->announcemenConfigurationsService->hasIncludeObjectAndContentDiploma($announcement)) {
            $templateDiploma = $extra['templateBonus'];
        }

        $diploma = $this->diplomaFactory->createDiploma($extra['className']);

        $content = $diploma->getContentAnnouncementDiploma($announcement, $user);

        $mPdf = $this->createPdfObject($orientation);

        $mPdf->WriteHTML($this->twig->render(
            $templateDiploma,
            $content
        ));

        if ($announcement && $this->announcemenConfigurationsService->hasIncludeObjectAndContentDiploma($announcement)) {
            $this->generateAnnouncementContentPage($mPdf, $templateContent, $course, $announcement);
        } elseif ($course->isIsContentDiploma() && $this->settings->get('app.content.diploma')) {
            $this->generateContentIndex($mPdf, $course, $user);
        }

        return $mPdf;
    }

    /**
     * Generates announcement content page with proper background and headers
     * Uses the same styling system as generateContentIndex but renders diploma_content.html.twig.
     */
    private function generateAnnouncementContentPage(Mpdf $mPdf, string $templateContent, Course $course, Announcement $announcement): void
    {
        $this->setupPageStylesForContent($mPdf, $course);
        $mPdf->AddPage();

        // Render content - mPDF handles pagination automatically
        $contentHtml = $this->twig->render($templateContent, [
            'course' => $course,
            'announcement' => $announcement,
        ]);

        $styledContent = '
            <div style="text-align: center; min-height: 100vh;">
                <div style="padding: 0 4rem 2rem 4rem; line-height: 1.4;">
                    ' . $contentHtml . '
                </div>
            </div>
        ';

        $mPdf->WriteHTML($styledContent);
    }

    public function generateDiplomaForPreview(TypeDiploma $typeDiploma): void
    {
        // Use the system default language for previews
        $locale = $this->settings->get('app.defaultLanguage');

        $currentDate = new \DateTime();
        $dateMinusThreeDays = (clone $currentDate)->modify('-3 days');

        // Use DateHelper to get the dates (same pattern as normal diplomas)
        $dateToUse = DateHelper::getDateToUse(null, $dateMinusThreeDays);
        $currentDateToUse = DateHelper::getDateToUse(null, $currentDate);

        $content = [
            'user' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'registerKey' => '12345678A',
            ],
            'course' => [
                'name' => 'Sample Course Name',
            ],
            'date' => $dateToUse,
            'locale' => $locale,
            'currentDate' => $currentDateToUse,
            'showDuration' => true, // Default to true for preview to show all elements
            'courseDurationHours' => 40.0, // Sample duration for preview templates
        ];

        $content['date'] = $dateToUse->format('d/m/Y');
        $content['currentDate'] = $currentDateToUse->format('d/m/Y');

        $extra = json_decode($typeDiploma->getExtra(), true);
        if (null == $extra) {
            error_log('JSON Error in generateDiplomaForPreview: ' . json_last_error_msg());
        }

        $templateDiploma = ZipFileTaskEnum::DIPLOMA_PREVIEW;
        $routePublic = 'assets_announcement/preview_type_diploma/easylearning.pdf';
        $orientation = self::DEFAULT_ORIENTATION; // Default value

        if ($extra) {
            if (isset($extra['templatePreview']) && isset($extra['previewDiploma'])) {
                $templateDiploma = $extra['templatePreview'];
                $routePublic = $extra['previewDiploma'];
            }
            if (isset($extra['orientation'])) {
                $orientation = $extra['orientation'];
            }
        }

        $mPdf = $this->createPdfObject($orientation);

        $mPdf->WriteHTML($this->twig->render(
            $templateDiploma,
            $content
        ));

        $filePath = $this->kernelInterface->getProjectDir() . '/public/' . $routePublic;

        $mPdf->Output($filePath, 'F');
    }

    /**
     * @throws \Exception When preview generation fails
     */
    public function generatePreview(TypeDiploma $diplomaType): void
    {
        try {
            $this->generateDiplomaForPreview($diplomaType);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function getFiltersIfManager(array $params): ?array
    {
        $filtersId = null;

        if (isset($params['createdBy']) && null != $params['createdBy']) {
            $user = $this->em->getRepository(User::class)->find($params['createdBy']);
            if ($user && \in_array('ROLE_MANAGER', $user->getRoles())) {
                $filters = $user->getFilters();

                foreach ($filters as $filter) {
                    $filtersId[] = $filter->getId();
                }
            }
        }

        return $filtersId;
    }
}
