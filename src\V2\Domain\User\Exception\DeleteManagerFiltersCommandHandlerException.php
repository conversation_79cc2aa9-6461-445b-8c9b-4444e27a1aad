<?php

declare(strict_types=1);

namespace App\V2\Domain\User\Exception;

class DeleteManagerFiltersCommandHandlerException extends \Exception
{
    public static function filterDoesNotExist(): self
    {
        return new self('At least one of the filters does not exist');
    }

    public static function userFilterNotAssignedToUser(): self
    {
        return new self('At least one of the filters is not assigned to a user');
    }
}
