<?php

declare(strict_types=1);

namespace App\V2\Domain\User\UserFilter;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserFilterNotFoundException;
use App\V2\Domain\User\Exception\UserFilterRepositoryException;

interface UserFilterRepository
{
    /**
     * @throws UserFilterRepositoryException
     * @throws InfrastructureException
     */
    public function insert(UserFilter $userFilter): void;

    /**
     * @throws UserFilterNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(UserFilterCriteria $criteria): UserFilter;

    /**
     * @throws InfrastructureException
     */
    public function findBy(UserFilterCriteria $criteria): UserFilterCollection;

    /**
     * @throws InfrastructureException
     */
    public function delete(UserFilter $userFilter): void;
}
