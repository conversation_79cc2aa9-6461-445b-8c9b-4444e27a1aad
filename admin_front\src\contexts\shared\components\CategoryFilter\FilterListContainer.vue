<template>
  <div class="FilterListContainer">
    <header>
      <BaseButton
        v-if="showHeaderButtons && type === 'remove'"
        :disabled="disabled"
        size="s"
        @click="emitAll"
      >
        <Icon icon="angle-left" />
        {{ $t('REMOVE_ALL') }}
      </BaseButton>
      <BaseInput
        v-model="searchText"
        :placeholder="$t('SUBSCRIPTION.FILTER.SEARCH')"
        name="search"
        :icon="['fas', 'search']"
        :disabled="disabled"
      />
      <BaseButton
        v-if="showHeaderButtons && type === 'add'"
        :disabled="disabled"
        size="s"
        @click="emitAll"
      >
        {{ $t('ADD_ALL') }}
        <Icon icon="angle-right" />
      </BaseButton>
    </header>
    <main>
      <FilterItem
        v-for="item in list"
        :key="item.key"
        :item="item"
        :disabled="disabled"
        :type="type"
        @change="emit('update', item)"
      />
      <p
        v-show="!list.length"
        class="noData"
      >
        {{ $t('NO_DATA') }}
      </p>
    </main>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import FilterItem from '@/contexts/shared/components/CategoryFilter/FilterItem.vue'

const emit = defineEmits(['update', 'updateAll'])
const props = defineProps({
  filterList: { type: Array, default: () => [] },
  disabled: { type: Boolean, default: false },
  showHeaderButtons: { type: Boolean, default: true },
  type: { type: String, default: 'add', validator: (value) => ['add', 'remove'].includes(value) },
})
const searchText = ref('')
const list = computed(() => {
  const search = `${searchText.value}`.trim()
  if (search.length) return props.filterList.filter((filter) => filter.name.includes(search))
  return props.filterList
})
function emitAll() {
  if (props.disabled || !props.filterList.length) {
    return null
  }
  emit(
    'updateAll',
    list.value.filter((item) => !item.disabled)
  )
}
</script>

<style scoped lang="scss">
.FilterListContainer {
  padding: 0 1rem 1rem;
  background-color: var(--color-neutral-lighter);
  header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 1rem;
    position: sticky;
    top: 0;
    padding-top: 1rem;
    background-color: var(--color-neutral-lighter);

    .BaseButton {
      white-space: nowrap;
    }
  }

  main {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .noData {
    text-align: center;
  }
}
</style>
