<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;

interface PurchasableItemRepository
{
    /**
     * @throws PurchasableItemRepositoryException
     */
    public function put(PurchasableItem $item): void;

    /**
     * @throws PurchasableItemRepositoryException
     * @throws PurchasableItemNotFoundException
     */
    public function findOneBy(PurchasableItemCriteria $criteria): ?PurchasableItem;

    /**
     * @throws PurchasableItemRepositoryException
     */
    public function findBy(PurchasableItemCriteria $criteria): PurchasableItemCollection;

    public function countBy(PurchasableItemCriteria $criteria): int;

    /**
     * @throws PurchasableItemRepositoryException
     */
    public function delete(PurchasableItem $item): void;
}
