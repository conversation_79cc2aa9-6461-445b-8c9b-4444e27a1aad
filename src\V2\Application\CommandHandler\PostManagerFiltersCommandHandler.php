<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Service\SettingsService;
use App\V2\Application\Command\PostManagerFiltersCommand;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\PostManagerFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class PostManagerFiltersCommandHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private FilterRepository $filterRepository,
        private ManagerFilterRepository $managerFilterRepository,
        private SettingsService $settingsService,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws UserForbiddenAction
     * @throws CollectionException
     * @throws CriteriaException
     * @throws PostManagerFiltersCommandHandlerException
     */
    public function handle(PostManagerFiltersCommand $command): void
    {
        if (!$this->settingsService->get('app.user.useFilters')) {
            throw UserForbiddenAction::filtersNotEnabled();
        }

        $this->userRepository->findOneBy(
            UserCriteria::createById($command->getUserId()),
        );

        $managerFilterIds = array_unique($command->getFilterIds()->all());
        $filters = $this->filterRepository->findBy(
            FilterCriteria::createByIds(new IdCollection($managerFilterIds)),
        );
        if ($filters->count() !== \count($managerFilterIds)) {
            throw PostManagerFiltersCommandHandlerException::filterDoesNotExist();
        }

        $assignedFilters = $this->managerFilterRepository->findBy(
            ManagerFilterCriteria::createEmpty()->filterByFilterIds($command->getFilterIds()),
        );

        if (!$assignedFilters->isEmpty()) {
            throw PostManagerFiltersCommandHandlerException::userHasFilterAssigned();
        }

        foreach ($managerFilterIds as $managerFilterId) {
            $this->managerFilterRepository->insert(
                new ManagerFilter(
                    userId: $command->getUserId(),
                    filterId: $managerFilterId,
                )
            );
        }
    }
}
