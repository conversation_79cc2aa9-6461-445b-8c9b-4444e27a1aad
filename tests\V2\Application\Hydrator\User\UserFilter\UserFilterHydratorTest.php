<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\User\UserFilter;

use App\Repository\FilterRepository as LegacyFilterRepository;
use App\Tests\Mother\Entity\FilterCategoryMother;
use App\Tests\Mother\Entity\FilterMother;
use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\User\UserFilter\UserFilterHydrator;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterHydrationCriteria;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class UserFilterHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?LegacyFilterRepository $legacyFilterRepository = null,
    ): UserFilterHydrator {
        return new UserFilterHydrator(
            legacyFilterRepository: $legacyFilterRepository ?? $this->createMock(LegacyFilterRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                UserFilterHydrationCriteria::createEmpty()->withFilters()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                UserFilterHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws HydratorException
     * @throws CollectionException
     */
    public function testEmptyCollection(): void
    {
        $collection = new UserFilterCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            UserFilterHydrationCriteria::createEmpty()
                ->withFilters()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws HydratorException
     * @throws CollectionException
     */
    public function testWithFilters(): void
    {
        $category1 = FilterCategoryMother::create(id: 1, name: 'Category 1');
        $category2 = FilterCategoryMother::create(id: 2, name: 'Category 2');
        $filter1 = FilterMother::create(id: 1, name: 'Filter 1', category: $category1);
        $filter2 = FilterMother::create(id: 2, name: 'Filter 2', category: $category2);
        $filter3 = FilterMother::create(id: 3, name: 'Filter 3', category: $category2);
        $filter4 = FilterMother::create(id: 4, name: 'Filter 4', category: $category1);
        $filter5 = FilterMother::create(id: 5, name: 'Filter 5', category: $category2);

        $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(Query::class);

        $query->method('getResult')
            ->willReturn([
                $filter1, $filter2, $filter3, $filter4, $filter5,
            ]);
        $queryBuilder->method('andWhere')
            ->willReturn($queryBuilder);
        $queryBuilder->method('setParameter')
            ->willReturn($queryBuilder);
        $queryBuilder->method('getQuery')
            ->willReturn($query);
        $legacyFilterRepository->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $userFilter1 = UserFilterMother::create(userId: new Id(1), filterId: new Id(1));
        $userFilter2 = UserFilterMother::create(userId: new Id(1), filterId: new Id(2));
        $userFilter4 = UserFilterMother::create(userId: new Id(1), filterId: new Id(4));
        $userFilter6 = UserFilterMother::create(userId: new Id(1), filterId: new Id(6));

        $collection = new UserFilterCollection([$userFilter1, $userFilter2, $userFilter4]);
        $hydrator = $this->getHydrator(
            legacyFilterRepository: $legacyFilterRepository,
        );

        $hydrator->hydrate(
            collection: $collection,
            criteria: UserFilterHydrationCriteria::createEmpty()->withFilters()
        );

        foreach ($collection->all() as $userFilter) {
            if (6 === $userFilter->getFilterId()->value()) {
                $this->assertNull($userFilter->getFilter());
            } else {
                $this->assertNotNull($userFilter->getFilter());
            }
            $filter = $userFilter->getFilter();
            switch ($userFilter->getFilterId()->value()) {
                case 1:
                    $this->assertEquals(1, $filter->getId()->value());
                    $this->assertEquals('Filter 1', $filter->getName());
                    $this->assertEquals(1, $filter->getCategoryId()->value());
                    break;
                case 2:
                    $this->assertEquals(2, $filter->getId()->value());
                    $this->assertEquals('Filter 2', $filter->getName());
                    $this->assertEquals(2, $filter->getCategoryId()->value());
                    break;
                case 4:
                    $this->assertEquals(4, $filter->getId()->value());
                    $this->assertEquals('Filter 4', $filter->getName());
                    $this->assertEquals(1, $filter->getCategoryId()->value());
                    break;
                case 6:
                    break;
                default:
                    $this->fail('Unexpected filter');
            }
        }
    }
}
