<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User\ManagerFilter;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;

class ManagerFilterCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new ManagerFilterCollection($items);
    }

    protected function getExpectedType(): string
    {
        return ManagerFilter::class;
    }

    protected function getItem(): object
    {
        return ManagerFilterMother::create();
    }
}
