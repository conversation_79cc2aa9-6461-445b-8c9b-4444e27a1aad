<?php

declare(strict_types=1);

namespace App\Tests\Functional\Service\Diploma;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\TypeCourse;
use App\Entity\UserCourse;
use App\Service\Diploma\DiplomaFactory;
use App\Service\Diploma\Strategy\NovomaticStrategy;
use App\Tests\Functional\FunctionalTestCase;

class NovomaticDiplomaTest extends FunctionalTestCase
{
    private const TEST_COURSE_CODE = 'NOVOMATIC-TEST-COURSE';
    private const TEST_ANNOUNCEMENT_CODE = 'NOVOMATIC-TEST-ANN';
    private const TEST_DEBUG_MODE = false;

    protected function setUp(): void
    {
        parent::setUp();
        self::$debugMode = self::TEST_DEBUG_MODE;
        $this->loadTestData();
    }

    /**
     * Test that NovomaticStrategy is properly registered in DiplomaFactory.
     */
    public function testNovomaticStrategyIsRegistered(): void
    {
        $this->log('Testing Novomatic strategy registration in DiplomaFactory');

        $diplomaFactory = $this->getContainer()->get(DiplomaFactory::class);
        $strategy = $diplomaFactory->getStrategy('Novomatic');

        $this->assertInstanceOf(
            NovomaticStrategy::class,
            $strategy,
            'NovomaticStrategy should be properly registered in DiplomaFactory'
        );
    }

    /**
     * Test diploma content generation for courses.
     */
    public function testGetContentCourseDiploma(): void
    {
        $this->log('Testing course diploma content generation');

        $em = $this->getEntityManager();
        $user = $this->getDefaultUser();
        $course = $em->getRepository(Course::class)->findOneBy(['code' => self::TEST_COURSE_CODE]);

        $this->assertNotNull($course, 'Test course should exist');

        $diplomaFactory = $this->getContainer()->get(DiplomaFactory::class);
        $strategy = $diplomaFactory->getStrategy('Novomatic');

        $request = [
            'idCourse' => $course->getId(),
            'date' => '2024-01-15',
        ];

        $content = $strategy->getContentCourseDiploma($request, $user);

        // Verify basic structure
        $this->assertIsArray($content, 'Content should be an array');
        $this->assertArrayHasKey('user', $content, 'Content should contain user');
        $this->assertArrayHasKey('course', $content, 'Content should contain course');
        $this->assertArrayHasKey('locale', $content, 'Content should contain locale');

        // Verify diploma-specific fields
        $this->assertArrayHasKey('userName', $content, 'Content should contain userName');
        $this->assertArrayHasKey('courseName', $content, 'Content should contain courseName');
        $this->assertArrayHasKey('diplomaDate', $content, 'Content should contain diplomaDate');

        // Verify content values
        $this->assertEquals($user, $content['user'], 'User should match');
        $this->assertEquals($course, $content['course'], 'Course should match');
        $this->assertEquals($course->getName(), $content['courseName'], 'Course name should match');
        $this->assertStringContainsString($user->getFirstName(), $content['userName'], 'User name should contain first name');
        $this->assertStringContainsString($user->getLastName(), $content['userName'], 'User name should contain last name');
    }

    /**
     * Test diploma content generation for announcements.
     */
    public function testGetContentAnnouncementDiploma(): void
    {
        $this->log('Testing announcement diploma content generation');

        $em = $this->getEntityManager();
        $user = $this->getDefaultUser();
        $announcement = $em->getRepository(Announcement::class)->findOneBy(['code' => self::TEST_ANNOUNCEMENT_CODE]);

        $this->assertNotNull($announcement, 'Test announcement should exist');

        $diplomaFactory = $this->getContainer()->get(DiplomaFactory::class);
        $strategy = $diplomaFactory->getStrategy('Novomatic');

        $content = $strategy->getContentAnnouncementDiploma($announcement, $user);

        // Verify basic structure
        $this->assertIsArray($content, 'Content should be an array');
        $this->assertArrayHasKey('user', $content, 'Content should contain user');
        $this->assertArrayHasKey('course', $content, 'Content should contain course');
        $this->assertArrayHasKey('diplomaDate', $content, 'Content should contain diplomaDate');

        // Verify that diplomaDate is properly formatted
        $this->assertNotNull($content['diplomaDate'], 'Diploma date should not be null');
        $this->assertIsString($content['diplomaDate'], 'Diploma date should be a string');
    }

    /**
     * Test diploma content with different user locales.
     *
     * @dataProvider localeProvider
     */
    public function testDiplomaContentWithDifferentLocales(string $locale): void
    {
        $this->log("Testing diploma content with locale: $locale");

        $this->setLocaleForUser($locale);

        $em = $this->getEntityManager();
        $user = $this->getDefaultUser();
        $course = $em->getRepository(Course::class)->findOneBy(['code' => self::TEST_COURSE_CODE]);

        $diplomaFactory = $this->getContainer()->get(DiplomaFactory::class);
        $strategy = $diplomaFactory->getStrategy('Novomatic');

        $request = [
            'idCourse' => $course->getId(),
            'date' => '2024-01-15',
        ];

        $content = $strategy->getContentCourseDiploma($request, $user);

        $this->assertEquals($locale, $content['locale'], "Locale should be $locale");
    }

    /**
     * Test diploma generation with UserCourse valued_at date.
     */
    public function testDiplomaWithUserCourseValuedAt(): void
    {
        $this->log('Testing diploma with UserCourse valued_at date');

        $em = $this->getEntityManager();
        $user = $this->getDefaultUser();
        $course = $em->getRepository(Course::class)->findOneBy(['code' => self::TEST_COURSE_CODE]);

        // Create UserCourse with valued_at date
        $userCourse = new UserCourse();
        $userCourse->setUser($user);
        $userCourse->setCourse($course);
        $userCourse->setValuedAt(new \DateTimeImmutable('2024-01-15'));

        $em->persist($userCourse);
        $em->flush();

        $diplomaFactory = $this->getContainer()->get(DiplomaFactory::class);
        $strategy = $diplomaFactory->getStrategy('Novomatic');

        $request = [
            'idCourse' => $course->getId(),
        ];

        $content = $strategy->getContentCourseDiploma($request, $user);

        // Note: Currently the strategy has the date formatting commented out
        // This test verifies the current behavior
        $this->assertArrayHasKey('diplomaDate', $content, 'Content should contain diplomaDate');

        // Clean up
        $em->remove($userCourse);
        $em->flush();
    }

    private function loadTestData(): void
    {
        $em = $this->getEntityManager();

        // Check if test data already exists
        $existingCourse = $em->getRepository(Course::class)->findOneBy(['code' => self::TEST_COURSE_CODE]);
        if ($existingCourse) {
            return;
        }

        // Create TypeCourse
        $typeCourse = $this->getTypeCourse();

        // Create Course
        $course = new Course();
        $course->setCode(self::TEST_COURSE_CODE)
            ->setName('Novomatic Test Course')
            ->setTypeCourse($typeCourse)
            ->setLocale('es')
            ->setDescription('Test course for Novomatic diploma functionality')
            ->setActive(true)
            ->setOpen(true)
            ->setIsNew(true)
            ->setOpenVisible(true);

        $em->persist($course);
        $em->flush();

        // Create Announcement
        $announcement = new Announcement();
        $announcement->setCourse($course)
            ->setStartAt(new \DateTimeImmutable('-1 week'))
            ->setFinishAt(new \DateTimeImmutable('+1 week'))
            ->setStatus(Announcement::STATUS_ACTIVE)
            ->setSubsidized(false)
            ->setCode(self::TEST_ANNOUNCEMENT_CODE);

        $em->persist($announcement);
        $em->flush();

        // Create AnnouncementUser
        $user = $this->getDefaultUser();
        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncement($announcement);
        $announcementUser->setUser($user);
        $announcementUser->setDateApproved(new \DateTimeImmutable('2024-01-15'));
        $announcementUser->setAproved(true);

        $em->persist($announcementUser);
        $em->flush();
    }

    protected function tearDown(): void
    {
        $this->clearTestData();
        parent::tearDown();
    }

    private function clearTestData(): void
    {
        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            UserCourse::class,
            Course::class,
        ]);
    }
}
