<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class VirtualMeetingValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateVirtualMeetingRequest(array $data): void
    {
        $constraints = [
            new Constraints\NotBlank(message: 'Body cannot be empty'),
            new Constraints\Collection([
                'type' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                    new Constraints\Choice(
                        choices: array_map(fn ($case) => strtolower($case->name), VirtualMeetingType::cases()),
                        message: 'Type must be one of the valid values from VirtualMeetingType enum.'
                    ),

                    new Constraints\Callback(function ($value, ExecutionContextInterface $context) {
                        $data = $context->getRoot();

                        if ('fixed' === $value && !isset($data['url'])) {
                            $context->buildViolation('URL is required for FIXED type virtual meetings')
                                ->addViolation();
                        }
                    }),
                ],
                'url' => [
                    new Constraints\Optional([
                        new Constraints\Url(),
                        new Constraints\Callback(function ($value, ExecutionContextInterface $context) {
                            $data = $context->getRoot();
                            $type = $data['type'] ?? null;

                            if ('fixed' === $type && empty($value)) {
                                $context->buildViolation('URL is required for FIXED type virtual meetings')
                                    ->addViolation();
                            }
                        }),
                    ]),
                ],
                'start_at' => [
                    new Constraints\NotBlank(),
                    new Constraints\DateTime(),
                ],
                'finish_at' => [
                    new Constraints\NotBlank(),
                    new Constraints\DateTime(),
                    new Constraints\Callback(function ($value, ExecutionContextInterface $context) {
                        $data = $context->getRoot();
                        $startAt = $data['start_at'] ?? null;

                        if (!empty($startAt) && !empty($value)) {
                            try {
                                $startDate = new \DateTimeImmutable($startAt);
                                $finishDate = new \DateTimeImmutable($value);

                                if ($finishDate <= $startDate) {
                                    $context->buildViolation('Finish date must be after start date')
                                        ->addViolation();
                                }
                            } catch (\Exception) {
                                // Do nothing
                            }
                        }
                    }),
                ],
            ]),
        ];

        parent::validate($data, $constraints);
    }
}
