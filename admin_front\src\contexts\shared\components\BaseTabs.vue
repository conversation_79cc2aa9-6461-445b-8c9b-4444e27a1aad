<template>
  <div class="BaseTabs">
    <div class="tabList container">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="tab"
        :class="{ active: tab.key === current }"
        @click="emitChange(tab.key)"
      >
        <Icon
          v-if="tab.icon"
          :icon="tab.icon"
        />
        <span>{{ tab.title }}</span>
      </div>
    </div>
    <div class="tabsContent">
      <main class="container">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['change'])
const props = defineProps({
  tabs: { type: Array, default: () => [] },
  current: { type: String, default: '' },
  disabled: { type: Boolean, default: false },
})

function emitChange(key = '') {
  if (props.disabled || props.current === key) {
    return null
  }
  emit('change', key)
}
</script>

<style scoped lang="scss">
.BaseTabs {
  display: grid;
  grid-template-rows: auto 1fr;

  .tabList {
    display: flex;
    gap: 3px;
    padding-inline: 1rem;
    user-select: none;
    overflow-y: hidden;
    overflow-x: auto;
    scrollbar-width: thin;
    margin-bottom: -1px;

    .tab {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.35rem 0.75rem;
      border: 1px solid var(--color-neutral-mid);
      margin-bottom: -1px;
      border-radius: 7px 7px 0 0;
      cursor: pointer;
      text-wrap: nowrap;
      z-index: 0;

      &.active {
        border-width: 1px 1px 0;
      }
    }
  }

  .tabsContent,
  .tab.active {
    background-color: var(--color-neutral-lightest);
  }

  .tabsContent {
    border: solid var(--color-neutral-mid);
    border-width: 1px 0 0;

    .container {
      padding: 1rem;
    }
  }

  .container {
    width: 100%;
  }
}
</style>
