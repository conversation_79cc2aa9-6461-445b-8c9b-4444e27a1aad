<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\LaunchLtiChapterQuery;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetLaunchLtiChapterController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(Request $request, int $courseId, int $chapterId): Response
    {
        IdValidator::validateId($courseId);
        IdValidator::validateId($chapterId);

        $user = RequestAttributeExtractor::extractUser($request);

        $result = $this->ask(
            new LaunchLtiChapterQuery(
                courseId: new Id($courseId),
                chapterId: new Id($chapterId),
                user: $user,
                schemeAndHttpHost: $request->getSchemeAndHttpHost(),
            )
        );

        return new Response(
            $result,
            Response::HTTP_OK
        );
    }
}
