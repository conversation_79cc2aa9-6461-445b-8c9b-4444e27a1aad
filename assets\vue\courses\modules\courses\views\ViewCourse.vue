<template>
  <div class="ViewCourse" :key="'course_' + course?.id">
    <div class="ViewCourse--content">
      <header class="p-4">
        <h1 class="fs-3">
          <i class="fa fa-landmark"></i>
          {{ $t("USER.COURSES.COURSE") }} {{ $route.params.name }}
        </h1>
        <p v-if="course.createdBy" class="text-muted">
          {{
            $t("CREATED_BY_WITH_DATE", {
              email: course.createdBy,
              date: course.createdAt,
            })
          }}
        </p>
        <p v-if="course.updatedBy" class="text-muted">
          {{
            $t("UPDATED_BY_WITH_DATE", {
              email: course.updatedBy,
              date: course.updatedAt,
            })
          }}
        </p>
      </header>

      <nav class="nav nav-tabs px-4">
        <li
          class="nav-item"
          role="presentation"
          v-for="(tab, index) in tabs"
          :key="tab.name + index"
        >
          <button
            class="nav-link"
            :class="{ active: activeTab === tab.name }"
            :id="`${tab.name}-tab`"
            @click="setActiveTab(tab.name)"
          >
            <i :class="tab.icon"></i> {{ tab.label }}
          </button>
        </li>
      </nav>

      <div class="tab-content">
        <div
          v-for="(tab, index) in tabs"
          :key="tab.name + index"
          class="tab-pane fade"
          :class="{ 'active show': activeTab === tab.name }"
          :id="tab.name + index"
          role="tabpanel"
          :aria-labelledby="`${tab.name}-tab`"
        >
          <component
              :is="tab.component"
              v-if="activeTab === tab.name"
              :course_data="{
                name: course.name,
                image: course.image,
                category: course.category,
                type: course.typeCourse,
              }"
              :read-only="
                !$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.OPINIONS)
              "
              :course_id="course.id"
              class="p-0"
          />
        </div>
      </div>
    </div>

    <BaseModal
      v-if="showModal && isEveryChapterContentCompleted"
      identifier="modal-new-language"
      :title="$t('COURSE.QUESTION_MODAL_TRANSLATE')"
      @close="closeModal"
    >
      <div
        class="d-flex align-items-center justify-content-center"
        v-if="isSubmitting"
      >
        <Spinner />
      </div>
      <form v-else @submit.prevent="handleSubmit">
        <p>{{ $t("COURSE.CONTENT_MODAL_TRANSLATE") }}</p>
        <div v-if="availableLocales?.length" class="mb-3">
          <div
            class="alert alert-warning"
            role="alert"
            v-if="!isEnableTranslation && selectedLocale"
          >
            {{ $t("COURSE.CONTENT_MODAL_TRANSLATE_COMPLETE") }}
          </div>
          <label for="select-locale" class="form-label">{{
            $t("LOCALES")
          }}</label>
          <select
            id="select-locale"
            class="form-select"
            v-model="selectedLocale"
            required
          >
            <option disabled value=""></option>
            <option
              v-for="locale in availableLocales"
              :key="locale.id"
              :value="locale.id"
            >
              {{ locale.name }}
            </option>
          </select>
        </div>
        <div v-else class="mb-3 alert alert-info">
          {{ $t("COURSE.MODAL_TRANSLATE_HAS_ALREADY_TRANSLATE") }}
        </div>
        <div v-if="availableLocales?.length" class="d-flex justify-content-end">
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="!isEnableTranslation"
          >
            <span
              v-if="isSubmitting"
              class="spinner-border spinner-border-sm me-2"
            ></span>
            {{ $t("COMMON.TRANSLATE") }}
          </button>
        </div>
      </form>
    </BaseModal>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import $ from "jquery";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import ROUTE_NAMES from "../router/routeNames";
import BaseModal from "../../../../base/BaseModal.vue";
import InfoCourse from "../components/InfoCourse.vue";
import CourseStatsUsers from "../../../../admin/course/CourseStatsUsers.vue";
import CourseStatsDetails from "../../../../admin/course/CourseStatsDetails.vue";
import CourseStatsOpinions from "../../../../admin/course/CourseStatsOpinions.vue";
import ListMaterialCourse from "../../../../admin/components/material-course/listMaterialCourse.vue";
import Chapters from "../components/Chapters.vue";
import Seasons from "../components/Seasons.vue";
import Announcements from "../components/Announcements.vue";
import Translations from "../components/Translations.vue";
import { setTabData, getTabData } from "../helpers";
import {
  COURSE_ACTIONS_BY_ROLE,
  COURSE_PERMISSIONS,
} from "../../../../common/utils/auth/permissions/course.permissions";
import { se } from "date-fns/locale";
import {courseCreatorsMixin} from "../mixins/courseCreatorsMixin";

const EVENT_NAMES = {
  BACK_HOME: "backHome",
  MODIFY_COURSE: "modifyCourse",
  TRANSLATE_COURSE: "translateCourse",
  SHARE_COURSE: "shareCourse",
};

export default {
  name: "ViewCourse",
  mixins: [courseCreatorsMixin],
  components: {
    Spinner,
    InfoCourse,
    CourseStatsUsers,
    CourseStatsDetails,
    CourseStatsOpinions,
    ListMaterialCourse,
    Chapters,
    Seasons,
    Announcements,
    Translations,
    BaseModal,
  },
  data() {
    return {
      ROUTE_NAMES,
      activeTab: "info",
      loading: false,
      showModal: false,
      isSubmitting: false,
      selectedLocale: "",

      tabs: [],
      tabsAvailable: [
        {
          name: "info",
          label: this.$t("COURSE.GENERAL_INFO_SHORT"),
          component: "InfoCourse",
        },
        {
          name: "chapters",
          label: this.$t("CHAPTERS.LABEL.PLURAL"),
          component: "Chapters",
        },
        {
          name: "seasons",
          label: this.$t("SEASONS.LABEL.PLURAL"),
          component: "Seasons",
        },
        {
          name: "announcements",
          label: this.$t("ANNOUNCEMENT.HOME.TITLE"),
          component: "Announcements",
        },
        {
          name: "materials",
          label: this.$t("ANNOUNCEMENT.MATERIAL"),
          component: "ListMaterialCourse",
        },
        {
          name: "translations",
          label: this.$t("TRANSLATIONS.LABEL_IN_PLURAL"),
          component: "Translations",
        },
        {
          name: "people",
          label: this.$t("ITINERARY.USER.LABEL_PLURAL"),
          component: "CourseStatsUsers",
        },
        {
          name: "statistics",
          label: this.$t("ANNOUNCEMENT.STATS"),
          component: "CourseStatsDetails",
        },
        {
          name: "opinions",
          label: this.$t("OPINIONS.HOME.TITLE"),
          component: "CourseStatsOpinions",
        },
      ],
      actions: [
        {
          name: this.$t("BACK"),
          event: EVENT_NAMES.BACK_HOME,
          class: "btn btn-secondary",
        },
        {
          name: this.$t("COMMON.EDIT"),
          event: EVENT_NAMES.MODIFY_COURSE,
          class: "btn btn-primary",
        },
        {
          icon: "fa fa-globe",
          name: this.$t("COMMON.TRANSLATE"),
          event: EVENT_NAMES.TRANSLATE_COURSE,
          class: "btn btn-primary",
          bind: { disabled: true },
        },
        {
          icon: "fa fa-share",
          name: this.$t("SHARE"),
          event: EVENT_NAMES.SHARE_COURSE,
          class: "btn btn-primary",
          bind: { disabled: true },
        },
      ],
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS;
    },
    useGlobalEventBus: get("contentTitleModule/getUseGlobalEventBus"),
    isEveryChapterContentCompleted: get(
      "coursesModule/getIsEveryChapterContentCompleted"
    ),
    rawCourseInfo: get("coursesModule/getCourseInfo"),
    course() {
      return this.rawCourseInfo;
    },
    courseId() {
      return this.$route.params.id;
    },
    languages: get("localeModule/getLanguagesOptions"),
    courseTranslates: get("coursesModule/getCourseInfoTranslates"),
    isAnnouncementEnabled: get("configModule/config@isAnnouncementEnabled"),
    availableLocales() {
      const languagesWithMain = [
        ...(this.course.languages || []),
        this.course.locale,
      ];

      const languages = this.languages.filter(
        (lang) => !languagesWithMain.includes(lang.id)
      );

      return languages.map((lang) => {       
        const isAlreadyUsed =
          languagesWithMain.includes(lang.id) ||
          this.courseTranslates.find((l) => l.locale === lang.id);

        return {
          ...lang,
          disabled: isAlreadyUsed, // ← aquí añadimos el campo
        };
      });
    },

    isEnableTranslation() {
      if (this.selectedLocale === "") {
        return false;
      }
      const translation = this.availableLocales.find(
        (locale) => locale.id === this.selectedLocale
      );

      if (!translation) {
        return false;
      }

      if (translation.disabled) {
        return false;
      }

      return true;
    },
  },
  watch: {
    courseId() {
      this.activeTab = "info";
      this.loadCourse();
      this.initActions();
    },

    course(newCourse) {
      let isTranslationTab = false;
      for (const tabsKey in this.tabs) {
        if (this.tabs[tabsKey].name == "translations") {
          isTranslationTab = true;
        }
      }
      if (newCourse && !newCourse.translation && !isTranslationTab) {
        const newTabs = [
          ...this.tabs.slice(0, 5),
          {
            name: "translations",
            label: this.$t("TRANSLATIONS.LABEL_IN_PLURAL"),
            component: "Translations",
          },
          ...this.tabs.slice(5),
        ];
        this.tabs = newTabs;
      }
      if(newCourse && newCourse.typeCourseId === 2){
        this.tabs = [
          {
            name: "announcements",
            label: this.$t("ANNOUNCEMENT.HOME.TITLE"),
            component: "Announcements",
          },
        ];
        this.activeTab = 'announcements';
        console.log(this.tabs)
      }
      if (newCourse?.id) {
        const translateAction = this.actions.find(
          (action) => action.event === EVENT_NAMES.TRANSLATE_COURSE
        );
        const shareAction = this.actions.find(
          (action) => action.event === EVENT_NAMES.SHARE_COURSE
        );
        if (translateAction && shareAction) {
          translateAction.bind.disabled =
            !!this.course.translation ||
            !this.$auth.hasPermission(COURSE_PERMISSIONS.CREATE);
          shareAction.bind.disabled = !this.$auth.hasPermission(
            COURSE_PERMISSIONS.SHARE
          );
          this.addActions();
        }
      }
    },
  },
  created() {
    this.initActions();
  },
  mounted() {
    const tabData = getTabData();
    this.$auth.setPermissionList(COURSE_ACTIONS_BY_ROLE);
    this.activeTab = tabData?.tab ?? "info";
    setTabData(this.courseId, this.activeTab);

    this.loadCourse();
    if (this.useGlobalEventBus) {
      this.$eventBus.$on(EVENT_NAMES.BACK_HOME, () => {
        this.$router.push({ name: this.ROUTE_NAMES.COURSES });
      });
      this.$eventBus.$on(EVENT_NAMES.MODIFY_COURSE, () => {
        this.$router.push({
          name: this.ROUTE_NAMES.UPDATE_COURSE,
          params: { id: this.course.id },
        });
      });
      this.$eventBus.$on(EVENT_NAMES.SHARE_COURSE, () => {
        this.shareCourse(this.course.id);
      });
      this.$eventBus.$on(EVENT_NAMES.TRANSLATE_COURSE, () => {
        const translateAction = this.actions.find(
          (action) => action.event === EVENT_NAMES.TRANSLATE_COURSE
        );
        if (!translateAction.bind.disabled) this.openModal();
      });
    }
  },
  beforeDestroy() {
    this.$auth.setPermissionList({});
    this.$eventBus.$off(EVENT_NAMES.BACK_HOME);
    this.$eventBus.$off(EVENT_NAMES.MODIFY_COURSE);
    this.$eventBus.$off(EVENT_NAMES.TRANSLATE_COURSE);
    this.$eventBus.$off(EVENT_NAMES.SHARE_COURSE);
  },
  methods: {
    async loadCourse() {
      await this.$store.dispatch(
        "coursesModule/fetchCourseById",
        this.courseId
      );
      if (!this.course?.id) return null;

      const newTabs = this.course.translation
        ? this.tabsAvailable.filter((tab) => tab.name !== "translations")
        : [...this.tabsAvailable];
      if(this.course?.typeCourseId !== 2){
        this.tabs = newTabs?.filter((tab) => {
          if (tab.name === "seasons") {
            return this.$auth.hasPermission(COURSE_PERMISSIONS.SEASONS) || this.canManageCourseContent;
          }
         else if (
              tab.name === "people" &&
              !this.$auth.hasPermission(COURSE_PERMISSIONS.PERSONS)
          )
            return false;
          else if (
              tab.name === "statistics" &&
              !this.$auth.hasPermission(COURSE_PERMISSIONS.STATS)
          )
            return false;
          else if (tab.name === "announcements")
          {
           return this.isAnnouncementEnabled && this.$auth.hasPermission(COURSE_PERMISSIONS.ANNOUNCEMENTS)
          }

          return true;
        });
      }

      if(this.course?.typeCourseId === 2){
        this.tabs = newTabs?.filter((tab) => {
          if (tab.name === "announcements") {
            return this.isAnnouncementEnabled && this.$auth.hasPermission(COURSE_PERMISSIONS.ANNOUNCEMENTS)
          }
          return false;
        }
        );
      }

      const translateAction = this.actions.findIndex(
        (action) => action.event === EVENT_NAMES.TRANSLATE_COURSE
      );
      const shareAction = this.actions.findIndex(
        (action) => action.event === EVENT_NAMES.SHARE_COURSE
      );

      if (translateAction > -1 && shareAction > -1) {
        this.actions[translateAction].bind.disabled =
          !!this.course.translation ||
          !this.$auth.hasPermission(COURSE_PERMISSIONS.CREATE);
        this.actions[shareAction].bind.disabled = !this.$auth.hasPermission(
          COURSE_PERMISSIONS.SHARE
        );
        this.addActions();
      }
    },
    initActions() {
      this.addActions();
      this.$store.dispatch("contentTitleModule/addRoute", {
        routeName: this.$route.name,
        params: {
          linkName: this.$route.params.name,
          params: this.$route.params,
        },
      });
    },
    setActiveTab(tabName) {
      setTabData(this.courseId, tabName);
      this.activeTab = tabName;
    },
    async shareCourse(id) {
      try {
        this.$toast.info(this.$t("EXECUTING"));
        const url = await this.$store.dispatch("coursesModule/shareCourse", id);
        await navigator.clipboard.writeText(url);
        this.$toast.success(this.$t("URL_COPIED"));
      } catch (e) {
        this.$toast.error(this.$t("COPY_FAILED"));
      }
    },
    openModal() {
      if (!this.isEveryChapterContentCompleted) {
        this.$toast.warning(this.$t("COURSE.ALERT_COURSE_CONTENT_INCOMPLETE"));
        return;
      }
      this.showModal = true;
      this.$nextTick(() => {
        $("#modal-new-language").modal("show");
      });
    },
    closeModal() {
      this.showModal = false;
      $("#modal-new-language").modal("hide");
      this.selectedLocale = "";
    },
    async handleSubmit() {
      if (!this.selectedLocale) {
        this.$toast.error(this.$t("COURSE.SELECT_TRANSLATE_REQUIRED"));
        return;
      }
      this.isSubmitting = true;
      try {
        await this.$store.dispatch("coursesModule/translateCourse", {
          id: this.course.id,
          body: { language: this.selectedLocale },
        });
        this.$toast.success(this.$t("COURSE.COURSE_TRANSLATE_SUCCESS"));
        await this.loadCourse();
        await this.$store.dispatch(
          "coursesModule/fetchCourseTranslateById",
          this.courseId
        );
        this.closeModal();
      } catch (error) {
        this.$toast.error(this.$t("COURSE.COURSE_TRANSLATE_FAILED"));
      } finally {
        this.isSubmitting = false;
      }
    },
    addActions() {
      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions: this.actions,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.ViewCourse {
  &--content {
    @include nav-bar-style;
  }
}
</style>
