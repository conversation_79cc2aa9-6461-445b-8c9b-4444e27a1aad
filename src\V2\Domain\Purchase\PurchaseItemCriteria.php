<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class PurchaseItemCriteria extends CriteriaWithUuid
{
    private ?Uuid $purchaseId = null;
    private ?UuidCollection $purchaseIds = null;
    private ?Uuid $purchasableItemId = null;
    private ?Money $minPrice = null;
    private ?Money $maxPrice = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->purchaseId
            && null === $this->purchaseIds
            && null === $this->purchasableItemId
            && null === $this->minPrice
            && null === $this->maxPrice;
    }

    public function filterByPurchaseId(Uuid $purchaseId): self
    {
        $this->purchaseId = $purchaseId;

        return $this;
    }

    public function getPurchaseId(): ?Uuid
    {
        return $this->purchaseId;
    }

    public function filterByPurchaseIds(UuidCollection $purchaseIds): self
    {
        $this->purchaseIds = $purchaseIds;

        return $this;
    }

    public function getPurchaseIds(): ?UuidCollection
    {
        return $this->purchaseIds;
    }

    public function filterByPurchasableItemId(Uuid $purchasableItemId): self
    {
        $this->purchasableItemId = $purchasableItemId;

        return $this;
    }

    public function getPurchasableItemId(): ?Uuid
    {
        return $this->purchasableItemId;
    }

    public function filterByMinPrice(Money $minPrice): self
    {
        $this->minPrice = $minPrice;

        return $this;
    }

    public function getMinPrice(): ?Money
    {
        return $this->minPrice;
    }

    public function filterByMaxPrice(Money $maxPrice): self
    {
        $this->maxPrice = $maxPrice;

        return $this;
    }

    public function getMaxPrice(): ?Money
    {
        return $this->maxPrice;
    }
}
