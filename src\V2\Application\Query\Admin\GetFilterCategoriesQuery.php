<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\V2\Domain\Bus\Query;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;

readonly class GetFilterCategoriesQuery implements Query
{
    public function __construct(
        private FilterCategoryCriteria $criteria,
    ) {
    }

    public function getCriteria(): FilterCategoryCriteria
    {
        return $this->criteria;
    }
}
