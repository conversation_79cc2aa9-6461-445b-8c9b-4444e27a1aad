<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingDTOMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Application\Command\UpdateVirtualMeeting;
use App\V2\Application\CommandHandler\UpdateVirtualMeetingCommandHandler;
use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class UpdateVirtualMeetingCommandHandlerTest extends TestCase
{
    private const string DEFAULT_URL = 'https://example.com/meeting';
    private const string UPDATED_URL = 'https://updated.example.com/meeting';

    /**
     * @throws Exception
     */
    private function getHandler(
        ?VirtualMeetingRepository $virtualMeetingRepository = null,
    ): UpdateVirtualMeetingCommandHandler {
        return new UpdateVirtualMeetingCommandHandler(
            $virtualMeetingRepository ?? $this->createMock(VirtualMeetingRepository::class),
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws \DateMalformedStringException
     */
    private function getCommand(
        ?Uuid $virtualMeetingId = null,
        ?VirtualMeetingDTO $virtualMeetingDto = null,
    ): UpdateVirtualMeeting {
        return new UpdateVirtualMeeting(
            $virtualMeetingId ?? UuidMother::create(),
            $virtualMeetingDto ?? VirtualMeetingDTOMother::create(
                type: VirtualMeetingType::Fixed,
                url: self::UPDATED_URL,
            ),
        );
    }

    /**
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(UpdateVirtualMeetingCommandHandler::class, $handler);
    }

    /**
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     * @throws CriteriaException
     */
    public function testHandleUpdatesAndPersistsVirtualMeeting(): void
    {
        $virtualMeetingId = UuidMother::create();
        $existingVirtualMeeting = VirtualMeetingMother::create(
            id: $virtualMeetingId,
            type: VirtualMeetingType::Fixed,
            url: self::DEFAULT_URL,
        );

        $updatedDto = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: self::UPDATED_URL,
        );
        $command = $this->getCommand($virtualMeetingId, $updatedDto);

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $virtualMeetingRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (VirtualMeetingCriteria $criteria) use ($virtualMeetingId) {
                return null !== $criteria->getId() && $criteria->getId()->equals($virtualMeetingId);
            }))
            ->willReturn($existingVirtualMeeting);

        $virtualMeetingRepository->expects($this->once())
            ->method('put')
            ->with($this->callback(function (VirtualMeeting $virtualMeeting) use ($updatedDto, $virtualMeetingId) {
                return $virtualMeeting->getId()->equals($virtualMeetingId)
                    && $virtualMeeting->getType() === $updatedDto->getType()
                    && $virtualMeeting->getStartAt() === $updatedDto->getStartAt()
                    && $virtualMeeting->getFinishAt() === $updatedDto->getFinishAt()
                    && $virtualMeeting->getUrl() === $updatedDto->getUrl();
            }));

        $handler = $this->getHandler(
            virtualMeetingRepository: $virtualMeetingRepository,
        );

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowsVirtualMeetingNotFoundExceptionWhenVirtualMeetingNotExists(): void
    {
        $virtualMeetingId = UuidMother::create();
        $updatedDto = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: self::UPDATED_URL,
        );
        $command = $this->getCommand($virtualMeetingId, $updatedDto);

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $virtualMeetingRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new VirtualMeetingNotFoundException());

        $handler = $this->getHandler(
            virtualMeetingRepository: $virtualMeetingRepository,
        );

        $this->expectException(VirtualMeetingNotFoundException::class);

        $handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws \DateMalformedStringException
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     * @throws VirtualMeetingNotFoundException
     * @throws CriteriaException
     */
    #[DataProvider('invalidVirtualMeetingExceptionProvider')]
    public function testHandleThrowsExceptionOnInvalidDTOUrlRequiredForFixedType(
        VirtualMeetingDTO $dto,
        InvalidVirtualMeetingException $expectedException,
    ): void {
        $virtualMeetingId = UuidMother::create();
        $existingVirtualMeeting = VirtualMeetingMother::create(
            id: $virtualMeetingId,
            type: VirtualMeetingType::Fixed,
            url: self::DEFAULT_URL,
        );

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $virtualMeetingRepository->method('findOneBy')
            ->willReturn($existingVirtualMeeting);

        $handler = $this->getHandler(virtualMeetingRepository: $virtualMeetingRepository);

        $this->expectExceptionObject($expectedException);

        $handler->handle($this->getCommand($virtualMeetingId, $dto));
    }

    /**
     * @throws \DateMalformedStringException
     */
    public static function invalidVirtualMeetingExceptionProvider(): \Generator
    {
        yield 'URL required for Fixed type' => [
            'dto' => VirtualMeetingDTOMother::create(
                type: VirtualMeetingType::Fixed,
                url: null // URL is required for Fixed type
            ),
            'expectedException' => InvalidVirtualMeetingException::urlRequiredForFixedType(),
        ];

        $startAt = new \DateTimeImmutable('2024-01-01 10:00:00');
        $finishAt = new \DateTimeImmutable('2024-01-01 09:00:00'); // Before start time
        yield 'Finish at must be after start at' => [
            'dto' => VirtualMeetingDTOMother::create(
                type: VirtualMeetingType::Fixed,
                startAt: $startAt,
                finishAt: $finishAt,
                url: self::UPDATED_URL,
            ),
            'expectedException' => InvalidVirtualMeetingException::finishAtMustBeAfterStartAt(
                $startAt,
                $finishAt,
            ),
        ];
    }

    /**
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowsInfrastructureExceptionWhenRepositoryFails(): void
    {
        $virtualMeetingId = UuidMother::create();
        $existingVirtualMeeting = VirtualMeetingMother::create(
            id: $virtualMeetingId,
            type: VirtualMeetingType::Fixed,
            url: self::DEFAULT_URL,
        );

        $updatedDto = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: self::UPDATED_URL,
        );
        $command = $this->getCommand($virtualMeetingId, $updatedDto);

        $repositoryException = new InfrastructureException('Repository failure');
        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $virtualMeetingRepository->method('findOneBy')
            ->willReturn($existingVirtualMeeting);
        $virtualMeetingRepository->method('put')
            ->willThrowException($repositoryException);

        $handler = $this->getHandler(
            virtualMeetingRepository: $virtualMeetingRepository,
        );

        $this->expectExceptionObject(
            $repositoryException
        );

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     * @throws CriteriaException
     */
    public function testHandleAllowsChangingDatesAndUrl(): void
    {
        $virtualMeetingId = UuidMother::create();
        $existingVirtualMeeting = VirtualMeetingMother::create(
            id: $virtualMeetingId,
            type: VirtualMeetingType::Fixed,
            url: self::DEFAULT_URL,
        );

        $newStartAt = new \DateTimeImmutable('2024-02-01 10:00:00');
        $newFinishAt = new \DateTimeImmutable('2024-02-01 12:00:00');

        $updatedDto = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            startAt: $newStartAt,
            finishAt: $newFinishAt,
            url: self::UPDATED_URL,
        );
        $command = $this->getCommand($virtualMeetingId, $updatedDto);

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $virtualMeetingRepository->method('findOneBy')
            ->willReturn($existingVirtualMeeting);

        $virtualMeetingRepository->expects($this->once())
            ->method('put')
            ->with($this->callback(
                function (
                    VirtualMeeting $virtualMeeting
                ) use (
                    $virtualMeetingId,
                    $newStartAt,
                    $newFinishAt
                ) {
                    return $virtualMeeting->getId()->equals($virtualMeetingId)
                    && VirtualMeetingType::Fixed === $virtualMeeting->getType()
                    && $virtualMeeting->getStartAt() === $newStartAt
                    && $virtualMeeting->getFinishAt() === $newFinishAt
                    && self::UPDATED_URL === $virtualMeeting->getUrl();
                }
            ));

        $handler = $this->getHandler(
            virtualMeetingRepository: $virtualMeetingRepository,
        );

        $handler->handle($command);
    }
}
