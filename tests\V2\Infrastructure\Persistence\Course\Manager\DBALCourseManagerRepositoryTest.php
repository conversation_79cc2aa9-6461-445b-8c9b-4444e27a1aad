<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Course\Manager;

use App\Tests\V2\Domain\Course\Manager\CourseManagerRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Infrastructure\Persistence\Course\Manager\DBALCourseManagerRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALCourseManagerRepositoryTest extends CourseManagerRepositoryTestCase
{
    private const string TABLE_NAME = 'course_manager';
    private Connection $connection;

    /**
     * @throws DBALException
     * @throws SchemaException
     */
    protected function getRepository(): CourseManagerRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALCourseManagerRepository(
            connection: $this->connection,
            courseManagerTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws SchemaException
     * @throws DBALException
     */
    private function createTable(): void
    {
        $this->connection->executeQuery('DROP TABLE IF EXISTS ' . static::TABLE_NAME);

        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('user_id', 'integer');
        $table->addColumn('course_id', 'integer');

        $table->setPrimaryKey(['user_id', 'course_id']);

        $this->connection->createSchemaManager()->createTable($table);
    }

    protected function addItem(CourseManager $courseManager): void
    {
        try {
            $this->connection->insert(
                table: self::TABLE_NAME,
                data: [
                    'user_id' => $courseManager->getUserId()->value(),
                    'course_id' => $courseManager->getCourseId()->value(),
                ]
            );
        } catch (DBALException $e) {
            $this->fail($e->getMessage());
        }
    }
}
