<?php

declare(strict_types=1);

namespace App\V2\Application\DTO;

readonly class TimeZonesDto
{
    /**
     * @param array<string> $timezones
     */
    public function __construct(
        private array $timezones,
        private string $default,
    ) {
    }

    /**
     * @return array<string>
     */
    public function getTimezones(): array
    {
        return $this->timezones;
    }

    public function getDefault(): string
    {
        return $this->default;
    }
}
