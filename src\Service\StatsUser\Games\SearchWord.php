<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\OrdenarMenormayor;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class SearchWord implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter)
    {
        $attempts = [];
        $searchWordRepository = $this->em->getRepository(OrdenarMenormayor::class);
        $lastDateInQuestions = null;
        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $at) {
                    $questionFind = $searchWordRepository->findWithDeleted($at['questionId']);
                    if (!$questionFind) {
                        continue;
                    }
                    $timeInQuestion = ceil($this->getTimeTotalAttemps($at['attempts']));
                    $lastDateInQuestions = $at['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    $questions[] = [
                        'id' => $questionFind?->getId(),
                        'question' => $questionFind?->getTitle(),
                        'correct' => $at['correct'] ?? false,
                        'answers' => $this->getAnswersUser($at['attempts'], $questionFind),
                    ];

                    $timeTotalAttempt += $timeInQuestion ?? 0;
                }
                if (!$questions) {
                    continue;
                }
                $attemptForCalculateScore = array_merge(['answers' => $attempt], $this->getTotalWordAndTimeLetterSoup($userCourseChapter));

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'date' => $lastDateInQuestions,
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswersUser($attempts, $question): array
    {
        if (empty($attempts)) {
            return [];
        }

        $answers = [];
        foreach ($attempts as $attempt) {
            $formatedWordsArray = $question?->getWordsArray() ? str_replace(',', ', ', $question->getWordsArray()) : null;
            $answers[] = [
                'answer' => $formatedWordsArray,
                'userAnswer' => $attempt['word'] ?? null,
                'correct' => $attempt['correct'] ?? false,
                'incorrect' => !$attempt['correct'] ?? false,
            ];
        }

        return $answers;
    }

    private function getTimeTotalAttemps($attemps)
    {
        if (empty($attemps)) {
            return 0;
        }

        $time = 0;
        foreach ($attemps as $attemp) {
            $time += $attemp['time'];
        }

        return $time;
    }

    private function getTotalWordAndTimeLetterSoup(UserCourseChapter $userCourseChapter): array
    {
        $letterSoup = $this->em->getRepository(OrdenarMenormayor::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

        $words = 0;
        $time = 0;
        foreach ($letterSoup as $word) {
            $wordsByQuestion = explode(',', $word->getWordsArray());
            $time += $word->getTime();
            foreach ($wordsByQuestion as $word) {
                ++$words;
            }
        }

        return [
            'words' => $words,
            'timeTotal' => $time,
        ];
    }
}
