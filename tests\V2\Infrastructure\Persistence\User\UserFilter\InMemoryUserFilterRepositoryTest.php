<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\User\UserFilter;

use App\Tests\V2\Domain\User\UserFilter\UserFilterRepositoryTestCase;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Infrastructure\Persistence\User\UserFilter\InMemoryUserFilterRepository;

class InMemoryUserFilterRepositoryTest extends UserFilterRepositoryTestCase
{
    protected function getRepository(): UserFilterRepository
    {
        return new InMemoryUserFilterRepository();
    }
}
