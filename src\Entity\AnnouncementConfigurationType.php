<?php

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Imageable;
use App\Behavior\Timestampable;
use App\Repository\AnnouncementConfigurationTypeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Gedmo\Mapping\Annotation as Gedmo;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;

/**
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @ORM\Entity(repositoryClass=AnnouncementConfigurationTypeRepository::class)
 * @Vich\Uploadable()
 */
class AnnouncementConfigurationType implements TranslatableInterface
{
    use TranslatableTrait;

    public const ID_ENABLE_TEMPORALIZATION = 1;
    public const ID_ENABLE_SUBSIDIZED_COURSE = 2;
    public const ID_ENABLE_CHAT = 3;
    public const ID_ENABLE_NOTIFICATION = 4;
    public const ID_ENABLE_SMS = 5;
    public const ID_ENABLE_FORUM = 6;
    public const ID_ENABLE_CERTIFICATE = 7;
    public const ID_ENABLE_ALERTS_TUTOR = 8;
    public const ID_ENABLE_SURVEY = 9;
    public const ID_ALLOW_ACTIVE_COURSE_AT_END = 10;
    public const ID_DIGITAL_SIGNATURE = 11;
    public const ID_COST = 12;
    public const ID_ENABLE_EMAIL_NOTIFICATION_ON_ANNOUNCEMENT = 13;
    public const ID_ENABLE_NOTIFICATION_ON_ANNOUNCEMENT = 14;
    public const ID_INCLUDE_OBJ_CONTENT_CERTIFICATE = 15;
    public const ID_INCLUDE_DNI_IN_CERTIFICATE = 16;
    public const ID_TEMPLATE_XLSX_IBEROSTAR = 17;
    public const ID_REPORT_ZIP = 18;


    use Blamable;
    use Timestampable;
    use Imageable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"list"})
     */
    private $description;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"list"})
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="configuration_client_image", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active;

    /**
     * @ORM\ManyToOne(targetEntity=ConfigurationClientAnnouncement::class, inversedBy="announcementConfigurationTypes")
     * @ORM\JoinColumn(nullable=false)
     */
    private $configurationClientAnnouncement;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementConfiguration::class, mappedBy="configuration", orphanRemoval=true)
     */
    private $announcementConfigurations;

    /**
     * @ORM\OneToMany(targetEntity=TypeCourseAnnouncementStepConfiguration::class, mappedBy="announcementConfigurationType")
     */
    private $typeCourseAnnouncementStepConfigurations;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $code;

    public function __construct()
    {
        $this->announcementConfigurations = new ArrayCollection();
        $this->typeCourseAnnouncementStepConfigurations = new ArrayCollection();
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @deprecated The ConfigurationClientAnnouncement is no longer used in the application.
     * The AnnouncementConfigurationType is now directly the one that is used in the client.
     * This is used to check if the configuration is active, now we only need to check the isActive method of this class
     *
     * @return ConfigurationClientAnnouncement|null
     */
    public function getConfigurationClientAnnouncement(): ?ConfigurationClientAnnouncement
    {
        return $this->configurationClientAnnouncement;
    }

    public function setConfigurationClientAnnouncement(
        ?ConfigurationClientAnnouncement $configurationClientAnnouncement
    ): self {
        $this->configurationClientAnnouncement = $configurationClientAnnouncement;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementConfiguration>
     */
    public function getAnnouncementConfigurations(): Collection
    {
        return $this->announcementConfigurations;
    }

    public function addAnnouncementConfiguration(AnnouncementConfiguration $announcementConfiguration): self
    {
        if (!$this->announcementConfigurations->contains($announcementConfiguration)) {
            $this->announcementConfigurations[] = $announcementConfiguration;
            $announcementConfiguration->setConfiguration($this);
        }

        return $this;
    }

    public function removeAnnouncementConfiguration(AnnouncementConfiguration $announcementConfiguration): self
    {
        if ($this->announcementConfigurations->removeElement($announcementConfiguration)) {
            // set the owning side to null (unless already changed)
            if ($announcementConfiguration->getConfiguration() === $this) {
                $announcementConfiguration->setConfiguration(null);
            }
        }

        return $this;
    }

    /**
     * @Groups({"list"})
     * @return String
     */
    public function getType(): string
    {
        return $this->getConfigurationClientAnnouncement()?->getName()
            ?? $this->getName()
            ?? '' ;
    }

    public function __toString()
    {
        return 'announcement-configuration-type-' . $this->getId();
    }

    /**
     * @return Collection<int, TypeCourseAnnouncementStepConfiguration>
     */
    public function getTypeCourseAnnouncementStepConfigurations(): Collection
    {
        return $this->typeCourseAnnouncementStepConfigurations;
    }

    public function addTypeCourseAnnouncementStepConfiguration(
        TypeCourseAnnouncementStepConfiguration $typeCourseAnnouncementStepConfiguration
    ): self {
        if (!$this->typeCourseAnnouncementStepConfigurations->contains($typeCourseAnnouncementStepConfiguration)) {
            $this->typeCourseAnnouncementStepConfigurations[] = $typeCourseAnnouncementStepConfiguration;
            $typeCourseAnnouncementStepConfiguration->setAnnouncementConfigurationType($this);
        }

        return $this;
    }

    public function removeTypeCourseAnnouncementStepConfiguration(
        TypeCourseAnnouncementStepConfiguration $typeCourseAnnouncementStepConfiguration
    ): self {
        if ($this->typeCourseAnnouncementStepConfigurations->removeElement($typeCourseAnnouncementStepConfiguration)) {
            // set the owning side to null (unless already changed)
            if ($typeCourseAnnouncementStepConfiguration->getAnnouncementConfigurationType() === $this) {
                $typeCourseAnnouncementStepConfiguration->setAnnouncementConfigurationType(null);
            }
        }

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }
}
