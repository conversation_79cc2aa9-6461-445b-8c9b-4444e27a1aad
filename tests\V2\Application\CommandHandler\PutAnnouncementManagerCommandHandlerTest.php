<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Entity\User;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Application\Command\PutAnnouncementManager;
use App\V2\Application\CommandHandler\PutAnnouncementManagerCommandHandler;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Exception\ManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\PutAnnouncementManagerException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PutAnnouncementManagerCommandHandlerTest extends TestCase
{
    private PutAnnouncementManagerCommandHandler $handler;
    private AnnouncementManagerRepository|MockObject $announcementManagerRepository;
    private UserRepository|MockObject $userRepository;
    private LegacyAnnouncementRepository|MockObject $legacyAnnouncementRepository;
    private SettingsService|MockObject $settingsService;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        $this->settingsService = $this->createMock(SettingsService::class);

        $this->handler = new PutAnnouncementManagerCommandHandler(
            $this->announcementManagerRepository,
            $this->userRepository,
            $this->legacyAnnouncementRepository,
            $this->settingsService,
        );
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws ManagerNotFoundException
     * @throws PutAnnouncementManagerException
     * @throws ManagerNotAuthorizedException
     * @throws UserNotFoundException
     * @throws CriteriaException
     */
    public function testHandleSuccess(): void
    {
        $announcementId = 1;
        $userId = 2;
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_ADMIN]);
        $user = UserMother::create(id: $userId, roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: $announcementId, course: $course);

        $command = new PutAnnouncementManager(
            announcementId: new Id($announcementId),
            userId: new Id($userId),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserCriteria::createById(new Id($userId)))
            ->willReturn($user);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $announcementId])
            ->willReturn($announcement);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(AnnouncementManagerCriteria::createEmpty()
                ->filterByUserId($user->getId())
                ->filterByAnnouncementId($announcement->getId()))
            ->willThrowException(new AnnouncementManagerNotFoundException());

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('insert')
            ->with($this->callback(function ($announcementManager) use ($userId, $announcementId) {
                return $announcementManager->getUserId()->value() === $userId
                    && $announcementManager->getAnnouncementId()->value() === $announcementId;
            }));

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws PutAnnouncementManagerException
     * @throws CriteriaException
     */
    public function testHandleThrowsManagerNotAuthorizedExceptionWhenSettingDisabled(): void
    {
        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: UserMother::create(roles: [User::ROLE_ADMIN]),
        );

        $announcement = AnnouncementMother::create(id: 1);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(false);

        $this->expectException(ManagerNotAuthorizedException::class);
        $this->expectExceptionMessage('Announcement manager sharing is disabled');

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws ManagerNotAuthorizedException
     * @throws PutAnnouncementManagerException
     * @throws CriteriaException
     */
    public function testHandleThrowsAnnouncementNotFoundExceptionWhenAnnouncementNotExists(): void
    {
        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: UserMother::create(roles: [User::ROLE_ADMIN]),
        );

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn(null);

        $this->expectException(AnnouncementNotFoundException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws PutAnnouncementManagerException
     * @throws CriteriaException
     * @throws ManagerNotAuthorizedException
     */
    public function testHandleThrowsExceptionWhenUserDoesNotExist(): void
    {
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);

        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id(9999),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserCriteria::createById(new Id(9999)))
            ->willThrowException(new UserNotFoundException('User not found'));

        $this->expectException(PutAnnouncementManagerException::class);
        $this->expectExceptionMessage('User not found');

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws PutAnnouncementManagerException
     * @throws ManagerNotAuthorizedException
     * @throws CriteriaException
     */
    public function testHandleThrowsManagerNotFoundExceptionWhenUserIsNotManager(): void
    {
        $user = UserMother::create(id: 2, roles: [User::ROLE_USER]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);

        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: UserMother::create(id: 3, roles: [User::ROLE_ADMIN]),
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcement);

        $this->expectException(PutAnnouncementManagerException::class);
        $this->expectExceptionMessage('User is not a manager');

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws PutAnnouncementManagerException
     * @throws CriteriaException
     */
    public function testThrowsExceptionWhenManagerAssignsToUnauthorizedAnnouncement(): void
    {
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $announcement->setCreatedBy(UserMother::create(id: 4, roles: [User::ROLE_ADMIN])); // Different creator

        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->expectException(ManagerNotAuthorizedException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws ManagerNotAuthorizedException
     * @throws CriteriaException
     */
    public function testHandleThrowsPutAnnouncementManagerExceptionWhenManagerAlreadyExists(): void
    {
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_ADMIN]);
        $user = UserMother::create(id: 2, roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);

        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcement);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn(AnnouncementManagerMother::create());

        $this->expectException(PutAnnouncementManagerException::class);
        $this->expectExceptionMessage('User is already a manager of this announcement');

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws ManagerNotAuthorizedException
     * @throws CriteriaException
     */
    public function testHandleThrowsPutAnnouncementManagerExceptionWhenTryingToAssignCreator(): void
    {
        $creator = UserMother::create(id: 2, roles: [User::ROLE_MANAGER]);
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $announcement->setCreatedBy($creator);

        $command = new PutAnnouncementManager(
            announcementId: new Id(1),
            userId: new Id($creator->getId()),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($creator);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcement);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new AnnouncementManagerNotFoundException());

        $this->expectException(PutAnnouncementManagerException::class);
        $this->expectExceptionMessage('Cannot assign the creator of the announcement as a manager');

        $this->handler->handle($command);
    }
}
