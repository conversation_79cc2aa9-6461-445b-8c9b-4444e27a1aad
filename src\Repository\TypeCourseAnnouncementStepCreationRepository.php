<?php

namespace App\Repository;

use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementConfigurationTypeTranslation;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseAnnouncementStepCreation;
use App\Entity\TypeCourseAnnouncementStepCreationTranslation;
use App\Entity\TypeCourseTranslation;
use App\Enum\TypeCourse as EnumTypeCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeCourseAnnouncementStepCreation>
 *
 * @method TypeCourseAnnouncementStepCreation|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeCourseAnnouncementStepCreation|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeCourseAnnouncementStepCreation[]    findAll()
 * @method TypeCourseAnnouncementStepCreation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeCourseAnnouncementStepCreationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeCourseAnnouncementStepCreation::class);
    }

    public function add(TypeCourseAnnouncementStepCreation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeCourseAnnouncementStepCreation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getStepsOfCourseInAnnouncement($source = EnumTypeCourse::INTERN, $locale = null): array
    {
        $criteria = ['active' => true];

        if ($source !== null) {
            $criteria['denomination'] = $source;
        }

        $typesCourse = $this->getEntityManager()->getRepository(TypeCourse::class)
            ->findBy($criteria);

        $typeCourseArray = [];

        foreach ($typesCourse as $typeCourse) {
            $name = null;
            if (!empty($locale)) {
                /** @var TypeCourseTranslation $translation */
                $translation = $typeCourse->translate($locale);
                $name = $translation->getName();
            }

            $typeCourseArray[$typeCourse->getType()] = [];

            foreach ($typeCourse->getTypeCourseAnnouncementStepCreations() as $stepCreation) {
                if (!$stepCreation->isActive()) {
                    continue;
                }

                if (!empty($locale)) {
                    /** @var TypeCourseAnnouncementStepCreationTranslation $translation */
                    $translation = $stepCreation->translate($locale);
                    $name = $translation->getName();
                }

                $typeCourseArray[$typeCourse->getType()][] = [
                    'id' => $stepCreation->getId(),
                    'name' => $name ?? $stepCreation->getName(),
                    "required" => $stepCreation->isIsRequired(),
                    "extra" => $stepCreation->getAnnouncementStepCreation()->getExtra(),
                    'active' => $stepCreation->isActive(),
                    'configurations' => $this->getConfigurationsByStep($stepCreation, $locale)
                ];
            }
        }
        return $typeCourseArray;
    }

    private function getConfigurationsByStep(TypeCourseAnnouncementStepCreation $stepCreation, $locale = null): array
    {
        $announcementConfigurations = [];

        foreach ($stepCreation->getTypeCourseAnnouncementStepConfigurations() as $configuration) {
            if (
                null === $configuration->getAnnouncementConfigurationType()
                || !$configuration->getAnnouncementConfigurationType()->isActive()
            ) {
                continue;
            }

            $announcementConfigurationType = $configuration->getAnnouncementConfigurationType();

            if (!empty($locale)) {
                /** @var AnnouncementConfigurationTypeTranslation $translation */
                $translation = $announcementConfigurationType->translate($locale);
                $name = $translation->getName();
                $description = $translation->getDescription();
            }

            $announcementConfigurations[] = [
                'id' => $announcementConfigurationType->getId(),
                'name' => $name ?? $announcementConfigurationType->getName(),
                'description' => $description ?? $announcementConfigurationType->getDescription(),
                'image' => 'assets_announcement/configuration/' . $announcementConfigurationType->getImage(),
            ];
        }

        return $announcementConfigurations;
    }
}
