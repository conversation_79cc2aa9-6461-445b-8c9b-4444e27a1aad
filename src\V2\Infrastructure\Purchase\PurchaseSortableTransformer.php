<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase;

use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Infrastructure\Shared\QueryParamTransformer\LifeCycleSortableTransformer;
use App\V2\Infrastructure\Shared\QueryParamTransformer\SortableTransformer;

class PurchaseSortableTransformer extends SortableTransformer
{
    private const array SORTABLE_FIELDS = [
        'id' => 'id',
        'status' => 'status',
        'created_at' => 'createdAt',
        'amount' => 'amount',
    ];

    public static function getSortableFields(): array
    {
        return self::SORTABLE_FIELDS;
    }

    public static function toSortableField(string $sortBy): SortableField
    {
        if (\array_key_exists($sortBy, LifeCycleSortableTransformer::getSortableFields())) {
            return LifeCycleSortableTransformer::toSortableField($sortBy);
        }

        $sortableFields = self::getSortableFields();

        if (!\array_key_exists($sortBy, $sortableFields)) {
            throw InvalidSortException::invalidField($sortBy);
        }

        return new SortableField($sortableFields[$sortBy]);
    }
}
