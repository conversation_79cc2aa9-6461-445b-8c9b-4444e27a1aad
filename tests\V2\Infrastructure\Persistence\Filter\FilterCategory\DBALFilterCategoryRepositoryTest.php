<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter\FilterCategory;

use App\Tests\V2\Domain\Filter\FilterCategory\FilterCategoryRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Infrastructure\Persistence\Filter\FilterCategory\DBALFilterCategoryRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALFilterCategoryRepositoryTest extends FilterCategoryRepositoryTestCase
{
    private const string TABLE_NAME = 'filter_category';
    private Connection $connection;

    /**
     * @throws DBALException
     * @throws SchemaException
     */
    protected function getRepository(): FilterCategoryRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALFilterCategoryRepository(
            connection: $this->connection,
            filterCategoryTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws SchemaException
     * @throws DBALException
     */
    private function createTable(): void
    {
        $this->connection->executeQuery('DROP TABLE IF EXISTS ' . static::TABLE_NAME);
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'integer');
        $table->addColumn('parent_id', 'integer', ['notnull' => false]);
        $table->addColumn('name', 'string', ['length' => 255]);
        $table->addColumn('sort', 'integer');

        $table->setPrimaryKey(['id']);
        $this->connection->createSchemaManager()->createTable($table);
    }

    protected function addFilterCategory(FilterCategory $category): void
    {
        try {
            $this->connection->insert(
                table: self::TABLE_NAME,
                data: [
                    'id' => $category->getId()->value(),
                    'parent_id' => $category->getParentId()?->value(),
                    'name' => $category->getName(),
                    'sort' => $category->getSort(),
                ]
            );
        } catch (DBALException $e) {
            $this->fail($e->getMessage());
        }
    }
}
