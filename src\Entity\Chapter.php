<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\ChapterContent;
use App\Enum\Games;
use App\Repository\ChapterRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=ChapterRepository::class)
 *
 * @Vich\Uploadable()
 */
class Chapter
{
    use AtAndBy;
    use Imageable;

    public const THUMBNAILS = ['small', 'medium'];

    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"detail"})
     */
    private ?int $id = null;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"detail", "contents"})
     */
    private $title;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="chapters")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $course;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"detail", "progress", "contents"})
     */
    private $description;

    /**
     * @Vich\UploadableField(mapping="chapter_image", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @ORM\ManyToOne(targetEntity=ChapterType::class)
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"detail", "progress"})
     */
    private $type;

    /**
     * @ORM\OneToMany(targetEntity=Content::class, mappedBy="chapter", cascade={"persist"})
     *
     * @ORM\OrderBy({"position" = "ASC"})
     *
     * @Groups({"contents"})
     */
    private $contents;

    /**
     * @ORM\OneToMany(targetEntity=Question::class, mappedBy="chapter", orphanRemoval=true, cascade={"persist"})
     */
    private $questions;

    /**
     * @ORM\Column(type="integer")
     */
    private $position;

    /**
     * @var string
     *
     * @Groups({"detail"})
     */
    private $playerUrl;

    /**
     * @var bool
     *
     * @Groups({"detail"})
     */
    private $status;

    /**
     * @ORM\ManyToOne(targetEntity=Season::class, inversedBy="chapters")
     *
     * @ORM\JoinColumn(name="season_id", referencedColumnName="id", onDelete="SET NULL")
     *
     * @Assert\NotBlank
     */
    private $season;

    /**
     * @ORM\OneToOne(targetEntity=LtiChapter::class, mappedBy="chapter", cascade={"persist", "remove"})
     */
    private $lti;

    /**
     * @ORM\OneToOne(targetEntity=Pdf::class, mappedBy="chapter", cascade={"persist", "remove"})
     */
    private $pdf;

    /**
     * @ORM\OneToOne(targetEntity=Ppt::class, mappedBy="chapter", cascade={"persist", "remove"})
     */
    private $ppt;

    /**
     * @ORM\OneToOne(targetEntity=Video::class, mappedBy="chapter", cascade={"persist", "remove"})
     */
    private $video;

    /**
     * @ORM\OneToOne(targetEntity=Scorm::class, mappedBy="chapter", cascade={"persist", "remove"})
     *
     * @Groups({"progress"})
     */
    private $scorm;
    /**
     * @ORM\OneToOne(targetEntity=Puzzle::class, mappedBy="Chapter", cascade={"persist", "remove"})
     */
    private $puzzle;

    /**
     * @ORM\OneToMany(targetEntity=RouletteWord::class, mappedBy="chapter", cascade={"persist"})
     *
     * @Groups({"roulette"})
     */
    private $rouletteWords;

    /**
     * @ORM\OneToMany(targetEntity=Gamesword::class, mappedBy="chapter")
     *
     * @Groups({"roulette"})
     */
    private $gameswords;

    /**
     * @ORM\OneToMany(targetEntity=TrueOrFalse::class, mappedBy="chapter", cascade={"persist"} )
     *
     * @Groups({"roulette"})
     */
    private $trueOrFalses;

    /**
     * @ORM\OneToMany(targetEntity=AdivinaImagen::class, mappedBy="chapter", cascade={"persist"} )
     *
     * @Groups({"roulette"})
     */
    private $adivinaImagens;

    /**
     * @ORM\OneToMany(targetEntity=OrdenarMenormayor::class, mappedBy="chapter", cascade={"persist"} )
     *
     * @Groups({"roulette"})
     */
    private $ordenarMenormayors;

    /**
     * @ORM\OneToMany(targetEntity=Parejas::class, mappedBy="chapter", cascade={"persist"})
     *
     * @Groups({"roulette"})
     */
    private $parejas;

    /**
     * @ORM\OneToMany(targetEntity=Fillgaps::class, mappedBy="chapter", cascade={"persist"})
     *
     * @Groups({"roulette"})
     */
    private $fillgaps;

    /**
     * @ORM\OneToMany(targetEntity=Guessword::class, mappedBy="chapter")
     *
     * @Groups({"roulette"})
     */
    private $guesswords;

    /**
     * @ORM\OneToMany(targetEntity=Videoquiz::class, mappedBy="chapter", cascade={"persist"})
     *
     * @Groups({"roulette"})
     */
    private $videoquizzes;

    private $stateRedirect = false;

    /**
     * @ORM\OneToMany(targetEntity=CategorizeOptions::class, mappedBy="chapter", cascade={"persist"})
     */
    private $categorizeOptions;

    /**
     * @ORM\OneToOne(targetEntity=VcmsProject::class, cascade={"persist", "remove"})
     */
    private $vcmsProject;

    /**
     * @ORM\OneToMany(targetEntity=Categorize::class, mappedBy="chapter", cascade={"persist", "remove"})
     */
    private $categorizes;

    /**
     * @ORM\OneToMany(targetEntity=HigherLower::class, mappedBy="chapter", cascade={"persist"})
     */
    private $higherLowers;

    /**
     * @ORM\OneToMany(targetEntity=TimeGame::class, mappedBy="chapter")
     */
    private $timeGames;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementTemporalization::class, mappedBy="chapter")
     */
    private $temporalizations;

    /**
     * @ORM\OneToOne(targetEntity=RoleplayProject::class, cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=true)
     */
    private $roleplayProject;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $maxQuestion;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isActive;

    public function __construct()
    {
        $this->contents = new ArrayCollection();
        $this->questions = new ArrayCollection();
        $this->rouletteWords = new ArrayCollection();
        $this->trueOrFalses = new ArrayCollection();
        $this->adivinaImagens = new ArrayCollection();
        $this->ordenarMenormayors = new ArrayCollection();
        $this->parejas = new ArrayCollection();
        $this->fillgaps = new ArrayCollection();
        $this->guesswords = new ArrayCollection();
        $this->gameswords = new ArrayCollection();
        $this->videoquizzes = new ArrayCollection();
        $this->categorizeOptions = new ArrayCollection();
        $this->categorizes = new ArrayCollection();
        $this->higherLowers = new ArrayCollection();
        $this->timeGames = new ArrayCollection();
        $this->temporalizations = new ArrayCollection();
        $this->isActive = true;
    }

    public function __toString()
    {
        return $this->title;
    }

    public function __clone()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

        // clone pdf
        if ($this->getPdf()) {
            $pdf = clone $this->getPdf();
            $this->setPdf($pdf);
        }

        // clone ppt
        if ($this->getPpt()) {
            $ppt = clone $this->getPpt();
            $this->setPpt($ppt);
        }

        // clone video
        if ($this->getVideo()) {
            $video = clone $this->getVideo();
            $this->setVideo($video);
        }

        // clone contents
        $contents = $this->getContents();
        $this->contents = new ArrayCollection();
        if (\count($contents)) {
            foreach ($contents as $content) {
                $clonedContent = clone $content;
                $this->addContent($clonedContent);
            }
        }

        // clone questions
        $criteria = Criteria::create()->orderBy(['id' => Criteria::DESC]);
        $questions = $this->getQuestions()->matching($criteria);
        $this->questions = new ArrayCollection();
        if (\count($questions)) {
            foreach ($questions as $question) {
                $clonedQuestion = clone $question;
                $this->addQuestion($clonedQuestion);
            }
        }

        // clone vcms
        if ($this->getVcmsProject()) {
            $vcms = clone $this->getVcmsProject();
            $this->setVcmsProject($vcms);
        }

        // clone categorize
        $categorizes = $this->getCategorizes();
        $this->categorizes = new ArrayCollection();
        if (\count($categorizes)) {
            foreach ($categorizes as $categorize) {
                $clonedCategorize = clone $categorize;
                $this->addCategorize($clonedCategorize);
            }
        }

        // clone fillgaps
        $fillgaps = $this->getFillgaps();
        $this->fillgaps = new ArrayCollection();
        if (\count($fillgaps)) {
            foreach ($fillgaps as $fillgap) {
                $clonedFillgap = clone $fillgap;
                $this->addFillgap($clonedFillgap);
            }
        }

        // clone guessword
        $guesswords = $this->getGuesswords();
        $this->guesswords = new ArrayCollection();
        if (\count($guesswords)) {
            foreach ($guesswords as $guessword) {
                $clonedGuessword = clone $guessword;
                $this->addGuessword($clonedGuessword);
            }
        }

        // clone higherLower
        $higherLowers = $this->getHigherLowers();
        $this->higherLowers = new ArrayCollection();
        if (\count($higherLowers)) {
            foreach ($higherLowers as $higherLower) {
                $clonedHigherLower = clone $higherLower;
                $this->addHigherLower($clonedHigherLower);
            }
        }

        // clone OrdenarMenormayor
        $ordenarMenormayors = $this->getOrdenarMenormayors();
        $this->ordenarMenormayors = new ArrayCollection();
        if (\count($ordenarMenormayors)) {
            foreach ($ordenarMenormayors as $ordenarMenormayor) {
                $clonedOrdenarMenormayor = clone $ordenarMenormayor;
                $this->addOrdenarMenormayor($clonedOrdenarMenormayor);
            }
        }

        // clone adivinaImagens
        $adivinaImagens = $this->getAdivinaImagens();
        $this->adivinaImagens = new ArrayCollection();
        if (\count($adivinaImagens)) {
            foreach ($adivinaImagens as $adivinaImagen) {
                $clonedAdivinaImagen = clone $adivinaImagen;
                $this->addAdivinaImagen($clonedAdivinaImagen);
            }
        }

        // clone RouletteWords
        $rouletteWords = $this->getRouletteWords();
        $this->rouletteWords = new ArrayCollection();
        if (\count($rouletteWords)) {
            foreach ($rouletteWords as $rouletteWord) {
                $clonedRouletteWord = clone $rouletteWord;
                $this->addRouletteWord($clonedRouletteWord);
            }
        }

        // clone Parejas
        $parejas = $this->getParejas();
        $this->parejas = new ArrayCollection();
        if (\count($parejas)) {
            foreach ($parejas as $pareja) {
                $clonedPareja = clone $pareja;
                $this->addPareja($clonedPareja);
            }
        }

        // clone Puzzle
        if ($this->getPuzzle()) {
            $puzzle = clone $this->getPuzzle();
            $this->setPuzzle($puzzle);
        }

        // clone trueOrFalse
        $trueOrFalses = $this->getTrueOrFalses();
        $this->trueOrFalses = new ArrayCollection();
        if (\count($trueOrFalses)) {
            foreach ($trueOrFalses as $trueOrFalse) {
                $clonedTrueOrFalse = clone $trueOrFalse;
                $this->addTrueOrFalse($clonedTrueOrFalse);
            }
        }

        // clone videoquiz
        $videoquizzes = $this->getVideoquizzes();
        $this->videoquizzes = new ArrayCollection();
        if (\count($videoquizzes)) {
            foreach ($videoquizzes as $videoquiz) {
                $clonedVideoquiz = clone $videoquiz;
                $this->addVideoquiz($clonedVideoquiz);
            }
        }

        // clone scorm
        if ($this->getScorm()) {
            $scorm = clone $this->getScorm();
            $this->setScorm($scorm);
        }

        // clone RoleyPlay
        if ($this->getRoleplayProject()) {
            $rolePlayProject = clone $this->getRoleplayProject();
            $this->setRoleplayProject($rolePlayProject);
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getType(): ?ChapterType
    {
        return $this->type;
    }

    public function setType(?ChapterType $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection|Content[]
     */
    public function getContents(): Collection
    {
        return $this->contents;
    }

    public function addContent(Content $content): self
    {
        if (!$this->contents->contains($content)) {
            $this->contents[] = $content;
            $content->setChapter($this);
        }

        return $this;
    }

    public function removeContent(Content $content): self
    {
        if ($this->contents->contains($content)) {
            $this->contents->removeElement($content);
            // set the owning side to null (unless already changed)
            if ($content->getChapter() === $this) {
                $content->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Question[]
     */
    public function getQuestions(): Collection
    {
        return $this->questions;
    }

    public function addQuestion(Question $question): self
    {
        if (!$this->questions->contains($question)) {
            $this->questions[] = $question;
            $question->setChapter($this);
        }

        return $this;
    }

    public function removeQuestion(Question $question): self
    {
        if ($this->questions->contains($question)) {
            $this->questions->removeElement($question);
            // set the owning side to null (unless already changed)
            if ($question->getChapter() === $this) {
                $question->setChapter(null);
            }
        }

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function hasMinimumQuestion()
    {
        if (Games::WHEEL_TYPE == $this->type->getId()) {
            if (\count($this->questions) < Games::WHEEL_MINIMUM_QUESTIONS) {
                return false;
            }
        } elseif (Games::DOUBLE_OR_NOTHING_TYPE == $this->type->getId()) {
            if (\count($this->questions) < Games::DOUBLE_OR_NOTHING_MINIMUM_QUESTIONS) {
                return false;
            }
        } elseif (Games::QUIZ_TYPE == $this->type->getId()) {
            if (\count($this->questions) < Games::QUIZ_MINIMUM_QUESTIONS) {
                return false;
            }
        } elseif (Games::PUZZLE_TYPE == $this->type->getId()) {
            if (\count($this->questions) < Games::PUZZLE_MINIMUM_QUESTIONS) {
                return false;
            }
        } elseif (Games::SECRET_WORD_TYPE == $this->type->getId()) {
            if (\count($this->questions) < Games::SECRET_WORD_MINIMUM_QUESTIONS) {
                return false;
            }
        } elseif (Games::MATCH_TYPE == $this->type->getId()) {
            if (\count($this->questions) < Games::MATCH_MINIMUM_QUESTIONS) {
                return false;
            }
        }

        return true;
    }

    public function getMinimumQuestions()
    {
        if (Games::WHEEL_TYPE == $this->type->getId()) {
            return Games::WHEEL_MINIMUM_QUESTIONS;
        } elseif (Games::DOUBLE_OR_NOTHING_TYPE == $this->type->getId()) {
            return Games::DOUBLE_OR_NOTHING_MINIMUM_QUESTIONS;
        } elseif (Games::QUIZ_TYPE == $this->type->getId()) {
            return Games::QUIZ_MINIMUM_QUESTIONS;
        } elseif (Games::PUZZLE_TYPE == $this->type->getId()) {
            return Games::PUZZLE_MINIMUM_QUESTIONS;
        } elseif (Games::SECRET_WORD_TYPE == $this->type->getId()) {
            return Games::SECRET_WORD_MINIMUM_QUESTIONS;
        } elseif (Games::MATCH_TYPE == $this->type->getId()) {
            return Games::MATCH_MINIMUM_QUESTIONS;
        }

        return 0;
    }

    public function getPlayerUrl(): ?string
    {
        if (ChapterContent::VCMS_TYPE == $this->getType()->getId()) {
            return $this->getVirtualProjectView();
        }

        if (ChapterContent::ROLEPLAY_TYPE == $this->getType()->getId()) {
            return $this->getRoleplayProjectView();
        }

        if (ChapterTypeConstants::LTI_TYPE == $this->getType()->getId()) {
            $data = [
                '%chapterId' => \is_null($this->getId()) ? 0 : $this->getId(),
            ];

            return str_replace(array_keys($data), array_values($data), ChapterTypeConstants::LTI_PLAYER_URL);
        }

        return (null !== $this->getType()->getPlayerUrl()) ? $this->getType()->getPlayerUrl() . $this->getId() : null;
    }

    public function getPlayerUrlWithUserId(): ?string
    {
        if (ChapterContent::VCMS_TYPE == $this->getType()->getId()) {
            return $this->getVirtualProjectView();
        }

        if (ChapterContent::ROLEPLAY_TYPE == $this->getType()->getId()) {
            return $this->getRoleplayProjectView();
        }

        if (ChapterTypeConstants::LTI_TYPE == $this->getType()->getId()) {
            $data = [
                '%chapterId' => \is_null($this->getId()) ? 0 : $this->getId(),
                '%userId' => 0, // used to test lti on admin screen
            ];

            return str_replace(array_keys($data), array_values($data), ChapterTypeConstants::LTI_PLAYER_URL);
        }

        return (null !== $this->getType()->getPlayerUrl()) ? $this->getType()->getPlayerUrl() . $this->getId() : null;
    }

    public function getStatus(): ?bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): void
    {
        $this->status = $status;
    }

    public function getSeason(): ?Season
    {
        return $this->season;
    }

    public function setSeason(?Season $season): self
    {
        $this->season = $season;

        return $this;
    }

    public function getPdf(): ?Pdf
    {
        return $this->pdf;
    }

    public function setPdf(Pdf $pdf): self
    {
        $this->pdf = $pdf;

        // set the owning side of the relation if necessary
        if ($pdf->getChapter() !== $this) {
            $pdf->setChapter($this);
        }

        return $this;
    }

    public function getPpt(): ?Ppt
    {
        return $this->ppt;
    }

    public function setPpt(Ppt $ppt): self
    {
        $this->ppt = $ppt;

        // set the owning side of the relation if necessary
        if ($ppt->getChapter() !== $this) {
            $ppt->setChapter($this);
        }

        return $this;
    }

    public function getPuzzle(): ?Puzzle
    {
        return $this->puzzle;
    }

    public function setPuzzle(Puzzle $puzzle): self
    {
        $this->puzzle = $puzzle;

        // set the owning side of the relation if necessary
        if ($puzzle->getChapter() !== $this) {
            $puzzle->setChapter($this);
        }

        return $this;
    }

    public function getVideo(): ?Video
    {
        return $this->video;
    }

    public function setVideo(Video $video): self
    {
        $this->video = $video;

        // set the owning side of the relation if necessary
        if ($video->getChapter() !== $this) {
            $video->setChapter($this);
        }

        return $this;
    }

    public function getScorm(): ?Scorm
    {
        return $this->scorm;
    }

    public function setScorm(Scorm $scorm): self
    {
        $this->scorm = $scorm;

        // set the owning side of the relation if necessary
        if ($scorm->getChapter() !== $this) {
            $scorm->setChapter($this);
        }

        return $this;
    }

    /**
     * @Groups({"detail"})
     */
    public function getOrder()
    {
        return $this->position;
    }

    public function getGameTotalTime()
    {
        if ($this->getType()->isGame()) {
            switch ($this->getType()->getId()) {
                case Games::PUZZLE_TYPE:
                    return Games::PUZZLE_TIME_GAPS;
                default:
                    return $this->getType()->getQuestionTime() * \count($this->getQuestions());
            }
        } else {
            return null;
        }
    }

    public function hasContentCompleted()
    {
        $state = false;
        if ($this->getType()->getId()) {
            switch ($this->getType()->getId()) {
                case ChapterContent::VIDEO_TYPE:
                    $state = (bool) $this->getVideo();
                    break;

                case ChapterContent::PPT_TYPE:
                    $state = (bool) $this->getPpt();
                    break;

                case ChapterContent::PDF_TYPE:
                    $state = (bool) $this->getPdf();
                    break;

                case ChapterContent::LTI_TYPE:
                    $state = (bool) $this->getLti();
                    break;

                case Games::PUZZLE_TYPE:
                    foreach ($this->questions as $question) {
                        if ($question->getId()) {
                            $state = $question->getId() && $this->hasMinimumQuestion() && $this->getPuzzle() && $question->getValidateQuestion();
                        }
                    }
                    break;

                case ChapterContent::SCORM_TYPE:
                    $state = (bool) $this->getScorm();
                    break;

                case ChapterContent::CONTENT_TYPE:
                    foreach ($this->contents as $content) {
                        if ($content->getId()) {
                            $state = (bool) $content->getId();
                        }
                    }
                    break;

                case Games::SECRET_WORD_TYPE:
                case Games::WHEEL_TYPE:
                case Games::QUIZ_TYPE:
                case Games::DOUBLE_OR_NOTHING_TYPE:
                    foreach ($this->questions as $question) {
                        if ($question->getId()) {
                            $state = $question->getId() && $this->hasMinimumQuestion() && $question->getValidateQuestion();
                        }
                    }
                    break;
                case Games::TRUE_OR_FALSE_TYPE:
                    $state = \count($this->getTrueOrFalses()) > 0;
                    break;
                case Games::RIDDLE_TYPE:
                    $state = \count($this->getAdivinaImagens()) > 0;
                    break;
                case Games::HIGHER_OR_LOWER_TYPE:
                    $state = \count($this->getHigherLowers()) > 0;
                    break;
                case Games::MATCH_TYPE:
                    $state = \count($this->getParejas()) >= Games::MATCH_MINIMUM_QUESTIONS;
                    break;
                case Games::WHERE_DOES_IT_FIT_TYPE:
                    $state = \count($this->getCategorizes());
                    break;
                case Games::FILL_IN_THE_BLANKS_TYPE:
                    $state = \count($this->getFillGaps()) > 0;
                    break;
                case Games::SORT_LETTERS_TYPE:
                case Games::ENIGMA_TYPE:
                case Games::WORD_SEARCH_TYPE:
                    $state = \count($this->getOrdenarMenormayors()) > 0;
                    break;
                case Games::VIDEO_QUIZ_TYPE:
                    $state = \count($this->getVideoquizzes());
                    break;

                case ChapterContent::VCMS_TYPE:
                    $state = $this->vcmsProject && $this->vcmsProject->hasSlidesWithContent();
                    break;
                case Games::LETTERS_WHEEL_TYPE:
                    $state = \count($this->getRouletteWords()) > 0;
                    break;
                case ChapterContent::ROLEPLAY_TYPE:
                    $state = (bool) $this->getRoleplayProject()->isValid();
                    break;
            }

            return $state;
        }
    }

    public function getStateRedirect()
    {
        return $this->stateRedirect;
    }

    public function setStateRedirect($stateRedirect)
    {
        $this->stateRedirect = $stateRedirect;
    }

    /**
     * @return Collection|RouletteWord[]
     */
    public function getRouletteWords(): Collection
    {
        return $this->rouletteWords;
    }

    public function addRouletteWord(RouletteWord $rouletteWord): self
    {
        if (!$this->rouletteWords->contains($rouletteWord)) {
            $this->rouletteWords[] = $rouletteWord;
            $rouletteWord->setChapter($this);
        }

        return $this;
    }

    public function removeRouletteWord(RouletteWord $rouletteWord): self
    {
        if ($this->rouletteWords->removeElement($rouletteWord)) {
            // set the owning side to null (unless already changed)
            if ($rouletteWord->getChapter() === $this) {
                $rouletteWord->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|TrueOrFalse[]
     */
    public function getTrueOrFalses(): Collection
    {
        return $this->trueOrFalses;
    }

    public function addTrueOrFalse(TrueOrFalse $trueOrFalse): self
    {
        if (!$this->trueOrFalses->contains($trueOrFalse)) {
            $this->trueOrFalses[] = $trueOrFalse;
            $trueOrFalse->setChapter($this);
        }

        return $this;
    }

    public function removeTrueOrFalse(TrueOrFalse $trueOrFalse): self
    {
        if ($this->trueOrFalses->removeElement($trueOrFalse)) {
            // set the owning side to null (unless already changed)
            if ($trueOrFalse->getChapter() === $this) {
                $trueOrFalse->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|AdivinaImagen[]
     */
    public function getAdivinaImagens(): Collection
    {
        return $this->adivinaImagens;
    }

    public function addAdivinaImagen(AdivinaImagen $adivinaImagen): self
    {
        if (!$this->adivinaImagens->contains($adivinaImagen)) {
            $this->adivinaImagens[] = $adivinaImagen;
            $adivinaImagen->setChapter($this);
        }

        return $this;
    }

    public function removeAdivinaImagen(AdivinaImagen $adivinaImagen): self
    {
        if ($this->adivinaImagens->removeElement($adivinaImagen)) {
            // set the owning side to null (unless already changed)
            if ($adivinaImagen->getChapter() === $this) {
                $adivinaImagen->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, OrdenarMenormayor>
     */
    public function getOrdenarMenormayors(): Collection
    {
        return $this->ordenarMenormayors;
    }

    public function addOrdenarMenormayor(OrdenarMenormayor $ordenarMenormayor): self
    {
        if (!$this->ordenarMenormayors->contains($ordenarMenormayor)) {
            $this->ordenarMenormayors[] = $ordenarMenormayor;
            $ordenarMenormayor->setChapter($this);
        }

        return $this;
    }

    public function removeOrdenarMenormayor(OrdenarMenormayor $ordenarMenormayor): self
    {
        if ($this->ordenarMenormayors->removeElement($ordenarMenormayor)) {
            // set the owning side to null (unless already changed)
            if ($ordenarMenormayor->getChapter() === $this) {
                $ordenarMenormayor->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Parejas>
     */
    public function getParejas(): Collection
    {
        return $this->parejas;
    }

    public function addPareja(Parejas $pareja): self
    {
        if (!$this->parejas->contains($pareja)) {
            $this->parejas[] = $pareja;
            $pareja->setChapter($this);
        }

        return $this;
    }

    public function removePareja(Parejas $pareja): self
    {
        if ($this->parejas->removeElement($pareja)) {
            // set the owning side to null (unless already changed)
            if ($pareja->getChapter() === $this) {
                $pareja->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Fillgaps>
     */
    public function getFillgaps(): Collection
    {
        return $this->fillgaps;
    }

    public function addFillgap(Fillgaps $fillgap): self
    {
        if (!$this->fillgaps->contains($fillgap)) {
            $this->fillgaps[] = $fillgap;
            $fillgap->setChapter($this);
        }

        return $this;
    }

    public function removeFillgap(Fillgaps $fillgap): self
    {
        if ($this->fillgaps->removeElement($fillgap)) {
            // set the owning side to null (unless already changed)
            if ($fillgap->getChapter() === $this) {
                $fillgap->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Guessword[]
     */
    public function getGuesswords(): Collection
    {
        return $this->guesswords;
    }

    public function addGuessword(Guessword $guessword): self
    {
        if (!$this->guesswords->contains($guessword)) {
            $this->guesswords[] = $guessword;
            $guessword->setChapter($this);
        }

        return $this;
    }

    public function removeGuessword(Guessword $guessword): self
    {
        if ($this->guesswords->removeElement($guessword)) {
            // set the owning side to null (unless already changed)
            if ($guessword->getChapter() === $this) {
                $guessword->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Gamesword>
     */
    public function getGameswords(): Collection
    {
        return $this->gameswords;
    }

    public function addGamesword(Gamesword $gamesword): self
    {
        if (!$this->gameswords->contains($gamesword)) {
            $this->gameswords[] = $gamesword;
            $gamesword->setChapter($this);
        }

        return $this;
    }

    public function removeGamesword(Gamesword $gamesword): self
    {
        if ($this->gameswords->removeElement($gamesword)) {
            // set the owning side to null (unless already changed)
            if ($gamesword->getChapter() === $this) {
                $gamesword->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Videoquiz>
     */
    public function getVideoquizzes(): Collection
    {
        return $this->videoquizzes;
    }

    public function addVideoquiz(Videoquiz $videoquiz): self
    {
        if (!$this->videoquizzes->contains($videoquiz)) {
            $this->videoquizzes[] = $videoquiz;
            $videoquiz->setChapter($this);
        }

        return $this;
    }

    public function removeVideoquiz(Videoquiz $videoquiz): self
    {
        if ($this->videoquizzes->removeElement($videoquiz)) {
            // set the owning side to null (unless already changed)
            if ($videoquiz->getChapter() === $this) {
                $videoquiz->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, CategorizeOptions>
     */
    public function getCategorizeOptions(): Collection
    {
        return $this->categorizeOptions;
    }

    public function addCategorizeOption(CategorizeOptions $categorizeOption): self
    {
        if (!$this->categorizeOptions->contains($categorizeOption)) {
            $this->categorizeOptions[] = $categorizeOption;
            $categorizeOption->setChapter($this);
        }

        return $this;
    }

    public function removeCategorizeOption(CategorizeOptions $categorizeOption): self
    {
        if ($this->categorizeOptions->removeElement($categorizeOption)) {
            // set the owning side to null (unless already changed)
            if ($categorizeOption->getChapter() === $this) {
                $categorizeOption->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Categorize>
     */
    public function getCategorizes(): Collection
    {
        return $this->categorizes;
    }

    public function addCategorize(Categorize $categorize): self
    {
        if (!$this->categorizes->contains($categorize)) {
            $this->categorizes[] = $categorize;
            $categorize->setChapter($this);
        }

        return $this;
    }

    public function removeCategorize(Categorize $categorize): self
    {
        if ($this->categorizes->removeElement($categorize)) {
            // set the owning side to null (unless already changed)
            if ($categorize->getChapter() === $this) {
                $categorize->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, HigherLower>
     */
    public function getHigherLowers(): Collection
    {
        return $this->higherLowers;
    }

    public function addHigherLower(HigherLower $higherLower): self
    {
        if (!$this->higherLowers->contains($higherLower)) {
            $this->higherLowers[] = $higherLower;
            $higherLower->setChapter($this);
        }

        return $this;
    }

    public function removeHigherLower(HigherLower $higherLower): self
    {
        if ($this->higherLowers->removeElement($higherLower)) {
            // set the owning side to null (unless already changed)
            if ($higherLower->getChapter() === $this) {
                $higherLower->setChapter(null);
            }
        }

        return $this;
    }

    public function getVcmsProject(): ?VcmsProject
    {
        return $this->vcmsProject;
    }

    public function setVcmsProject(?VcmsProject $vcmsProject): self
    {
        $this->vcmsProject = $vcmsProject;

        return $this;
    }

    public function getVirtualProjectView(): string
    {
        return \sprintf(ChapterContent::VCMS_URL_VIEW, $this->getVcmsProject() ? $this->getVcmsProject()->getId() : null);
    }

    public function getVirtualProjectEdit(): string
    {
        return \sprintf(ChapterContent::VCMS_URL_EDIT, $this->getVcmsProject() ? $this->getVcmsProject()->getId() : null);
    }

    public function getRoleplayProjectView(): string
    {
        return \sprintf(ChapterContent::ROLEPLAY_URL_VIEW, $this->getRoleplayProject() ? $this->getRoleplayProject()->getId() : null);
    }

    public function getRoleplayProjectEdit(): string
    {
        return \sprintf(ChapterContent::ROLEPLAY_URL_EDIT, $this->getRoleplayProject() ? $this->getRoleplayProject()->getId() : null);
    }

    /**
     * @return Collection<int, TimeGame>
     */
    public function getTimeGames(): Collection
    {
        return $this->timeGames;
    }

    public function addTimeGame(TimeGame $timeGame): self
    {
        if (!$this->timeGames->contains($timeGame)) {
            $this->timeGames[] = $timeGame;
            $timeGame->setChapter($this);
        }

        return $this;
    }

    public function removeTimeGame(TimeGame $timeGame): self
    {
        if ($this->timeGames->removeElement($timeGame)) {
            // set the owning side to null (unless already changed)
            if ($timeGame->getChapter() === $this) {
                $timeGame->setChapter(null);
            }
        }

        return $this;
    }

    public function getRoleplayProject(): ?RoleplayProject
    {
        return $this->roleplayProject;
    }

    public function setRoleplayProject(RoleplayProject $roleplayProject): self
    {
        $this->roleplayProject = $roleplayProject;

        return $this;
    }

    /**
     * @return Collection<int, Temporalization>
     */
    public function getTemporalizations(): Collection
    {
        return $this->temporalizations;
    }

    public function addTemporalization(AnnouncementTemporalization $temporalization): self
    {
        if (!$this->temporalizations->contains($temporalization)) {
            $this->temporalizations[] = $temporalization;
            $temporalization->setChapter($this);
        }

        return $this;
    }

    public function removeTemporalization(AnnouncementTemporalization $temporalization): self
    {
        if ($this->temporalizations->removeElement($temporalization)) {
            // set the owning side to null (unless already changed)
            if ($temporalization->getChapter() === $this) {
                $temporalization->setChapter(null);
            }
        }

        return $this;
    }

    /**
     * Get the value of maxQuestion.
     */
    public function getMaxQuestion()
    {
        return $this->maxQuestion;
    }

    /**
     * Set the value of maxQuestion.
     *
     * @return self
     */
    public function setMaxQuestion($maxQuestion)
    {
        $this->maxQuestion = $maxQuestion;

        return $this;
    }

    public function isIsActive(): ?bool
    {
        if (!$this->getType()->isActive()) {
            return false;
        }

        return $this->isActive;
    }

    public function setIsActive(?bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    /**
     * Get the value of lti.
     */
    public function getLti()
    {
        return $this->lti;
    }

    /**
     * Set the value of lti.
     *
     * @return self
     */
    public function setLti($lti)
    {
        $this->lti = $lti;

        return $this;
    }
}
