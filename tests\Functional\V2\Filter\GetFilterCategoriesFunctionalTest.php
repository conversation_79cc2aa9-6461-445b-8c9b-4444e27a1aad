<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Filter;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\FilterEndpoints;
use App\Tests\Functional\V2\Fixtures\FilterCategoryFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetFilterCategoriesFunctionalTest extends FunctionalTestCase
{
    use FilterCategoryFixtureTrait;

    private const string EMAIL_TUTOR = '<EMAIL>';
    private const string EMAIL_MANAGER = '<EMAIL>';
    private ?User $tutor;
    private ?User $manager;

    private array $userIds = [];

    protected function setUp(): void
    {
        parent::setUp();
        $this->tutor = $this->createAndGetUser(roles: [User::ROLE_TUTOR], email: self::EMAIL_TUTOR);
        $this->manager = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: self::EMAIL_MANAGER);
        $this->userIds[] = $this->tutor->getId();
        $this->userIds[] = $this->manager->getId();
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(
            email: self::EMAIL_TUTOR,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testManagerCanAccess(): void
    {
        $token = $this->loginAndGetToken(
            email: self::EMAIL_MANAGER,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(0, $data);
    }

    public function testGetFilterCategories(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(0, $data);

        $category1 = $this->setAndGetFilterCategoryInRepository(
            id: new Id(1),
            name: 'Category 1',
            sort: 2
        );

        $category2 = $this->setAndGetFilterCategoryInRepository(
            id: new Id(2),
            name: 'Category 2',
            sort: 1
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(2, $data);
        $this->assertEquals([
            [
                'id' => 2,
                'name' => 'Category 2',
            ],
            [
                'id' => 1,
                'name' => 'Category 1',
            ],
        ], $data);

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(name: 'Category 1'),
            bearerToken: $token,
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(1, $data);
        $this->assertEquals([
            [
                'id' => 1,
                'name' => 'Category 1',
            ],
        ], $data);

        $category3 = $this->setAndGetFilterCategoryInRepository(
            id: new Id(3),
            parentId: new Id(2),
            name: 'Category 3',
            sort: 2,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(parentId: 2),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(1, $data);
        $this->assertEquals([
            [
                'id' => 3,
                'name' => 'Category 3',
            ],
        ], $data);
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: FilterEndpoints::getFilterCategoriesEndpoint(name: ''),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[name]' => 'This value should not be blank.',
            ],
        ], $content['metadata']);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
