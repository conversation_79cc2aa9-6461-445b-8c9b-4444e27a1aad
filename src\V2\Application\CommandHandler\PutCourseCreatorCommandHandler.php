<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\Course;
use App\Entity\User;
use App\V2\Application\Command\PutCourseCreator;
use App\V2\Domain\Course\CourseCriteria;
use App\V2\Domain\Course\CourseRepository;
use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\Exceptions\CreatorNotAuthorizedException;
use App\V2\Domain\Course\Creator\Exceptions\CreatorNotFoundException;
use App\V2\Domain\Course\Creator\Exceptions\PutCourseCreatorException;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class PutCourseCreatorCommandHandler
{
    public function __construct(
        private CourseCreatorRepository $courseCreatorRepository,
        private UserRepository $userRepository,
        private CourseRepository $courseRepository,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotFoundException
     * @throws CourseNotFoundException
     * @throws CreatorNotFoundException
     * @throws CreatorNotAuthorizedException
     * @throws CriteriaException
     * @throws PutCourseCreatorException
     */
    public function handle(PutCourseCreator $command): void
    {
        $user = $this->userRepository->findOneBy(
            UserCriteria::createById($command->getUserId())
        );

        $course = $this->courseRepository->findOneBy(
            CourseCriteria::createById($command->getCourseId())
        );

        if (!\in_array(User::ROLE_CREATOR, $user->getRoles(), true)) {
            throw new CreatorNotFoundException('User is not a creator');
        }

        $this->checkPermissions($command->getRequestUser(), $course);

        $this->checkCreatorNotAlreadyExists($user, $course);

        $courseCreator = new CourseCreator(
            userId: $command->getUserId(),
            courseId: $command->getCourseId(),
        );

        $this->courseCreatorRepository->insert($courseCreator);
    }

    /**
     * @throws CreatorNotAuthorizedException
     */
    private function checkPermissions(User $requestUser, Course $course): void
    {
        if (
            !\in_array(User::ROLE_ADMIN, $requestUser->getRoles())
            && \in_array(User::ROLE_CREATOR, $requestUser->getRoles(), true)
            && ($course->getCreatedBy() && $course->getCreatedBy()->getId() !== $requestUser->getId())
        ) {
            throw CreatorNotAuthorizedException::userNotAuthorized($requestUser, $course);
        }
    }

    /**
     * @throws PutCourseCreatorException
     * @throws InfrastructureException
     */
    private function checkCreatorNotAlreadyExists(User $user, Course $course): void
    {
        if ($user->getId() === $course->getCreatedBy()?->getId()) {
            throw PutCourseCreatorException::creatorAlreadyExists();
        }

        try {
            $this->courseCreatorRepository->findOneBy(
                CourseCreatorCriteria::createEmpty()
                    ->filterByUserId(new Id($user->getId()))
                    ->filterByCourseId(new Id($course->getId()))
            );

            throw PutCourseCreatorException::creatorAlreadyExists();
        } catch (CourseCreatorNotFoundException) {
        }
    }
}
