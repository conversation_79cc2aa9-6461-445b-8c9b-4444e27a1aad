<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Course\Manager;

use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Shared\Id\Id;

class CourseManagerMother
{
    public static function create(
        ?Id $userId = null,
        ?Id $courseId = null,
    ): CourseManager {
        return new CourseManager(
            userId: $userId ?? new Id(1),
            courseId: $courseId ?? new Id(1),
        );
    }
}
