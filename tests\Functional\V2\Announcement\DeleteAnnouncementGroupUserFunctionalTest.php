<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementGroupEndpoints;
use App\Tests\Functional\SettingsConstants;
use App\V2\Domain\Shared\Filter\FilterCollection;
use Symfony\Component\HttpFoundation\Response;

class DeleteAnnouncementGroupUserFunctionalTest extends FunctionalTestCase
{
    private array $userIds = [];
    private Announcement $announcement1;
    private Announcement $announcement2;
    private AnnouncementGroup $announcementGroup1;
    private AnnouncementGroup $announcementGroup2;
    private AnnouncementGroup $announcementGroup22;

    private User $userTest1;
    private User $userTest2;

    protected function setUp(): void
    {
        parent::setUp();

        $filter1 = $this->createAndGetFilter(
            name: 'Filter test 1'
        );

        $manager1 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $manager2 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
            manageFilters: new FilterCollection([
                $filter1,
            ])
        );

        $this->userTest1 = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: '<EMAIL>',
        );

        $this->userTest2 = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: '<EMAIL>',
            userFilters: new FilterCollection([
                $filter1,
            ]),
            createdBy: $manager2,
        );

        $this->userIds[] = $manager1->getId();
        $this->userIds[] = $manager2->getId();
        $this->userIds[] = $this->userTest1->getId();
        $this->userIds[] = $this->userTest2->getId();

        $course = $this->createAndGetCourse();
        $course2 = $this->createAndGetCourse();
        $this->announcement1 = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $manager1,
        );
        $this->announcement2 = $this->createAndGetAnnouncement(
            course: $course2,
            createdBy: $manager2,
        );

        $this->announcementGroup1 = $this->createAndGetAnnouncementGroup(
            announcement: $this->announcement1,
        );

        $this->announcementGroup2 = $this->createAndGetAnnouncementGroup(
            announcement: $this->announcement2,
        );

        $this->announcementGroup22 = $this->createAndGetAnnouncementGroup(
            announcement: $this->announcement2,
        );
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: 1,
                groupId: 1,
                userId: 1,
            )
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testNotFoundResults(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: 999,
                groupId: $this->announcementGroup1->getId(),
                userId: $this->userTest1->getId(),
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement not found', $content['message']);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement1->getId(),
                groupId: 999,
                userId: $this->userTest1->getId(),
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement group not found', $content['message']);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement1->getId(),
                groupId: $this->announcementGroup1->getId(),
                userId: 999,
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User not found', $content['message']);
    }

    public function testDeleteAnnouncementUserAsAdmin(): void
    {
        $announcementUser = $this->createAndGetAnnouncementUser(
            announcement: $this->announcement1,
            user: $this->userTest1,
            announcementGroup: $this->announcementGroup1,
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement1->getId(),
                groupId: $this->announcementGroup1->getId(),
                userId: $this->userTest1->getId(),
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    public function testDeleteAnnouncementUserAsManager(): void
    {
        $this->createAndGetAnnouncementUser(
            announcement: $this->announcement2,
            user: $this->userTest1,
            announcementGroup: $this->announcementGroup2,
        );

        $announcementUser2 = $this->createAndGetAnnouncementUser(
            announcement: $this->announcement2,
            user: $this->userTest2,
            announcementGroup: $this->announcementGroup2,
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement2->getId(),
                groupId: $this->announcementGroup2->getId(),
                userId: $this->userTest1->getId(),
            ),
            bearerToken: $token,
        );

        // User is not managed
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement2->getId(),
                groupId: $this->announcementGroup2->getId(),
                userId: $this->userTest2->getId(),
            ),
            bearerToken: $token,
        );

        // User is managed
        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    public function testDeleteWithAnnouncementSignature(): void
    {
        $announcementUser2 = $this->createAndGetAnnouncementUser(
            announcement: $this->announcement2,
            user: $this->userTest2,
            announcementGroup: $this->announcementGroup2,
        );

        $this->createAndGetAnnouncementUserDigitalSignature(
            announcementUser: $announcementUser2,
        );

        $this->createAndGetAnnouncementClassroomVirtualUser(
            announcementUser: $announcementUser2,
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement2->getId(),
                groupId: $this->announcementGroup2->getId(),
                userId: $this->userTest2->getId(),
            ),
            bearerToken: $token,
        );

        // User is managed
        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    public function testDeleteUserAsManagerWithSharingEnabled(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'true',
        );

        $this->createAndGetAnnouncementUser(
            announcement: $this->announcement1,
            user: $this->userTest2,
            announcementGroup: $this->announcementGroup1,
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Try to delete user from announcement when is not shared.
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement1->getId(),
                groupId: $this->announcementGroup1->getId(),
                userId: $this->userTest2->getId(),
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testUserNotInAnnouncement(): void
    {
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Try to delete user from announcement when is not shared.
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement2->getId(),
                groupId: $this->announcementGroup2->getId(),
                userId: $this->userTest2->getId(),
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement user not found', $content['message']);
    }

    public function testUserNotInGroup(): void
    {
        $announcementUser2 = $this->createAndGetAnnouncementUser(
            announcement: $this->announcement2,
            user: $this->userTest2,
            announcementGroup: $this->announcementGroup22,
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
                announcementId: $this->announcement2->getId(),
                groupId: $this->announcementGroup2->getId(),
                userId: $this->userTest2->getId(),
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User not in group', $content['message']);
    }

    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );
        $this->truncateEntities([
            AnnouncementUser::class,
            AnnouncementGroup::class,
            Announcement::class,
            Course::class,
            Filter::class,
            FilterCategory::class,
        ]);
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
