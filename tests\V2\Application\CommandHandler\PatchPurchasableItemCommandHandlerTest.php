<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Purchase\Input\PatchPurchasableItemInputMother;
use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\PatchPurchasableItemCommand;
use App\V2\Application\CommandHandler\PatchPurchasableItemCommandHandler;
use App\V2\Application\DTO\Purchase\PatchPurchasableItemInputDTO;
use App\V2\Application\Purchase\PurchasableItemAssembler;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PatchPurchasableItemCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?PurchasableItemRepository $purchasableItemRepository = null,
    ): PatchPurchasableItemCommandHandler {
        return new PatchPurchasableItemCommandHandler(
            $purchasableItemRepository ?? $this->createMock(PurchasableItemRepository::class),
            new PurchasableItemAssembler(),
        );
    }

    /**
     * @throws InvalidUuidException
     */
    private function getCommand(
        ?Identifier $purchasableItemId = null,
        ?PatchPurchasableItemInputDTO $input = null,
    ): PatchPurchasableItemCommand {
        return new PatchPurchasableItemCommand(
            purchasableItemId: $purchasableItemId ?? UuidMother::create(),
            input: $input ?? PatchPurchasableItemInputMother::create(),
        );
    }

    /**
     * Test basic functionality - handler is instantiable.
     *
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(PatchPurchasableItemCommandHandler::class, $handler);
    }

    /**
     * @throws Exception
     */
    public function testHandleMethodExists(): void
    {
        $this->assertTrue(method_exists(PatchPurchasableItemCommandHandler::class, 'handle'));

        $handler = $this->getHandler();
        $this->assertTrue(\is_callable([$handler, 'handle']));
    }

    /**
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws CriteriaException
     */
    #[DataProvider('successfulUpdateProvider')]
    public function testHandleSuccessfulUpdate(
        PurchasableItem $existingItem,
        PatchPurchasableItemInputDTO $input,
    ): void {
        $command = $this->getCommand(
            purchasableItemId: $existingItem->getId(),
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(
                $this->callback(function (PurchasableItemCriteria $criteria) use ($existingItem) {
                    return null !== $criteria->getId() && $criteria->getId()->equals($existingItem->getId());
                })
            )
            ->willReturn($existingItem);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->with(
                $this->callback(function (PurchasableItem $updatedItem) use ($existingItem, $input) {
                    // Verify the updated item has the correct properties
                    $expectedPrice = $input->getPrice() ?? $existingItem->getPrice();
                    $expectedIsActive = $input->getIsActive() ?? $existingItem->getIsActive();

                    return $updatedItem->getId()->equals($existingItem->getId())
                        && $updatedItem->getPrice()->equals($expectedPrice)
                        && $updatedItem->getIsActive() === $expectedIsActive;
                })
            );

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleWithNonExistentItem(): void
    {
        $command = $this->getCommand();

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new PurchasableItemNotFoundException());

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $this->expectException(PurchasableItemNotFoundException::class);
        $handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleWithRepositoryExceptionOnFind(): void
    {
        $command = $this->getCommand();

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new PurchasableItemRepositoryException('Database error'));

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Database error');
        $handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleWithRepositoryExceptionOnSave(): void
    {
        $existingItem = PurchasableItemMother::create();
        $input = PatchPurchasableItemInputMother::create(price: MoneyMother::create());

        $command = $this->getCommand(
            purchasableItemId: $existingItem->getId(),
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        $purchasableItemRepository
            ->method('findOneBy')
            ->willReturn($existingItem);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->willThrowException(new PurchasableItemRepositoryException('Save failed'));

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Save failed');
        $handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleWithInvalidPurchasableItemException(): void
    {
        $existingItem = PurchasableItemMother::create();
        $negativePrice = MoneyMother::create(amount: -100);
        $input = PatchPurchasableItemInputMother::create(price: $negativePrice);

        $command = $this->getCommand(
            purchasableItemId: $existingItem->getId(),
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        $purchasableItemRepository
            ->method('findOneBy')
            ->willReturn($existingItem);

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $this->expectException(InvalidPurchasableItemException::class);
        $handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleWithCriteriaException(): void
    {
        $command = $this->getCommand();

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new CriteriaException('Invalid criteria'));

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Invalid criteria');
        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws CriteriaException
     */
    public function testHandleWithPriceInCents(): void
    {
        $existingItem = PurchasableItemMother::create();
        $priceInCents = MoneyMother::create(amount: 1500); // 15.00 EUR in cents
        $input = PatchPurchasableItemInputMother::create(price: $priceInCents);

        $command = $this->getCommand(
            purchasableItemId: $existingItem->getId(),
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        $purchasableItemRepository
            ->method('findOneBy')
            ->willReturn($existingItem);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->with(
                $this->callback(function (PurchasableItem $updatedItem) use ($priceInCents) {
                    return $updatedItem->getPrice()->equals($priceInCents);
                })
            );

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $handler->handle($command);

        // Verify the price is handled correctly in cents
        $this->assertEquals(1500, $priceInCents->value());
    }

    /**
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws CriteriaException
     */
    public function testRepositoryOperationsWithCorrectCriteria(): void
    {
        $itemId = UuidMother::create();
        $existingItem = PurchasableItemMother::create(id: $itemId);
        $input = PatchPurchasableItemInputMother::create(isActive: true);

        $command = $this->getCommand(
            purchasableItemId: $itemId,
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        // Verify that findOneBy is called with criteria created by ID
        $purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(
                $this->callback(function (PurchasableItemCriteria $criteria) use ($itemId) {
                    // Verify the criteria is created with the correct ID
                    return null !== $criteria->getId() && $criteria->getId()->equals($itemId);
                })
            )
            ->willReturn($existingItem);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->with(
                $this->callback(function (PurchasableItem $updatedItem) use ($itemId) {
                    return $updatedItem->getId()->equals($itemId) && true === $updatedItem->getIsActive();
                })
            );

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws CriteriaException
     */
    public function testIntegrationContextWithAllDependencies(): void
    {
        $existingItem = PurchasableItemMother::create();
        $newPrice = MoneyMother::create(amount: 2000);
        $input = PatchPurchasableItemInputMother::create(
            price: $newPrice,
            isActive: true
        );

        $command = $this->getCommand(
            purchasableItemId: $existingItem->getId(),
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        // Verify the complete flow: find -> assemble -> save
        $purchasableItemRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($existingItem);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->with(
                $this->callback(function (PurchasableItem $updatedItem) use ($existingItem, $newPrice) {
                    return $updatedItem->getId()->equals($existingItem->getId())
                        && $updatedItem->getPrice()->equals($newPrice)
                        && true === $updatedItem->getIsActive();
                })
            );

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws CriteriaException
     */
    #[DataProvider('patchSemanticsProvider')]
    public function testPatchSemanticsWithPartialUpdates(
        PatchPurchasableItemInputDTO $input,
    ): void {
        $existingItem = PurchasableItemMother::create();

        $command = $this->getCommand(
            purchasableItemId: $existingItem->getId(),
            input: $input,
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);

        $purchasableItemRepository
            ->method('findOneBy')
            ->willReturn($existingItem);

        $purchasableItemRepository
            ->expects($this->once())
            ->method('put')
            ->with(
                $this->callback(function (PurchasableItem $updatedItem) use ($existingItem, $input) {
                    $expectedPrice = $input->getPrice() ?? $existingItem->getPrice();
                    $expectedIsActive = $input->getIsActive() ?? $existingItem->getIsActive();

                    return $updatedItem->getId()->equals($existingItem->getId())
                        && $updatedItem->getPrice()->equals($expectedPrice)
                        && $updatedItem->getIsActive() === $expectedIsActive;
                })
            );

        $handler = $this->getHandler(purchasableItemRepository: $purchasableItemRepository);

        $handler->handle($command);
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public static function successfulUpdateProvider(): \Generator
    {
        $baseItem = PurchasableItemMother::create();

        // Update only price
        $newPrice = MoneyMother::create(amount: 2500);
        $priceInput = PatchPurchasableItemInputMother::create(price: $newPrice);

        yield 'update price only' => [
            'existingItem' => $baseItem,
            'input' => $priceInput,
        ];

        // Update only isActive
        $activeInput = PatchPurchasableItemInputMother::create(isActive: true);

        yield 'update isActive only' => [
            'existingItem' => $baseItem,
            'input' => $activeInput,
        ];

        // Update both fields
        $bothPrice = MoneyMother::create(amount: 3000);
        $bothInput = PatchPurchasableItemInputMother::create(price: $bothPrice, isActive: false);

        yield 'update both price and isActive' => [
            'existingItem' => $baseItem,
            'input' => $bothInput,
        ];

        // Update with null values (no changes)
        $emptyInput = PatchPurchasableItemInputMother::create();

        yield 'update with null values' => [
            'existingItem' => $baseItem,
            'input' => $emptyInput,
        ];
    }

    public static function patchSemanticsProvider(): \Generator
    {
        yield 'price only update' => [
            'input' => PatchPurchasableItemInputMother::create(
                price: MoneyMother::create(amount: 1200)
            ),
        ];

        yield 'isActive only update' => [
            'input' => PatchPurchasableItemInputMother::create(isActive: false),
        ];

        yield 'both fields update' => [
            'input' => PatchPurchasableItemInputMother::create(
                price: MoneyMother::create(amount: 1800),
                isActive: true
            ),
        ];

        yield 'no fields update' => [
            'input' => PatchPurchasableItemInputMother::create(),
        ];
    }
}
