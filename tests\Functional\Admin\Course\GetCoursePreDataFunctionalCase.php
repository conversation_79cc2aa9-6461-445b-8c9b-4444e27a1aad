<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\NpsQuestion;
use App\Entity\Survey;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\HelperTrait\SurveyHelperTrait;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetCoursePreDataFunctionalCase extends FunctionalTestCase
{
    use SurveyHelperTrait;

    private ?array $testSurveysIds = null;

    public function changeDefaultSurveyValue(): void
    {
        $defaultSurvey = $this->getEntityManager()->getRepository(Survey::class)->findOneBy(['isMain' => 1]);
        if ($defaultSurvey) {
            $defaultSurvey->setActive(false);
            $this->getEntityManager()->persist($defaultSurvey);
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getCoursePreDataResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCourseEndpoints::preDataEndpoint(),
            bearerToken: $userToken
        );
    }

    #[DataProvider('surveyDataProvider')]
    public function testCourseSurveyPreSelection(array $surveys, array $expectedMain, int $assertCount): void
    {
        $this->changeDefaultSurveyValue();
        foreach ($surveys as $survey) {
            $testSurvey = $this->createAndGetSurvey(
                name: $survey['name'],
                active: $survey['active'],
                description: $survey['description'],
                applyTo: $survey['applyTo'],
                isMain: $survey['isMain'],
                createdBy: $this->getDefaultUser(),
            );
            $this->testSurveysIds[] = $testSurvey->getId();
        }
        $response = $this->getCoursePreDataResponse();
        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);
        $mainSurveys = array_filter($responseData['surveysCourse'], fn ($s) => true === $s['isMain']);
        $this->assertCount($assertCount, $mainSurveys ?? []);
        if (!empty($expectedMain)) {
            $this->assertArrayHasKey('name', $mainSurveys[0] ?? []);
            $this->assertArrayHasKey('isMain', $mainSurveys[0] ?? []);
            $this->assertEquals($expectedMain['name'], $mainSurveys[0]['name']);
            $this->assertTrue($mainSurveys[0]['isMain']);
        } else {
            $this->assertSame($expectedMain, $mainSurveys ?? []);
        }
        if (!empty($this->testSurveysIds)) {
            $this->clearTestSurveysAndRestoreDefault();
        }
    }

    public function clearTestSurveysAndRestoreDefault(): void
    {
        if (!empty($this->testSurveysIds)) {
            foreach ($this->testSurveysIds as $surveyId) {
                $survey = $this->getEntityManager()->getRepository(Survey::class)->find($surveyId);
                if ($survey) {
                    $npsQuestionsSurvey = $this->getEntityManager()->getRepository(NpsQuestion::class)->findBy(['survey' => $surveyId]);
                    foreach ($npsQuestionsSurvey as $question) {
                        $this->getEntityManager()->remove($question);
                    }
                    $this->getEntityManager()->remove($survey);
                }
            }
        }
        $defaultSurvey = $this->getEntityManager()->getRepository(Survey::class)->findOneBy(['isMain' => true]);
        if ($defaultSurvey) {
            $defaultSurvey->setActive(true);
            $this->getEntityManager()->persist($defaultSurvey);
        }
        $this->getEntityManager()->flush();
        $this->testSurveysIds = [];
    }

    public static function surveyDataProvider(): \Generator
    {
        yield 'surveys with one main survey' => [
            'surveys' => [
                [
                    'name' => 'First Survey',
                    'description' => 'This is the First survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => true,
                ],
                [
                    'name' => 'Second Survey',
                    'description' => 'This is the Second survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => false,
                ],
                [
                    'name' => 'Third Survey',
                    'description' => 'This is the Third survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => false,
                ],
            ],
            'expectedMain' => [
                'name' => 'First Survey',
                'isMain' => true,
            ],
            'assertCount' => 1,
        ];
        yield 'surveys with two main survey' => [
            'surveys' => [
                [
                    'name' => 'First Survey',
                    'description' => 'This is the First survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => true,
                ],
                [
                    'name' => 'Second Survey',
                    'description' => 'This is the Second survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => true,
                ],
                [
                    'name' => 'Third Survey',
                    'description' => 'This is the Third survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => false,
                ],
            ],
            'expectedMain' => [
                'name' => 'First Survey',
                'isMain' => true,
            ],
            'assertCount' => 2,
        ];
        yield 'surveys with no main survey' => [
            'surveys' => [
                [
                    'name' => 'First Survey',
                    'description' => 'This is the First survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => false,
                ],
                [
                    'name' => 'Second Survey',
                    'description' => 'This is the Second survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => false,
                ],
                [
                    'name' => 'Third Survey',
                    'description' => 'This is the Third survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => false,
                ],
            ],
            'expectedMain' => [],
            'assertCount' => 0,
        ];
        yield 'surveys with null main survey' => [
            'surveys' => [
                [
                    'name' => 'First Survey',
                    'description' => 'This is the First survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => null,
                ],
                [
                    'name' => 'Second Survey',
                    'description' => 'This is the Second survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => null,
                ],
                [
                    'name' => 'Third Survey',
                    'description' => 'This is the Third survey.',
                    'active' => true,
                    'applyTo' => 1,
                    'isMain' => null,
                ],
            ],
            'expectedMain' => [],
            'assertCount' => 0,
        ];
    }
}
