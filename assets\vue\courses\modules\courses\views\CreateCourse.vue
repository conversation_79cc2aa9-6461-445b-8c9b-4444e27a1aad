<template>
  <div
    class="d-flex flex-column align-items-center justify-content-center"
    v-if="isGlobalLoading"
  >
    <Loader :is-loaded="isGlobalLoading" />
    <span>{{ $t("LOADING") }}</span>
  </div>
  <div class="CreateCourse" v-else>
    <StepForm
      id="new-course-form"
      :show-progress="numberOfSteps - disabledSteps.length > 1"
      :number-of-steps="numberOfSteps"
      :current-step="currentStepNumber"
      :current-step-icon="currentStepIcon"
      :disabled-steps="disabledSteps"
      :current-step-description-tooltip="currentStepDescriptionTooltip"
      :steps-custom-titles="customTitles"
      :showBarProgressPercentage="false"
      @next="next()"
      @prev="prev()"
      @on-step-change="onCurrentStepChange"
      @submit="submit()"
    >
      <template v-slot:form-content>
        <div
          class="form-step basic-info"
          v-show="currentStep === STEPS.BASIC_INFO"
        >
          <div class="basic-info--data">
            <FileSelector
              name="thumbnail"
              :title-tooltip="$t('COURSE.THUMBNAIL')"
              :defaultImage="imageUrl"
              preview-default-class="limited-size centered"
              :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.THUMBNAIL)"
            />
            <div
              class="form__basic-info__data d-flex flex-row flex-wrap align-items-start"
            >
              <div class="row w-100">
                <div class="required col-12">
                  <label for="name">{{ $t("NAME") }}</label>
                  <input
                    id="name"
                    name="name"
                    type="text"
                    class="form-control"
                    required
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.NAME)"
                    v-model="name"
                  />
                </div>
              </div>
              
              <div class="row w-100">
                <div class="required col-xs-12 col-md-6 mt-3">
                  <div class="w-100">
                    <div class="d-flex align-items-center mb-2">
                      <label for="code" class="mb-0">{{ $t("CODE") }}</label>
                      <i
                        class="fas fa-question-circle text-primary ml-2 fs-4"
                        data-toggle="tooltip"
                        :title="$t('COURSE.CODE_TOOLTIP')"
                        data-placement="right"
                      />
                    </div>
                    <input
                      id="code"
                      name="code"
                      type="text"
                      class="form-control"
                      required
                      :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.CODE)"
                      v-model="code"
                    />
                  </div>
                  <div class="w-100 mt-3">
                    <div class="required d-flex align-items-center mb-2">
                      <label for="typeCourse" class="mb-0">{{
                          $t("COURSE.TYPE_COURSE")
                        }}</label>
                      <i
                        class="fas fa-question-circle text-primary ml-2 fs-4"
                        data-toggle="tooltip"
                        :title="$t('COURSE.TYPE_COURSE_TOOLTIP')"
                        data-placement="right"
                      />
                    </div>
                    <select
                      class="custom-select"
                      id="typeCourse"
                      name="typeCourse"
                      v-model="typeCourse_id"
                      v-bind="{ disabled: $route.name === 'UpdateCourse' || !$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.TYPE) }"
                    >
                      <option disabled :value="null">{{ $t("SELECT") }}</option>
                      <option
                        v-for="typeCourse in typesCourse"
                        :key="'type_' + typeCourse.id"
                        :value="typeCourse.id"
                      >
                        {{ typeCourse.name }}
                      </option>
                    </select>
                  </div>
                  <div class="w-100 mt-3">
                    <div class="col-xs-12 required">
                      <label for="category">{{ $t("CATEGORY") }}</label>
                      <select
                          class="custom-select"
                          id="category"
                          name="category"
                          v-model="category_id"
                          :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.CATEGORY)"
                          required
                      >
                        <option disabled :value="null">{{ $t("SELECT") }}</option>
                        <option
                            v-for="category in categoriesByTypeCourse"
                            :key="'category_' + category.id"
                            :value="category.id"
                        >
                          {{ category.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="col-xs-12 col-md-6 mt-3">
                  <BaseTimePicker
                    :current-time="duration"
                    :show-presets="true"
                    @change="setDuration"
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.DURATION)"
                  />
                </div>
              </div>


              <div class="row w-100">
                <div class="col-xs-12 col-md-6" v-if="setCourseLevel">
                  <label for="category">{{ $t("COURSE.LEVEL") }}</label>
                  <select
                    class="custom-select"
                    id="level"
                    name="level"
                    v-model="level_id"
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.LEVEL)"
                  >
                    <option value="">{{ $t("SELECT") }}</option>
                    <option
                      v-for="level in levels"
                      :key="'level_' + level.id"
                      :value="level.id"
                    >
                      {{ level.name }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="row w-100">
                <div class="col-12" v-if="multilingual">
                  <LocaleSelector
                    :locale="locale"
                    :tooltip-description="$t('COURSE.LOCALE_TOOLTIP')"
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.LOCALE)"
                    @set-locale="locale = $event"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="basic-info--segments" v-if="useSegment && $auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.SEGMENTS)">
            <AddRemove
              :source-items="segments"
              :realtime="false"
              :title="$t('COURSE.SEGMENT') + ''"
              :enable-all="true"
              v-model="selectedSegments"
              :loading-source="false"
              :loading-selected="false"
            />
          </div>
          <div class="basic-info--description mb-4">
            <div class="w-100">
              <div class="d-flex align-items-center mb-2">
                <label class="mb-0">{{ $t("DESCRIPTION") }}</label>
                <i
                  class="fas fa-question-circle text-primary ml-2 fs-4"
                  data-toggle="tooltip"
                  :title="$t('COURSE.DESCRIPTION_TOOLTIP')"
                  data-placement="right"
                />
              </div>
              <p class="textarea form-control bg-gray" v-if="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.DESCRIPTION)">{{ description }}</p>
              <Froala
                v-else
                :tag="`textarea`"
                v-model="description"
                :config="froalaDescriptionConfig"
              />
            </div>
          </div>
          <div v-if="courseDocumentation" class="w-100 mb-4">
            <div class="d-flex align-items-center mb-2">
              <label class="mb-0">{{ $t("COURSE.FORM.LAST_STEP") }}</label>
              <i
                class="fas fa-question-circle text-primary ml-2 fs-4"
                data-toggle="tooltip"
                :title="$t('COURSE.GENERAL_INFORMATION_TOOLTIP')"
                data-placement="right"
              />
            </div>
            <p class="textarea form-control bg-gray" v-if="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.GENERAL_INFO)">{{ generalInformation }}</p>
            <Froala
              v-else
              :tag="`textarea`"
              v-model="generalInformation"
              :config="froalaGeneralDocumentationConfig"
            />
          </div>

          <div class="w-100">
            <h3 class="section-title">
              <i class="fa fa-tag" /> {{ $t("COURSE.TAGS_AND_CLASSIFICATION") }}
              <i
                class="fas fa-question-circle text-primary ml-2 fs-4"
                data-toggle="tooltip"
                :title="$t('COURSE.TAGS_AND_CLASSIFICATION_TOOLTIP')"
                data-placement="right"
              />
            </h3>

            <div class="basic-info--tags">
              <Tags
                @tags-updated="tags = $event"
                :prop-tags="tags"
                auto-complete-url="/admin/courses/available-tags"
                :tooltip-description="$t('COURSE.TAGS_TOOLTIP')"
                :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.TAGS)"
              />
            </div>
          </div>
        </div>
        <div
          class="form-step general-info"
          v-show="currentStep === STEPS.AUDIENCE"
        >
          <div class="row px-3 pb-3" v-if="typeCourse_id === 1 && (surveysCourse.length > 1 || setCoursePoints)">
            <div
              class="col-lg-3 col-md-4 col-sm-6 col-xs-12 p-3"
              v-if="surveysCourse.length > 1"
            >
              <label for="surverCourse">{{
                $t("ANNOUNCEMENT.FORM.STEPS.SURVEY")
                }}</label>
              <select
                class="custom-select"
                id="survey"
                name="survey"
                v-model="survey_id"
              >
                <option disabled :value="null">{{ $t("SELECT") }}</option>
                <option
                  v-for="surveyCourse in surveysCourse"
                  :key="'survey_course_' + surveyCourse.id"
                  :value="surveyCourse.id"
                >
                  {{ surveyCourse.name }}
                </option>
              </select>
            </div>
            
            <div
              class="col-lg-3 col-md-4 col-sm-6 col-xs-12 py-3"
              v-if="setCoursePoints"
            >
              <label for="points">{{ $t("COURSE.SCORE") }}</label>
              <input
                id="points"
                class="form-control"
                name="points"
                type="number"
                min="100"
                v-model="points"
                @input="validatePoints"
              />
            </div>
          </div>
          <div class="px-3">
            <h3 class="section-title">
              <i class="fa fa-eye"></i> {{ $t("COURSE.AUDIENCE.TITLE") }}
              <i
                class="fas fa-question-circle text-primary ml-2 fs-4"
                data-toggle="tooltip"
                :title="$t('COURSE.AUDIENCE_TOOLTIP')"
                data-placement="right"
              />
            </h3>
            <div class="enableCourseStatus">
              <ButtonWithDescription
                title="COURSE.BTN_ACTIVE.TITLE"
                description="COURSE.BTN_ACTIVE.DESCRIPTION"
                name="active"
                v-model="active"
                :disabled="
                !$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.PUBLISH)
              "
                :disabled-message="$t('NOT_ALLOWED')"
              />
              <ButtonWithDescription
                name="isNew"
                title="COURSE.BTN_IS_NEW.TITLE"
                description="COURSE.BTN_IS_NEW.DESCRIPTION"
                v-model="isNew"
                :disabled="
                !$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.AUDIENCE)
              "
                :disabled-message="$t('NOT_ALLOWED')"
              />
              <ButtonWithDescription
                v-if="isOriginalCourse"
                title="COURSE.BTN_OPEN.TITLE"
                description="COURSE.BTN_OPEN.DESCRIPTION"
                v-model="open"
                name="open"
                :disabled="
                !$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.AUDIENCE)
              "
                :disabled-message="$t('NOT_ALLOWED')"
              />
              <ButtonWithDescription
                v-if="isOriginalCourse"
                name="open_visible"
                title="COURSE.BTN_OPEN_CAMPUS.TITLE"
                description="COURSE.BTN_OPEN_CAMPUS.DESCRIPTION"
                v-model="open_visible"
                :disabled="
                !$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.AUDIENCE)
              "
                :disabled-message="$t('NOT_ALLOWED')"
              />
            </div>
          </div>
          <div class="col-12 mt-3 audience" v-if="open && filtersEnabled && isOriginalCourse">
            <div class="col-12 audience-header">
              <h1>{{ $t("COURSE.AUDIENCE.TITLE") }}</h1>
              <p v-html="$t('COURSE.AUDIENCE.DESCRIPTION')" />
            </div>
            <CategoryFilter
              v-model="filters"
              url-categories="/api/v2/admin/filters/categories"
              :show-category-warning-status="true"
              :category-warning-status-text="
                $t('COURSE.AUDIENCE_WARNING_TEXT') + ''
              "
              :show-titles="true"
              :allow-all="true"
            />
          </div>

          <div
            class="col-12 mt-3 audience"
            v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MANAGER_LIST) && isOriginalCourse"
          >
            <h3 class="section-title">
              <i class="fa fa-users-gear"></i> {{ $t("COURSE.MANAGERS") }}
              <i
                class="fas fa-question-circle text-primary ml-2 fs-4"
                data-toggle="tooltip"
                :title="$t('COURSE.MANAGERS_TOOLTIP')"
                data-placement="right"
              />
            </h3>
            <AddRemove
              :source-items="availableManagers"
              :realtime="false"
              :title="$t('COURSE.MANAGERS') + ''"
              :enable-all="false"
              v-model="selectedManagers"
              :loading-source="false"
              :loading-selected="false"
              :fields="{ id: 'id', name: 'fullName' }"
            />
          </div>
        </div>
        <div class="form-step" v-show="currentStep === STEPS.CERTIFICATES">
          <div class="col-xs-12 col-md-6" v-if="typeDiplomas.length > 1">
            <label for="typeDiploma">{{
              $t("ANNOUNCEMENT.FORM.STEPS.CERTIFICATE")
            }}</label>
            <select
              class="custom-select"
              id="TypeDiploma"
              name="TypeDiploma"
              v-model="typeDiploma_id"
            >
              <option disabled :value="null">
                {{ $t("ANNOUNCEMENT.FORM.ENTITY.CERTIFICATE_SELECT") }}
              </option>
              <option
                v-for="typeDiploma in typeDiplomas"
                :key="'type_diploma_' + typeDiploma.id"
                :value="typeDiploma.id"
              >
                {{ typeDiploma.name }}
              </option>
            </select>
          </div>
          <div class="diploma-data">
            <h5 class="text-left">
              <strong>{{ $t("ANNOUNCEMENT.DIPLOMAS_STUDENT") }}</strong>
            </h5>
            <!--  -->
            <div class="row">
              <div class="col-1">
                <div class="box-icon">
                  <i class="fas fa-hourglass-end"></i>
                </div>
              </div>
              <div class="col-5">
                <div class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    id="showDurationInDiploma"
                    v-model="diplomaShowDuration"
                    :disabled="!duration"
                  />
                  <label class="custom-control-label" for="showDurationInDiploma">
                    <strong>
                      Incluir tiempo de formación
                    </strong>
                  </label>
                </div>
                <div>
                  <p v-if="duration">Activando esta opción se añadirá el campo horas de formación en el diploma correspondiente<span v-show="diplomaShowDuration">: <b><i class="icon fa fa-clock"></i> {{ parseDurationToHumanTime(duration) }}</b></span></p>
                  <p v-else>
                    <i class="icon fa fa-warning"></i>
                    <a @click="()=>{ currentStep = STEPS.BASIC_INFO; currentStepNumber = 1;}" class="actionLink"> 
                      Selecciona una duración para el curso si quieres que se incluya en el diploma
                    </a> 
                  </p>
                </div>
              </div>
              <div class="col-6" v-show="diplomaShowDuration">
                <!--  -->
              </div>
            </div>
            <!--  -->
            <div class="row">
              <div class="col-1">
                <div class="box-icon">
                  <i class="fas fa-file"></i>
                </div>
              </div>
              <div class="col-5">
                <div class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    id="isContentDiploma"
                    v-model="isContentDiploma"
                  />
                  <label class="custom-control-label" for="isContentDiploma"
                    ><strong>{{
                      $t("INCLUDE_COURSE_DIPLOMA_INDEX")
                    }}</strong></label
                  >
                </div>
                <div>
                  <p>{{ $t("INCLUDE_COURSE_DIPLOMA_INDEX_DESCRIPTION") }}</p>
                </div>
              </div>
              <div class="col-6" v-show="isContentDiploma">
                <label for="category"
                  ><strong>{{ $t("COURSE_DIPLOMA_INDEX") }}</strong></label
                >
                <select
                  class="custom-select"
                  id="diploma_select"
                  name="diploma_select"
                  v-model="diploma_index"
                >
                  <option :value="typeIndexDiploma[0]">
                    {{ $t("REPORTS_DIPLOMA_SELECT_DEFAULT") }}
                  </option>
                  <option :value="typeIndexDiploma[1]">
                    {{ $t("REPORTS_DIPLOMA_SELECT_MANUAL") }}
                  </option>
                </select>
              </div>
            </div>
            <div class="p-3 mt-3">
              <h6>
                <strong>{{ $t("COURSE_DIPLOMA_INDEX_DEFAULT_TITLE") }}</strong>
              </h6>
              <p>{{ $t("COURSE_DIPLOMA_INDEX_DEFAULT_DESCRIPTION") }}</p>
            </div>
            <div
              class="basic-info--description"
              v-show="diploma_index === 'MANUAL'"
            >
              <div class="form-group w-100">
                <label>{{ $t("DESCRIPTION_MANUAL_INDEX") }}</label>
                <froala
                  :tag="`textarea`"
                  v-model="descriptionContentDiploma"
                  :config="froalaDefaultConfig"
                ></froala>
              </div>
            </div>
          </div>
        </div>
      </template>
    </StepForm>
  </div>
</template>

<script>
import $ from "jquery";
import { get } from "vuex-pathify";

import ButtonWithDescription from "../../../../common/components/ButtonWithDescription.vue";
import CategoryFilter from "../../../../common/components/filter/CategoryFilter.vue";
import FileSelector from "../../../../common/components/FileSelector.vue";
import FormProgress from "../../../../common/components/FormProgress.vue";
import Loader from "../../../../admin/components/Loader.vue";
import LocaleSelector from "../../../../common/components/LocaleSelector.vue";
import Tags from "../../../../common/components/Tags.vue";
import StepForm from "../../../../common/views/StepForm.vue";

import {
  getFiltersForRequest,
  generateCompatibleData,
} from "../../../../common/components/filter/CategoryFilter.vue";
import AddRemove from "../../../../common/components/select/AddRemove.vue";
import ROUTE_NAMES from "../router/routeNames";
import {
  COURSE_ACTIONS_BY_ROLE,
  COURSE_PERMISSIONS,
} from "../../../../common/utils/auth/permissions/course.permissions";
import BaseTimePicker from '../../../../admin/components/base/BaseTimePicker.vue'

const EVENT_NAMES = {
  CREATE_OR_UPDATE_AND_ADD_COURSE: "CREATE_OR_UPDATE_AND_ADD_COURSE",
  CREATE_OR_UPDATE: "CREATE_OR_UPDATE",
};

const STEPS = {
  BASIC_INFO: "BASIC_INFO",
  AUDIENCE: "AUDIENCE",
  CERTIFICATES: "CERTIFICATES",
};

export default {
  name: "CreateCourse",
  components: {
    BaseTimePicker,
    AddRemove,
    StepForm,
    ButtonWithDescription,
    CategoryFilter,
    FileSelector,
    FormProgress,
    Loader,
    LocaleSelector,
    Tags,
  },
  $,
  data() {
    return {
      testFilters: null,
      name: "",
      code: "",
      category_id: null,
      duration: null,
      diplomaShowDuration: false,
      selectedSegments: [],
      tags: [],
      description: "",
      generalInformation: "",
      documentation: "",
      active: false,
      open: false,
      open_visible: false,
      isOriginalCourse: true,
      isNew: false,
      filters: [],
      locale: "en",
      points: 0,
      level_id: "",
      selectedManagers: [],
      typesCourse: [],
      typeCourse_id: null,
      surveysCourse: [],
      survey_id: null,
      typeDiplomas: [],
      isCustomizedDiploma: false,
      typeDiploma_id: null,
      disabledSteps: [],
      STEPS,
      currentStep: STEPS.BASIC_INFO,
      currentStepNumber: 1,
      currentStepIcon: "fa fa-book",
      currentStepDescriptionTooltip: this.$t(
        "COURSE.INFORMATION_GENERAL_TOOLTIP"
      ),
      categories: [],
      segments: [],
      levels: [],
      availableManagers: [],
      
      courseDocumentation: false,
      multilingual: false,
      setCourseLevel: false,
      setCoursePoints: false,
      useSegment: false,
      loadingCourse: false,
      imageUrl: "/assets/common/add_image_file.svg",
      isContentDiploma: false,
      typeIndexDiploma: [],
      diploma_index: "DEFAULT",
      descriptionContentDiploma: "",
      
      managerCanPublish: false,
      ROUTE_NAMES,
      actions: [
        {
          name: this.$t("COURSE.CREATE_AND_ADD_COURSE"),
          event: EVENT_NAMES.CREATE_OR_UPDATE_AND_ADD_COURSE,
          class: "btn btn-secondary",
          bind: { disabled: true },
        },
        {
          name: this.$t("SUBMIT"),
          event: EVENT_NAMES.CREATE_OR_UPDATE,
          class: "btn btn-primary",
          bind: { disabled: true },
        },
      ],
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS;
    },
    useGlobalEventBus: get("contentTitleModule/getUseGlobalEventBus"),
    loading: get("createCourseModule/loading"),
    filtersEnabled: get("configModule/config@filtersEnabled"),
    defaultLocale() {
      return this.$store.getters["localeModule/getDefaultLocale"];
    },
    
    froalaDefaultConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 150,
        pastePlain: true,
        pluginsEnabled: ["align", "charCounter"],
        toolbarButtons: {
          moreText: {
            buttons: ["bold", "italic", "underline"],
          },
        },
        charCounterCount: true,
      };
    },
    
    froalaDescriptionConfig() {
      return {
        ...this.froalaDefaultConfig,
        charCounterMax: 500,
      };
    },
    froalaGeneralDocumentationConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 250,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "image",
          "lists",
          "file",
          "paragraphStyle",
          "paragraphFormat",
          "filesManager",
          "imageTUI",
          "video",
        ],
        toolbarButtons: {
          moreText: {
            buttons: ["bold", "italic", "underline"],
          },
          moreParagraph: {
            buttons: [
              "alignLeft",
              "alignCenter",
              "formatOLSimple",
              "alignRight",
              "alignJustify",
              "formatOL",
              "formatUL",
              "paragraphFormat",
              "paragraphStyle",
              "lineHeight",
              "outdent",
              "indent",
              "quote",
            ],
          },
          moreRich: {
            buttons: ["insertLink", "insertImage", "insertFile", "insertVideo"],
            buttonsVisible: 4,
          },
        },
      };
    },
    customTitles() {
      const map = new Map();
      map.set(1, "COURSE.FORM.STEP-1");
      map.set(2, "FORM-PRESENCIAL.STEP-1");
      map.set(3, "DIPLOMA");
      return map;
    },
    numberOfSteps() {
      if (!this.isUpdate) return 1;
      if (this.typeCourse_id === 1 && this.isCustomizedDiploma ) return 3;
      if (this.typeCourse_id === 1 || this.typeCourse_id === 3) return 2;
      return 1;
    },
    routeName() {
      return this.$route.name;
    },
    canPublish() {
      const isAdmin = this.$store.getters["userModule/isAdmin"];
      const isManager = this.$store.getters["userModule/isManager"];
      const isManagerEditor = this.$store.getters["userModule/isManagerEditor"];
      
      if (isAdmin || isManagerEditor) return true;
      else return !!(isManager && this.managerCanPublish);
    },
    categoriesByTypeCourse() {
      if(!this.typeCourse_id || this.typeCourse_id === null){
        return this.categories;
      }
      return this.categories.filter((item) =>
        item.idTypesCourse.includes(this.typeCourse_id)
      );
    },
    isGlobalLoading() {
      return this.loading || this.loadingCourse;
    },
    isUpdate() {
      return this.routeName === this.ROUTE_NAMES.UPDATE_COURSE;
    },
  },
  watch: {
    $route(to, from) {
      this.handleRouteParams();
    },
    duration: {
      handler: function(val){
        if(!val) this.diplomaShowDuration = false;
      }
    },
    typeCourse_id: {
      handler: function (val, oldVal) {
        if (val !== oldVal && this.$route?.name === ROUTE_NAMES.CREATE_COURSE) {
          this.category_id = null;
          if(this.surveysCourse.length && val === 1){
            const defaultSurvey = this.surveysCourse.find(survey => survey.isMain);
            this.survey_id = defaultSurvey ? defaultSurvey.id : null;
          }
          else if(val !== 1){
            this.survey_id = null;
          }
          this.typeDiploma_id =
            this.typeDiplomas.length === 1 && val === 1
              ? this.typeDiplomas[0].id
              : null;
        }
      },
    },
    isGlobalLoading(value) {
      if (!value) this.enableActions();
    },
  },
  created() {
    this.addActions();
    this.addANameActions();
    this.handleRouteParams();
    const managerPublishDomElement = document.getElementById("manager-publish");
    if (managerPublishDomElement) {
      this.managerCanPublish =
        managerPublishDomElement.dataset.managerPublish === "true";
    }
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.isUpdate
          ? this.$t("COURSE.UPDATE_COURSE")
          : this.$t("COURSE.CREATE_COURSE"),
      },
    });
  },
  mounted() {
    this.$auth.setPermissionList(COURSE_ACTIONS_BY_ROLE);
    if (this.useGlobalEventBus) {
      this.$eventBus.$on(
        EVENT_NAMES.CREATE_OR_UPDATE_AND_ADD_COURSE,
        async () => {
          await this.submit();
          this.$store.dispatch(
            "contentTitleModule/removeRouteFromContentTitle",
            this.routeName
          );
          this.$router.push({ name: ROUTE_NAMES.CREATE_COURSE });
        }
      );
      this.$eventBus.$on(EVENT_NAMES.CREATE_OR_UPDATE, async () => {
        await this.submit();
      });
    }
    this.handleSteps();
  },
  beforeDestroy() {
    this.$auth.setPermissionList({});
    this.$eventBus.$off(EVENT_NAMES.CREATE_OR_UPDATE_AND_ADD_COURSE);
    this.$eventBus.$off(EVENT_NAMES.CREATE_OR_UPDATE);
  },
  updated() {
    $('[data-toggle="tooltip"]').tooltip();
  },
  methods: {
    validatePoints() {
      if (this.points.includes("-")) {
        this.points = "";
      }
      const value = parseInt(this.points, 10);
      if (isNaN(value) || value < 1) {
        this.points = "";
        this.points = value;
      }
    },
    validateFirstStep() {
      if (!this.code || this.code.length < 1) {
        this.$toast.error(this.$t("ERROR_CODE_FIELD_REQUIRED") + "");
        return false;
      }
      if (!this.name || this.name.length < 1) {
        this.$toast.error(this.$t("ERROR_NAME_FIELD_REQUIRED") + "");
        return false;
      }
      if (!this.typeCourse_id || this.typeCourse_id.length < 1) {
        this.$toast.error(this.$t("ERROR_TYPE_COURSE_FIELD_REQUIRED") + "");
        return false;
      }
      if (!this.category_id || this.name.length < 1) {
        this.$toast.error(this.$t("ERROR_CATEGORY_FIELD_REQUIRED") + "");
        return false;
      }
      return true;
    },
    onCurrentStepChange(step, validate = true) {
      if (step > 1 && validate && !this.validateFirstStep()) return;
      if (step === 1) {
        this.currentStep = this.STEPS.BASIC_INFO;
        this.currentStepIcon = "fa fa-book";
        this.currentStepDescriptionTooltip = this.$t(
          "COURSE.INFORMATION_GENERAL_TOOLTIP"
        );
      } else if (step === 2) {
        this.currentStepIcon = "fa fa-cog";
        this.currentStepDescriptionTooltip = this.$t("COURSE.INFORMATION_ADVANCED_TOOLTIP");
        this.currentStep = this.STEPS.AUDIENCE;
      } else if (step === 3) {
        this.currentStepDescriptionTooltip = this.$t(
          "COURSE.CERTIFICATE_TOOLTIP"
        );
        this.currentStepIcon = "fa fa-certificate";
        this.currentStep = this.STEPS.CERTIFICATES;
      }
      this.currentStepNumber = step;
    },
    next() {
      switch (this.currentStepNumber) {
        case 1:
          if (!this.validateFirstStep()) return;
          this.currentStep = this.STEPS.AUDIENCE;
          break;
        case 2:
          this.currentStep = this.STEPS.CERTIFICATES;
          break;
      }
      this.currentStepNumber++;
    },
    prev() {
      switch (this.currentStepNumber) {
        case 2:
          this.currentStep = this.STEPS.BASIC_INFO;
          break;
        case 3:
          this.currentStep = this.STEPS.AUDIENCE;
          break;
      }
      this.currentStepNumber--;
    },
    addActions() {
      const actions = this.$auth.hasPermission(COURSE_PERMISSIONS.CREATE)
        ? [...this.actions]
        : [this.actions[1]];
      
      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions: actions,
      });
    },
    addANameActions() {
      this.actions[0].name = this.isUpdate
        ? this.$t("COURSE.UPDATE_AND_ADD_COURSE")
        : this.$t("COURSE.CREATE_AND_ADD_COURSE");
    },
    enableActions() {
      const createAndAddCourseAction = this.actions.find(
        (action) => action.event === EVENT_NAMES.CREATE_OR_UPDATE_AND_ADD_COURSE
      );
      const createAction = this.actions.find(
        (action) => action.event === EVENT_NAMES.CREATE_OR_UPDATE
      );
      if (createAndAddCourseAction && createAction) {
        createAndAddCourseAction.bind.disabled = false;
        createAction.bind.disabled = false;
        this.addActions();
      }
    },
    async handleRouteParams() {
      try {
        if (this.isUpdate) {
          this.loadingCourse = true;
          
          await this.loadRequiredData();
          
          const courseResp = await this.$store.dispatch(
            "createCourseModule/loadCourse",
            this.$route.params.id
          );
          const { data, error, survey, isOriginalCourse, isCustomizedDiploma } = courseResp;
          
          if (error) {
            throw new Error(error);
          }
          
          this.active = data.active;
          if (data.category) {
            this.category_id = data.category.id;
          }
          if (data.typeCourse) {
            this.typeCourse_id = data.typeCourse.id;
          }
          if (data.typeDiploma) {
            this.typeDiploma_id = data.typeDiploma.id;
          }
          if (survey) {
            this.survey_id = survey.id;
          }
          
          
          this.code = data.code;
          this.duration = this.parseDurationToFloat(data.duration || null);
          this.diplomaShowDuration = data.diploma_config.show_duration || false;
          this.description = data.description;
          this.documentation = data.documentation ?? "";
          this.generalInformation = data.generalInformation ?? "";
          this.locale = data.locale;
          this.name = data.name;
          this.open = data.open;
          this.open_visible = data.open_visible;
          this.isOriginalCourse = isOriginalCourse;
          this.isCustomizedDiploma = isCustomizedDiploma;
          this.isNew = data.isNew;
          this.points = data.points;
          this.isContentDiploma = data.is_content_diploma;
          this.diploma_index = data.type_index_diploma || "DEFAULT";
          this.descriptionContentDiploma =
            data.description_content_diploma || "";
          if (this.typeDiplomas.length === 1) {
            this.typeDiploma_id = this.typeDiplomas[0].id;
          }
          if (data.courseSegments) {
            this.selectedSegments = data.courseSegments;
          }
          if (data.tags && data.tags.length > 0) {
            data.tags.forEach((tag) => {
              this.tags.push(tag.name);
            });
          }
          if (data.image) {
            this.imageUrl = `/uploads/images/course/${data.image}`;
          }
          
          this.loadingCourse = false;
          
          const filtersResp = await this.$store.dispatch(
            "createCourseModule/getCourseFilters",
            data.id
          );
          const { data: filtersData, error: filtersError } = filtersResp;
          
          if (filtersError) {
            throw new Error(filtersError);
          }
          this.filters = generateCompatibleData(filtersData);
          
          const managersResp = await this.$store.dispatch(
            "createCourseModule/getManagers",
            data.id
          );
          const { data: managersData, error: managersError } = managersResp;
          
          if (managersError) {
            throw new Error(managersError);
          }
          this.selectedManagers = managersData;
        } else {
          await this.loadRequiredData();
        }
        
        await this.loadManagers();
      } catch (err) {
        console.error("Error al manejar parámetros de la ruta:", err);
        this.$toast?.error(
          err.message || "Error al manejar parámetros de la ruta."
        );
      } finally {
        this.loadingCourse = false;
      }
    },
    async loadRequiredData() {
      this.locale = this.defaultLocale;
      
      try {
        const response = await this.$store.dispatch(
          "createCourseModule/loadPreData"
        );
        const { data, error } = response;
        
        if (error) {
          throw new Error(error);
        }
        
        this.categories = data.categories;
        
        this.courseDocumentation = data.courseDocumentation;
        this.multilingual = data.multilingual;
        this.setCourseLevel = data.setCourseLevel;
        this.setCoursePoints = data.setCoursePoints;
        this.useSegment = data.useSegment;
        this.typesCourse = data.typesCourse;
        this.surveysCourse = data.surveysCourse;
        this.typeDiplomas = data.typeDiplomas;
        this.isCustomizedDiploma = data.isCustomizedDiploma;
        this.points = data.points;
        this.typeIndexDiploma = data.typeIndexDiploma;
        
        if (this.typeDiplomas.length === 1) {
          this.typeDiploma_id = this.typeDiplomas[0].id;
        }
        if (this.surveysCourse.length === 1) {
          this.survey_id = this.surveysCourse[0].id;
        }
        if (this.useSegment) {
          this.segments = data.segments;
        }
        if (this.setCourseLevel) {
          this.levels = data.levels;
        }
      } catch (err) {
        console.error("Ocurrió un error al cargar los datos:", err);
        this.$toast?.error("Ocurrió un error al cargar los datos.");
      }
    },
    addCreationForm(formData) {
      formData.delete('isNew')
      formData.delete('active')
      formData.delete('open')
      formData.delete('open_visible')
      formData.delete('diploma_select')
      formData.delete('survey')
      if (this.setCoursePoints) {
        formData.delete('points')
      }
      formData.append("description", this.description);
      formData.append("general-information", this.generalInformation);
      formData.append("tags", JSON.stringify(this.tags));
      formData.append("locale", this.locale);
      formData.append("duration", this.parseDurationToMinutes(this.duration));
      if (this.useSegment) {
        formData.append("segments", JSON.stringify(this.selectedSegments));
      }
      if (this.courseDocumentation)
        formData.append("documentation", this.documentation);
    },
    async addUpdateForm(formData) {
      formData.append("code", this.code);
      formData.append("name", this.name);
      formData.append("description", this.description);
      formData.append("general-information", this.generalInformation);
      formData.append("active", this.active);
      formData.append("open", this.open);
      formData.append("duration", this.parseDurationToMinutes(this.duration));
      formData.append("diploma_show_duration", this.diplomaShowDuration);
      formData.append("open-visible", this.open_visible);
      formData.append("open-new", this.open_new);
      if (this.isContentDiploma)
        formData.append("is-content-diploma", this.isContentDiploma);
      if (this.diploma_index)
        formData.append("diploma-index", this.diploma_index);
      if (this.descriptionContentDiploma)
        formData.append(
          "description-content-diploma",
          this.descriptionContentDiploma
        );
      formData.append(
        "filters",
        JSON.stringify(getFiltersForRequest(this.filters))
      );
      formData.append("locale", this.locale);
      formData.append("tags", JSON.stringify(this.tags));
      if (this.useSegment) {
        formData.append("segments", JSON.stringify(this.selectedSegments));
      }
      formData.append("managers", JSON.stringify(this.selectedManagers));
      if (this.courseDocumentation)
        formData.append("documentation", this.documentation);
      formData.append("typeCourse", this.typeCourse_id);

      if (this.survey_id !== null) {
        formData.append("survey", this.survey_id);
      }

      if (this.typeDiploma_id !== null) {
        formData.append("typeDiploma", this.typeDiploma_id);
      }
      
      await this.saveCourse(formData, true, this.$route.params.id);
    },
    async submit() {
      const currentForm = document.forms["new-course-form"];
      const formData = new FormData(currentForm);
      if (this.isUpdate) await this.addUpdateForm(formData)
      else {
        this.addCreationForm(formData)
        if (!this.validateFirstStep()) return;
        await this.saveCourse(formData);
      }
    },
    async saveCourse(formData, update = false, id = null) {
      this.$store.dispatch("loaderModule/setLoading", {
        loading: true,
        message: this.$t("SAVING"),
      });
      
      try {
        const res = update
          ? await this.$store.dispatch("createCourseModule/updateCourse", {
            id,
            formData,
          })
          : await this.$store.dispatch(
            "createCourseModule/createCourse",
            formData
          );
        
        const { error, data } = res;
        
        if (error) {
          throw new Error(error);
        }
        
        const { message, courseData } = data;
        this.$toast.success(message);
        
        this.$store.dispatch(
          "contentTitleModule/removeRouteFromContentTitle",
          this.routeName
        );
        this.$store.dispatch("routerModule/setDeleteLastRoute", true);
        
        this.$router.replace({
          name: ROUTE_NAMES.VIEW_COURSE,
          params: { id: courseData.id, name: courseData.name },
        });
      } catch (error) {
        this.$toast.error(
          error?.message || "Ocurrió un error al guardar el curso"
        );
      } finally {
        this.$store.dispatch("loaderModule/setLoading", {
          loading: false,
        });
      }
    },
    async loadManagers() {
      try {
        const res = await this.$store.dispatch("userModule/loadAllManagers");
        this.availableManagers = res;
      } catch (error) {
        console.error("Error loading managers:", error);
      }
    },
    handleSteps() {
      let firstStep = 1;
      
      if (!this.isUpdate) {
        return this.onCurrentStepChange(firstStep, false);
      }

      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.CERTIFICATES)) {
        this.disabledSteps.push(3);
      } else firstStep = 3;
      
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.AUDIENCE)) {
        this.disabledSteps.push(2);
      } else firstStep = 2
      
      this.onCurrentStepChange(firstStep, false);
    },
    parseDurationToFloat(duration = null){
      if (!duration) return null
      return duration / 60
    },
    parseDurationToMinutes(duration = null){
      if (!duration) return null
      return Math.ceil(duration * 60)
    },
    parseDurationToHumanTime(duration = null) {
      if(!duration) return null
      const minutes = duration * 60;
      return `${Math.floor(minutes / 60)}h ${minutes % 60}m`
    },
    setDuration(total = null) {
      this.diplomaShowDuration = (total > 0)
      this.duration = total
    }
  },
};
</script>

<style scoped lang="scss">
.CreateCourse {
  .form-group {
    margin: 0;
    &.col-xs-12.col-md-6 {
      padding: 0;
      
      @media screen AND (min-width: 768px) {
        padding: 12px 15px;
      }
    }
    &.required {
      label::after {
        content: "*";
        color: red;
      }
    }
    
    @media screen AND (min-width: 768px) {
      padding: 12px 15px;
    }
  }
  
  .form-step {
    width: 100%;
    justify-content: center;
    flex-flow: row wrap;
    &.basic-info {
      .basic-info--tags,
      .basic-info--segments,
      .basic-info--description,
      .basic-info--data {
        width: 100%;
        padding: 0.5rem 0.15rem;
      }
      
      .basic-info--data {
        @media screen AND (min-width: 768px) {
          display: grid;
          grid-template-columns: [thumbnail] 400px [data] auto;
          gap: 1rem;
          
          .locale-selector {
            padding: 12px 15px;
          }
        }
      }
      
      .basic-info--segments {
        overflow-x: auto;
      }
    }
    
    &.general-info {
      .enableCourseStatus {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        
        @media screen and (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }
      
      .audience {
        overflow-x: auto;
        
        .audience-header {
          text-align: center;
        }
      }
    }
  }
  
  .required {
    label::after {
      content: "*";
      color: red;
    }
  }
  
  .audience-header {
    h1 {
      font-size: 24px;
      text-align: center;
    }
    
    p {
      font-size: 18px;
    }
  }
}

.section-title {
  font-weight: bold;
  border-bottom: 1px solid $base-border-color;
  width: 100%;
  font-size: 16px;
  padding-bottom: 0.3rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.diploma-data {
  background-color: #d9f0fa;
  width: 100%;
  padding: 0.5rem 0.8rem;
  margin-top: 1rem;
  
  p {
    margin-bottom: 0;
  }
  
  .row {
    padding: .3rem;
  }
  
  .box-icon {
    color: #fff;
    border-radius: 5px;
    align-self: stretch;
    align-items: center;
    display: grid;
    justify-content: center;
    grid-column: 1;
    grid-row: 1/4;
    font-size: 2rem;
    padding: $spacing-s;
    background: #cfd8e3;
    height: 80px;
    
    img {
      width: 100%;
      filter: brightness(2);
    }
  }
  .actionLink {
    color: $color-primary;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
  }
}

.BaseTimePicker {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  &::v-deep {
    .controls {
      position: relative;
      display: initial !important;
      box-shadow: none;
    }
    
    .form-control {
      background-color: var(--color-neutral-lighter);
      pointer-events: none;
    }
  }
}

.BaseTimePicker {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  &::v-deep {
    .controls {
      position: relative;
      display: initial !important;
      box-shadow: none;
    }
    
    .form-control {
      background-color: var(--color-neutral-lighter);
      pointer-events: none;
    }
  }
}

.textarea.form-control {
  min-height: 150px;
}
</style>
