<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Course\Manager;

use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALCourseManagerRepository implements CourseManagerRepository
{
    public function __construct(
        private Connection $connection,
        private string $courseManagerTableName,
    ) {
    }

    public function findBy(CourseManagerCriteria $criteria): CourseManagerCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new CourseManagerCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToCourseManager($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function fromArrayToCourseManager(array $values): CourseManager
    {
        return new CourseManager(
            userId: new Id($values['user_id']),
            courseId: new Id($values['course_id'])
        );
    }

    private function getQueryBuilderByCriteria(CourseManagerCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->courseManagerTableName, 't');

        if (null !== $criteria->getUserId()) {
            $queryBuilder->andWhere('t.user_id = :userId')
                ->setParameter('userId', $criteria->getUserId()->value());
        }

        if (null !== $criteria->getCourseId()) {
            $queryBuilder->andWhere('t.course_id = :courseId')
                ->setParameter('courseId', $criteria->getCourseId()->value());
        }

        return $queryBuilder;
    }
}
