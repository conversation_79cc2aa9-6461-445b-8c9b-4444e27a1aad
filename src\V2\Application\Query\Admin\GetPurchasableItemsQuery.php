<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\V2\Domain\Bus\Query;
use App\V2\Domain\Purchase\PurchasableItemCriteria;

class GetPurchasableItemsQuery implements Query
{
    public function __construct(
        private PurchasableItemCriteria $criteria,
    ) {
    }

    public function getCriteria(): PurchasableItemCriteria
    {
        return $this->criteria;
    }
}
