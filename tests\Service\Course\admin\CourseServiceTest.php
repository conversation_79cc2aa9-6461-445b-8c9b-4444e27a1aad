<?php

declare(strict_types=1);

namespace App\Tests\Service\Course\admin;

use App\Entity\Course;
use App\Entity\User;
use App\Repository\CourseRepository;
use App\Service\Course\admin\CourseService;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Application\Admin\LegacyAdminUrlGeneratorInterface;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

class CourseServiceTest extends TestCase
{
    private EntityManagerInterface $entityManager;
    private SettingsService $settingsService;
    private CourseCreatorRepository $courseCreatorRepository;
    private LegacyAdminUrlGeneratorInterface $adminUrlGenerator;
    private CourseRepository $courseRepository;
    private TranslatorInterface $translator;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);
        $this->adminUrlGenerator = $this->createMock(LegacyAdminUrlGeneratorInterface::class);
        $this->courseRepository = $this->createMock(CourseRepository::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
    }

    private function getCourseService(): CourseService
    {
        return new CourseService(
            $this->entityManager,
            $this->settingsService,
            $this->translator,
            $this->adminUrlGenerator,
            $this->courseCreatorRepository,
        );
    }

    /**
     * @throws InfrastructureException
     */
    public function testGetRestrictedCourseIdsForAdminRole()
    {
        $courseService = $this->getCourseService();

        $adminUser = UserMother::create(roles: [User::ROLE_ADMIN]);
        $restrictedIds = $courseService->getRestrictedCourseIdsForAccessByUserRole($adminUser);
        $this->assertIsArray($restrictedIds);
        $this->assertEmpty($restrictedIds, 'Admin users should have no course restrictions');
    }

    /**
     * @throws Exception|InfrastructureException
     */
    public function testGetRestrictedCourseIdsForManagerRole()
    {
        $courseService = $this->getCourseService();

        // With managed courses
        $managerUser = UserMother::create(id: 1, roles: [User::ROLE_MANAGER]);
        $managedCourse1 = CourseMother::create(id: 1);
        $managedCourse2 = CourseMother::create(id: 2);

        $this->entityManager->expects($this->exactly(2))
            ->method('getRepository')
            ->with(Course::class)
            ->willReturn($this->courseRepository);

        // Return managed courses. One for each manager call.
        $this->courseRepository->expects($this->exactly(2))
            ->method('getCoursesManager')
            ->willReturnOnConsecutiveCalls(
                [$managedCourse1, $managedCourse2],
                []
            );

        $restrictedIds = $courseService->getRestrictedCourseIdsForAccessByUserRole($managerUser);
        $this->assertIsArray($restrictedIds);
        $this->assertCount(2, $restrictedIds);
        $this->assertContains(1, $restrictedIds);
        $this->assertContains(2, $restrictedIds);

        // With no managed courses
        $managerUserNoManagedCourses = UserMother::create(id: 2, roles: [User::ROLE_MANAGER]);

        $this->courseRepository->expects($this->once())
            ->method('getCoursesManager')
            ->with($managerUserNoManagedCourses)
            ->willReturn([]);

        $restrictedIds = $courseService->getRestrictedCourseIdsForAccessByUserRole($managerUserNoManagedCourses);
        $this->assertIsArray($restrictedIds);
        $this->assertEmpty($restrictedIds, 'Manager with no managed courses should have no restrictions');
    }

    /**
     * @throws Exception
     * @throws InfrastructureException
     */
    public function testGetRestrictedCourseIdsForCreatorUserWithNoCourses()
    {
        $courseService = $this->getCourseService();

        $user = UserMother::create(id: 1, roles: [User::ROLE_CREATOR]);

        $this->entityManager->expects($this->once())
            ->method('getRepository')
            ->with(Course::class)
            ->willReturn($this->courseRepository);

        $this->courseRepository->expects($this->once())
            ->method('findBy')
            ->with(['createdBy' => $user])
            ->willReturn([]);

        $this->courseCreatorRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new CourseCreatorCollection([]));

        $restrictedIds = $courseService->getRestrictedCourseIdsForAccessByUserRole($user);
        $this->assertIsArray($restrictedIds);
        $this->assertEmpty($restrictedIds, 'Creator with no created courses should have no restrictions');
    }

    /**
     * @throws Exception
     * @throws InfrastructureException
     * @throws CollectionException
     */
    public function testGetRestrictedCourseIdsForCreatorUserWithSharedAndCreatedCourses()
    {
        $courseService = $this->getCourseService();

        $user = UserMother::create(id: 5, roles: [User::ROLE_CREATOR]);
        $createdCourse = CourseMother::create(id: 1, createdBy: $user);
        $sharedCourse = CourseMother::create(id: 2);

        $this->entityManager->expects($this->once())
            ->method('getRepository')
            ->with(Course::class)
            ->willReturn($this->courseRepository);

        // Mock created courses
        $this->courseRepository->expects($this->once())
            ->method('findBy')
            ->with(['createdBy' => $user])
            ->willReturn([$createdCourse]);

        // Mock shared creator courses
        $sharedCourseCreators = [
            CourseCreatorMother::create(
                userId: new Id($user->getId()),
                courseId: new Id($sharedCourse->getId())
            ),
        ];

        $this->courseCreatorRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new CourseCreatorCollection($sharedCourseCreators));

        $restrictedIds = $courseService->getRestrictedCourseIdsForAccessByUserRole($user);

        $this->assertIsArray($restrictedIds);
        $this->assertCount(2, $restrictedIds);
        $this->assertContains(1, $restrictedIds); // Created course
        $this->assertContains(2, $restrictedIds); // Shared course 1
    }

    /**
     * @throws InfrastructureException
     * @throws CollectionException
     */
    public function testGetRestrictedCourseIdsForAccessByUserRoleMultipleRolesAndDuplicates()
    {
        $courseService = $this->getCourseService();

        $user = UserMother::create(id: 1, roles: [User::ROLE_MANAGER, User::ROLE_CREATOR]);

        // Course that user both manages and created (should appear only once in result)
        $duplicateCourse = CourseMother::create(id: 1, createdBy: $user);
        $managedOnlyCourse = CourseMother::create(id: 2);
        $createdOnlyCourse = CourseMother::create(id: 3, createdBy: $user);
        $sharedCourse = CourseMother::create(id: 4);

        $this->entityManager->expects($this->exactly(2))
            ->method('getRepository')
            ->with(Course::class)
            ->willReturn($this->courseRepository);

        // Mock manager courses
        $this->courseRepository->expects($this->once())
            ->method('getCoursesManager')
            ->with($user)
            ->willReturn([$duplicateCourse, $managedOnlyCourse]);

        // Mock created courses
        $this->courseRepository->expects($this->once())
            ->method('findBy')
            ->with(['createdBy' => $user])
            ->willReturn([$duplicateCourse, $createdOnlyCourse]);

        $sharedCourseCreators = [
            CourseCreatorMother::create(
                userId: new Id($user->getId()),
                courseId: new Id($sharedCourse->getId())
            ),
        ];

        $this->courseCreatorRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new CourseCreatorCollection($sharedCourseCreators));

        $restrictedIds = $courseService->getRestrictedCourseIdsForAccessByUserRole($user);
        $this->assertIsArray($restrictedIds);
        $this->assertCount(4, $restrictedIds, 'Should remove duplicate course IDs');
        $this->assertContains(1, $restrictedIds);   // Managed and created. Only appears once.
        $this->assertContains(2, $restrictedIds);   // Managed only.
        $this->assertContains(3, $restrictedIds);   // Created only.
        $this->assertContains(4, $restrictedIds);   // Shared course.

        $this->assertEquals(array_unique($restrictedIds), $restrictedIds, 'Should not contain duplicate IDs');
    }
}
