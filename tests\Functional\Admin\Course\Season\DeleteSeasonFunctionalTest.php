<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course\Season;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Season;
use App\Entity\User;
use App\Enum\ChapterContent;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminSeasonEndpoints;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\SeasonEndpointsTrait;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class DeleteSeasonFunctionalTest extends FunctionalTestCase
{
    use SeasonEndpointsTrait;
    use CourseHelperTrait;
    use SeasonHelperTrait;
    use ChapterHelperTrait;
    use ChapterTypeHelperTrait;
    use UserHelperTrait;
    use CourseCreatorFixtureTrait;

    private ?Course $course;
    /** @var Chapter[] */
    private array $chapters;

    private ?User $user = null;

    private ?User $otherUser = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->course = $this->createAndGetCourse();

        $this->user = $this->createAndGetUser(
            firstName: 'test',
            lastName: 'test',
            roles: [User::ROLE_ADMIN],
            email: '<EMAIL>',
        );
        $this->otherUser = $this->createAndGetUser(
            firstName: 'other',
            lastName: 'user',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>',
        );
    }

    public function testDeleteNoChapters(): void
    {
        $userToken = $this->loginAndGetToken();

        $season = $this->createAndGetSeason(course: $this->course);

        $this->client->request(
            'DELETE',
            $this->deleteSeasonEndpoint($season->getId()),
        );

        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertArrayHasKey('error', $content);
        $this->assertFalse($content['error']);
    }

    public function testDeleteWithChapters(): void
    {
        $this->loginAndGetToken();
        $season = $this->createAndGetSeason(course: $this->course, name: 'Season 1');
        $season2 = $this->createAndGetSeason(course: $this->course, name: 'Season 2');
        $chapterType = $this->createAndGetChapterTypeRevised(id: ChapterContent::CONTENT_TYPE, name: 'test');
        $this->createAndGetChapter(
            title: 'Chapter 1',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
        );

        $this->createAndGetChapter(
            title: 'Chapter 1',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
            deletedAt: new \DateTimeImmutable(),
        );

        $this->createAndGetChapter(
            title: 'Chapter 1 Season 2',
            chapterType: $chapterType,
            course: $this->course,
            season: $season2,
        );

        $this->client->request(
            'DELETE',
            $this->deleteSeasonEndpoint($season->getId()),
        );

        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertArrayHasKey('error', $content);
        $this->assertFalse($content['error']);
    }

    public function testTryDeleteWithActiveChapters(): void
    {
        $this->loginAndGetToken();
        $season = $this->createAndGetSeason(course: $this->course);
        $chapterType = $this->createAndGetChapterTypeRevised(id: ChapterContent::CONTENT_TYPE, name: 'test');
        $this->createAndGetChapter(
            title: 'Chapter 1',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
        );

        $this->createAndGetChapter(
            title: 'Chapter 2',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
            deletedAt: new \DateTimeImmutable(),
        );

        $this->createAndGetChapter(
            title: 'Chapter 3',
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
        );

        $this->client->request(
            'DELETE',
            $this->deleteSeasonEndpoint($season->getId()),
        );

        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertArrayHasKey('error', $content);
        $this->assertTrue($content['error']);
        $this->assertArrayHasKey('data', $content);
        $this->assertEquals(1, $content['data']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     */
    #[DataProvider('providerCourseSeasonDeletionAuthorization')]
    public function testCourseSeasonDeletionAuthorization(
        array $roles,
        bool $isCreatorCourse,
        bool $shareCourseWithUser,
        int $expectedStatusCode,
    ): void {
        $this->user->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $course = $this->createAndGetCourse(
            createdBy: $isCreatorCourse ? $this->user : $this->otherUser,
        );

        $season = $this->createAndGetSeason(
            course: $course,
            name: 'Season to delete',
            type: 'sequential',
        );

        if ($shareCourseWithUser && !$isCreatorCourse) {
            $this->setAndGetCourseCreatorInRepository(
                userId: new Id($this->user->getId()),
                courseId: new Id($course->getId()),
            );
        }

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'DELETE',
            uri: AdminSeasonEndpoints::courseSeasonEndpoint($season->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerCourseSeasonDeletionAuthorization(): \Generator
    {
        yield 'Super Admin can delete season' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Admin can delete season' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager cannot delete season' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 403,
        ];
        yield 'Creator can delete season if is creator of course' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorCourse' => true,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator can delete season if the course is shared with him' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator cannot delete season if not related to course' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 403,
        ];
        yield 'Tutor cannot delete season' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'Inspector cannot delete season' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'Support cannot delete season' => [
            'roles' => [User::ROLE_SUPPORT],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'User cannot delete season' => [
            'roles' => [User::ROLE_USER],
            'isCreatorCourse' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        // Clean stored data
        $this->truncateEntities([
            Chapter::class,
            Season::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->user?->getId(),
            $this->otherUser?->getId(),
        ]);

        parent::tearDown();
    }
}
