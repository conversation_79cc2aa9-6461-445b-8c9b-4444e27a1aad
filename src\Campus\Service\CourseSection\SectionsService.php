<?php

declare(strict_types=1);

namespace App\Campus\Service\CourseSection;

use App\Campus\Service\Base\BaseService;
use App\Campus\Service\User\TokenExtractorService;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\User;
use App\Modules\CourseSection\Enum\SectionTypeEnum;
use App\Repository\CourseRepository;
use App\Repository\CourseSectionRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Symfony\Component\Security\Core\Security;

class SectionsService extends BaseService
{
    private $courseRepository;
    private $courseSectionRepository;
    private $tokenExtractorService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security,
        CourseRepository $courseRepository,
        CourseSectionRepository $courseSectionRepository,
        TokenExtractorService $tokenExtractorService
    ) {
        parent::__construct($em, $settings, $security);
        $this->courseRepository = $courseRepository;
        $this->courseSectionRepository = $courseSectionRepository;
        $this->tokenExtractorService = $tokenExtractorService;
    }

    /**
     * Get all sections than user has access.
     */
    public function getCoursesSectionForCampus(): array
    {
        $sections = $this->getCoursesSectionByPayload();
        $sections = $this->filterEmptySections($sections);

        $sectionsTranslated = [];
        foreach ($sections as $section) {
            $sectionsTranslated[] = [
                'id' => $section->getId(),
                'name' => $section->getName(),
                'slug' => $section->getSlug(),
                'sort' => $section->getSort(),
                'hideCategoryName' => $section->getHideCategoryName(),
                'isMain' => $section->isIsMain(),
                'openCampus' => $section->isOpenCampus(),
                'isItineraries' => $section->isItineraries(),
                'isAnnouncements' => $section->isAnnouncements(),
                'categories' => $section->getHideCategoryName() ? [] : $this->getCategoriesBySection($section->getCategories()),
            ];
        }

        return $sectionsTranslated;
    }

    public function getCoursesSectionByPayload()
    {
        $payload = $this->tokenExtractorService->getPayloadToken();
        $locale = $this->getUser()->getLocaleCampus();

        if (!$payload || empty($payload['course'])) {
            return $this->courseSectionRepository->getSectionsActives($locale);
        }

        if ($payload && $payload['announcement']) {
            return $this->courseSectionRepository->getSectionsActives($locale, [SectionTypeEnum::SECTION_ANNOUNCEMENTS]);
        }

        return $this->courseSectionRepository->getSectionsActives($locale);
    }

    private function getCategoriesBySection($courseCategories): array
    {
        $categories = [];
        $locale = $this->getUser()->getLocaleCampus() ?? $this->settings->get('app.default_locale');
        foreach ($courseCategories as $category) {
            $name = null;
            if (!empty($locale)) {
                /** @var CourseCategory $translation */
                $translation = $category->translate($locale);
                $name = $translation->getName();
            }

            $categories[] = [
                'id' => $category->getId(),
                'name' => $name ?? $category->getName(),
                'slug' => $category->getNormalize($name ?? $category->getName()),
                'slugMenu' => $category->getNormalize($name ?? $category->getName()),
                'sort' => $category->getSort(),
            ];
        }

        // Sort categories by sort field to ensure consistent ordering
        usort($categories, function ($a, $b) {
            return $a['sort'] <=> $b['sort'];
        });

        return $categories;
    }

    /**
     * Clean sections that not have categories.
     *
     * @throws NonUniqueResultException
     */
    private function filterEmptySections(array $sections): array
    {
        /** @var User $user */
        $user = $this->getUser();
        $categoriesByOpenCampus = $this->courseRepository->getCategoriesByOpenCampusThanUserHasAccess(user: $user);
        $categoriesByFilter = $this->findUserAccessibleCategoriesByFilter();

        foreach ($sections as $key => $section) {
            $categories = $this->filterAccessibleCategoriesForUser($categoriesByOpenCampus, $categoriesByFilter, $section);

            $this->addAllCourseCategoriesToCourseSection($section);
            $this->removeCourseCategoriesIfNotHasCourse($section, $categories);

            if ($section->isItineraries() || $section->isAnnouncements()) {
                continue;
            }

            if (0 === $section->getCategories()->count()) {
                unset($sections[$key]);
            }
        }

        return array_values($sections);
    }

    /**
     * Filter categories that user has access.
     */
    private function filterAccessibleCategoriesForUser(array $categoriesByOpenCampus, array $categoriesByFilter, CourseSection $courseSection): array
    {
        if (!$courseSection->isOpenCampus() && !$courseSection->isCourseByFilter()) {
            return [];
        }

        $allCategories = [];

        if ($courseSection->isOpenCampus()) {
            $allCategories = $categoriesByOpenCampus ?? [];
        }

        if ($courseSection->isCourseByFilter()) {
            $allCategories = array_merge($allCategories, $categoriesByFilter);
        }

        return array_unique(array_column($allCategories, 'idCategory'));
    }

    private function findUserAccessibleCategoriesByFilter(): array
    {
        $user = $this->getUser();

        if (0 === \count($user->getFilter())) {
            return [];
        }

        $categoriesAccess = $this->courseRepository->searchCourseByFilter($user->getFilter());

        $categoriesByFilter = [];

        foreach ($categoriesAccess as $categoryData) {
            $course = $this->em->getRepository(Course::class)->find($categoryData['idCourse']);

            if ($course && $course->checkUserFilterAccess($user)) {
                $category = $course->getCategory();
                if ($category && !\in_array($category->getId(), $categoriesByFilter)) {
                    $categoriesByFilter[] = ['idCategory' => $category->getId()];
                }
            }
        }

        return $categoriesByFilter;
    }

    /**
     * Add all course categories to course section, if not has categories.
     */
    public function addAllCourseCategoriesToCourseSection(CourseSection $courseSection): CourseSection
    {
        if ($courseSection->getCategories()->count() > 0 && $courseSection->isIsManualSelection()) {
            return $courseSection;
        }

        $categories = $this->getCourseCategoryActive();
        foreach ($categories as $category) {
            $courseSection = $this->addCourseCategory($courseSection, $category);
        }

        return $courseSection;
    }

    private function addCourseCategory(CourseSection $courseSection, CourseCategory $courseCategory): CourseSection
    {
        $courseSection->addCategory($courseCategory);
        $this->em->persist($courseSection);
        $this->em->flush();

        return $courseSection;
    }

    private function getCourseCategoryActive(): array
    {
        $categories = $this->em->getRepository(CourseCategory::class)->findAll();

        return $categories;
    }

    private function removeCourseCategoriesIfNotHasCourse(CourseSection $section, $categories): void
    {
        foreach ($section->getCategories() as $category) {
            if (!\in_array($category->getId(), $categories)) {
                $section->removeCategory($category);
            }
        }
    }
}
