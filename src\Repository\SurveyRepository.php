<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Survey;
use App\Entity\SurveyTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Survey>
 *
 * @method Survey|null find($id, $lockMode = null, $lockVersion = null)
 * @method Survey|null findOneBy(array $criteria, array $orderBy = null)
 * @method Survey[]    findAll()
 * @method Survey[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SurveyRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Survey::class);
    }

    public function add(Survey $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Survey $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getActiveTranslatedList(?string $locale = null): array
    {
        $data = [];
        $surveyData = $this->createQueryBuilder('s')
            ->select('s')
            ->where('s.active = 1')
            ->andWhere('s.applyTo in (1,2)')
            ->getQuery()
            ->getResult();

        foreach ($surveyData as $survey) {
            $name = $survey->getName();

            /** @var SurveyTranslation $translation */
            $translation = $survey->translate($locale);
            if ($translation) {
                if (!\is_null($translation->getName()) && \strlen(trim($translation->getName())) > 0) {
                    $name = $translation->getName();
                }
            }

            $data[] = [
                'id' => $survey->getId(),
                'name' => $name,
                'isMain' => (bool) $survey->isIsMain(),
            ];
        }

        return $data;
    }
}
