<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\PutPurchasableItemValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class PutPurchasableItemValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        PutPurchasableItemValidator::validatePutPurchasableItemRequest($payload);
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Valid course with EUR currency' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
        ];

        yield 'Valid subscription with USD currency' => [
            [
                'type' => 'subscription',
                'resource_id' => 2,
                'price_amount' => 2500,
                'price_currency' => 'USD',
            ],
        ];

        yield 'Valid with zero price amount' => [
            [
                'type' => 'course',
                'resource_id' => 3,
                'price_amount' => 0,
                'price_currency' => 'EUR',
            ],
        ];

        yield 'Valid with large resource_id' => [
            [
                'type' => 'subscription',
                'resource_id' => 999999,
                'price_amount' => 50000,
                'price_currency' => 'USD',
            ],
        ];

        yield 'Valid with large price amount' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 999999999,
                'price_currency' => 'EUR',
            ],
        ];
    }

    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            PutPurchasableItemValidator::validatePutPurchasableItemRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Empty body' => [
            [],
            [
                '' => 'Body cannot be empty',
                '[type]' => 'This field is missing.',
                '[resource_id]' => 'This field is missing.',
                '[price_amount]' => 'This field is missing.',
                '[price_currency]' => 'This field is missing.',
            ],
        ];

        yield 'Missing type field' => [
            [
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[type]' => 'This field is missing.',
            ],
        ];

        yield 'Missing resource_id field' => [
            [
                'type' => 'course',
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[resource_id]' => 'This field is missing.',
            ],
        ];

        yield 'Missing price_amount field' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_currency' => 'EUR',
            ],
            [
                '[price_amount]' => 'This field is missing.',
            ],
        ];

        yield 'Missing price_currency field' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 1000,
            ],
            [
                '[price_currency]' => 'This field is missing.',
            ],
        ];

        yield 'Empty type field' => [
            [
                'type' => '',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[type]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'Invalid type - not string' => [
            [
                'type' => 123,
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[type]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'Invalid type - wrong choice' => [
            [
                'type' => 'invalid_type',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[type]' => 'The value you selected is not a valid choice.',
            ],
        ];

        yield 'Empty resource_id field' => [
            [
                'type' => 'course',
                'resource_id' => '',
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[resource_id]' => [
                    'This value should not be blank.',
                    'This value should be an integer or a UUID v4.',
                ],
            ],
        ];

        yield 'Invalid resource_id - not integer' => [
            [
                'type' => 'course',
                'resource_id' => 'invalid',
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[resource_id]' => 'This value should be an integer or a UUID v4.',
            ],
        ];

        yield 'Invalid resource_id - zero' => [
            [
                'type' => 'course',
                'resource_id' => 0,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[resource_id]' => 'This value should be greater than 0.',
            ],
        ];

        yield 'Invalid resource_id - negative' => [
            [
                'type' => 'course',
                'resource_id' => -1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            [
                '[resource_id]' => 'This value should be greater than 0.',
            ],
        ];

        yield 'Empty price_amount field' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => '',
                'price_currency' => 'EUR',
            ],
            [
                '[price_amount]' => [
                    'This value should not be blank.',
                    'This value should be of type integer.',
                    'This value should be greater than or equal to 0.',
                ],
            ],
        ];

        yield 'Invalid price_amount - not integer' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 'invalid',
                'price_currency' => 'EUR',
            ],
            [
                '[price_amount]' => 'This value should be of type integer.',
            ],
        ];

        yield 'Invalid price_amount - negative' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => -1,
                'price_currency' => 'EUR',
            ],
            [
                '[price_amount]' => 'This value should be greater than or equal to 0.',
            ],
        ];

        yield 'Empty price_currency field' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => '',
            ],
            [
                '[price_currency]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'Invalid price_currency - not string' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 123,
            ],
            [
                '[price_currency]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'Invalid price_currency - wrong choice' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'GBP',
            ],
            [
                '[price_currency]' => 'The value you selected is not a valid choice.',
            ],
        ];

        yield 'Multiple validation errors' => [
            [
                'type' => 'invalid_type',
                'resource_id' => 0,
                'price_amount' => -1,
                'price_currency' => 'GBP',
            ],
            [
                '[type]' => 'The value you selected is not a valid choice.',
                '[resource_id]' => 'This value should be greater than 0.',
                '[price_amount]' => 'This value should be greater than or equal to 0.',
                '[price_currency]' => 'The value you selected is not a valid choice.',
            ],
        ];

        yield 'All fields invalid types' => [
            [
                'type' => 123,
                'resource_id' => 'invalid',
                'price_amount' => 'invalid',
                'price_currency' => 123,
            ],
            [
                '[type]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
                '[resource_id]' => 'This value should be an integer or a UUID v4.',
                '[price_amount]' => 'This value should be of type integer.',
                '[price_currency]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'Extra unexpected field' => [
            [
                'type' => 'course',
                'resource_id' => 1,
                'price_amount' => 1000,
                'price_currency' => 'EUR',
                'unexpected_field' => 'value',
            ],
            [
                '[unexpected_field]' => 'This field was not expected.',
            ],
        ];
    }
}
