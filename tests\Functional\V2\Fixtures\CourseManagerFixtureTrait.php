<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Persistence\Course\Manager\InMemoryCourseManagerRepository;

trait CourseManagerFixtureTrait
{
    private function getCourseManagerRepository(): object
    {
        return $this->client->getContainer()
            ->get(CourseManagerRepository::class);
    }

    private function setAndGetCourseManagerInRepository(
        ?Id $userId = null,
        ?Id $courseId = null,
    ): CourseManager {
        $courseManager = CourseManagerMother::create(
            userId: $userId,
            courseId: $courseId
        );

        /** @var InMemoryCourseManagerRepository $repository */
        $repository = $this->getCourseManagerRepository();
        $repository->add($courseManager);

        // TODO: Update when write is supported in the repository

        return $courseManager;
    }
}
