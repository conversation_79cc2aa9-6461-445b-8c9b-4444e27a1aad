<?php

declare(strict_types=1);

namespace App\Tests\Entity;

use App\Entity\VcmsProject;
use PHPUnit\Framework\TestCase;

class VcmsProjectTest extends TestCase
{
    public function testHasSlidesWithContentReturnsFalseWhenNoSlides(): void
    {
        $project = new VcmsProject();

        $project->setSlides([]);
        $this->assertFalse($project->hasSlidesWithContent());
    }

    public function testHasSlidesWithContentReturnsFalseWhenSlidesHaveNoElements(): void
    {
        $project = new VcmsProject();
        $project->setSlides([
            ['id' => 1, 'elements' => []],
            ['id' => 2, 'elements' => []],
        ]);
        $this->assertFalse($project->hasSlidesWithContent());
    }

    public function testHasSlidesWithContentReturnsTrueWhenAtLeastOneSlideHasElements(): void
    {
        $project = new VcmsProject();
        $project->setSlides([
            ['id' => 1, 'elements' => []],
            ['id' => 2, 'elements' => [['id' => 1, 'type' => 'text']]],
        ]);
        $this->assertTrue($project->hasSlidesWithContent());
    }
}
