export class CourseChapter {
    constructor({
                    id,
                    name,
                    image,
                    description,
                    typeId,
                    typeName,
                    order,
                    seasonId,
                    seasonName,
                    seasonOrder,
                    icon,
                    hasContentCompleted,
                    editUrl,
                    projectEditUrl,
                    projectViewUrl,
                    'thumbnail-url': thumbnailUrl,
                }) {
        this.id = id ?? null;
        this.name = name ?? null;
        this.image = image ?? null;
        this.description = description ?? null;
        this.typeId = typeId ?? null;
        this.typeName = typeName ?? null;
        this.order = order ?? null;
        this.seasonId = seasonId ?? null;
        this.seasonName = seasonName ?? null;
        this.seasonOrder = seasonOrder ?? null;
        this.icon = icon ?? null;
        this.hasContentCompleted = hasContentCompleted ?? null;
        this.editUrl = editUrl ?? null;
        this.projectEditUrl = projectEditUrl ?? null;
        this.projectViewUrl = projectViewUrl ?? null;
        this.thumbnailUrl = thumbnailUrl ?? null;
    }
}

export class CourseChapters {
    constructor({courseChapters, addChapterUrl}) {
        this.chapters = courseChapters
            ? Array.isArray(courseChapters)
                ? courseChapters.map((chapter) => new CourseChapter(chapter))
                : []
            : null;
        this.addChapterUrl = addChapterUrl ?? null;
    }
}
