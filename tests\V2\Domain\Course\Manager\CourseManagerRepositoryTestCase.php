<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course\Manager;

use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\TestCase;

abstract class CourseManagerRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): CourseManagerRepository;

    abstract protected function addItem(CourseManager $courseManager): void;

    public function testFindBy(): void
    {
        $repository = $this->getRepository();
        $course1Manager1 = CourseManagerMother::create(
            userId: new Id(1),
            courseId: new Id(1),
        );
        $course2Manager1 = CourseManagerMother::create(
            userId: new Id(1),
            courseId: new Id(2),
        );
        $course3Manager1 = CourseManagerMother::create(
            userId: new Id(1),
            courseId: new Id(3),
        );
        $course4Manager1 = CourseManagerMother::create(
            userId: new Id(1),
            courseId: new Id(4),
        );
        $course1Manager2 = CourseManagerMother::create(
            userId: new Id(2),
            courseId: new Id(1),
        );
        $course2Manager2 = CourseManagerMother::create(
            userId: new Id(2),
            courseId: new Id(2),
        );
        $course3Manager2 = CourseManagerMother::create(
            userId: new Id(2),
            courseId: new Id(3),
        );
        $course4Manager2 = CourseManagerMother::create(
            userId: new Id(2),
            courseId: new Id(4),
        );

        $this->addItem($course1Manager1);
        $this->addItem($course2Manager1);
        $this->addItem($course3Manager1);
        $this->addItem($course4Manager1);
        $this->addItem($course1Manager2);
        $this->addItem($course2Manager2);
        $this->addItem($course3Manager2);
        $this->addItem($course4Manager2);

        $result = $repository->findBy(
            CourseManagerCriteria::createEmpty()
        );

        $this->assertCount(8, $result);

        $result = $repository->findBy(
            CourseManagerCriteria::createEmpty()->filterByCourseId(
                new Id(1)
            )
        );
        $this->assertCount(2, $result);
        $this->assertEquals($course1Manager1, $result->first());
        $this->assertEquals($course1Manager2, $result->last());

        $result = $repository->findBy(
            CourseManagerCriteria::createEmpty()->filterByUserId(
                new Id(1)
            )
        );
        $this->assertCount(4, $result);
        $this->assertEquals($course1Manager1, $result->first());
        $this->assertEquals($course4Manager1, $result->last());

        $result = $repository->findBy(
            CourseManagerCriteria::createEmpty()
                ->filterByUserId(new Id(1))
                ->filterByCourseId(new Id(4))
        );

        $this->assertCount(1, $result);
        $this->assertEquals($course4Manager1, $result->first());

        $result = $repository->findBy(
            CourseManagerCriteria::createEmpty()
                ->filterByUserId(new Id(1))
                ->filterByCourseId(new Id(5))
        );

        $this->assertCount(0, $result);

        $result = $repository->findBy(
            CourseManagerCriteria::createEmpty()
                ->filterByUserId(new Id(4))
                ->filterByCourseId(new Id(4))
        );

        $this->assertCount(0, $result);
    }
}
