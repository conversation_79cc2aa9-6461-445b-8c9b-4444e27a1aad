import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export const USER_API_ROUTES = {
  HOME: {
    LIST: '/users',
    EXPORT: '/users/export',
    IMPERSONATE: '/users/impersonate',
    FILTER_LIST: '/filters/by-categories',
    TOGGLE_ACTIVE: '/users/:id/active',
    REMOVE: '/users/:id/remove',
  },
  FORM: {
    INIT_DATA: '/users/:id',
    EXTRA_FIELDS: '/users/extra',
    NEW_USER: '/users',
    UPDATE_USER: '/users/:id',
  },
  PROFILE: {
    INIT_DATA: '/users/:id/profile',
    COURSES: '/user/:id/courses',
    DETAILS: '/user/detail-stats/:id',
    CHAPTERS: '/user/:id/chapters',
    MESSAGES: '/user/:id/messages',
    COURSES_LIST: '/user/:id/courses-list',
    LOGINS: '/user/:id/logins',
    TIME: '/user/:id/time-spent',
    ITINERARY: '/user/:id/itinerary',
  },
}

export const USER_ROLE_NAMES = {
  [USER_ROLE_LIST.SUPER_ADMIN]: 'USER.ROLES.SUPER_ADMINISTRATOR',
  [USER_ROLE_LIST.ADMIN]: 'USER.ROLES.ADMINISTRATOR',
  [USER_ROLE_LIST.TUTOR]: 'ANNOUNCEMENT.ASSISTANCE_TUTOR',
  [USER_ROLE_LIST.MANAGER]: 'USER.ROLES.MANAGER',
  [USER_ROLE_LIST.CREATOR]: 'user.roles.creator',
  [USER_ROLE_LIST.USER]: 'USER.ROLES.USER',
  [USER_ROLE_LIST.SUBSIDIZER]: 'USER.ROLES.SUBSIDIZER',
  [USER_ROLE_LIST.MANAGER_EDITOR]: 'USER.ROLES.MANAGER_EDITOR',
  [USER_ROLE_LIST.TEAM_MANAGER]: 'USER.ROLES.TEAM_MANAGER',
  [USER_ROLE_LIST.INSPECTOR]: 'USER.ROLES.INSPECTOR',
  [USER_ROLE_LIST.DEVELOPER]: 'USER.ROLES.DEVELOPER',
  [USER_ROLE_LIST.SUPPORT]: 'USER.ROLES.SUPPORT',
}
