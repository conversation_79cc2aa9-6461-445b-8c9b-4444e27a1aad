<?php

declare(strict_types=1);

namespace App\Tests\Unit\Repository;

use App\Entity\Fillgaps;
use App\Repository\FillgapsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\FilterCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

/**
 * Unit test for FillgapsRepository::findWithDeleted method.
 * Tests soft-delete filter management following established pattern.
 */
class FillgapsRepositoryTest extends TestCase
{
    private const int VALID_ENTITY_ID = 123;
    private const int INVALID_ENTITY_ID = 999;
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    private MockObject|EntityManagerInterface $entityManager;
    private MockObject|FilterCollection $filterCollection;
    private MockObject|FillgapsRepository $repository;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->filterCollection = $this->createMock(FilterCollection::class);

        $this->repository = $this->createPartialMock(FillgapsRepository::class, ['getEntityManager', 'findOneBy']);
        $this->repository->method('getEntityManager')->willReturn($this->entityManager);
    }

    public function testFindWithDeletedReturnsEntityWhenFound(): void
    {
        $fillgapsId = self::VALID_ENTITY_ID;
        $expectedEntity = new Fillgaps();

        $this->entityManager->expects($this->once())
            ->method(constraint: 'getFilters')
            ->willReturn($this->filterCollection);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'disable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->repository->expects($this->once())
            ->method(constraint: 'findOneBy')
            ->with(criteria: ['id' => $fillgapsId])
            ->willReturn($expectedEntity);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'enable')
            ->with(self::SOFT_DELETE_FILTER);

        $result = $this->repository->findWithDeleted(id: $fillgapsId);

        $this->assertSame(expected: $expectedEntity, actual: $result);
    }

    public function testFindWithDeletedReturnsNullWhenNotFound(): void
    {
        $fillgapsId = self::INVALID_ENTITY_ID;

        $this->entityManager->expects($this->once())
            ->method(constraint: 'getFilters')
            ->willReturn($this->filterCollection);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'disable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->repository->expects($this->once())
            ->method(constraint: 'findOneBy')
            ->with(criteria: ['id' => $fillgapsId])
            ->willReturn(null);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'enable')
            ->with(self::SOFT_DELETE_FILTER);

        $result = $this->repository->findWithDeleted(id: $fillgapsId);

        $this->assertNull(actual: $result);
    }

    public function testFindWithDeletedEnsuresFilterIsReenabledOnException(): void
    {
        $fillgapsId = self::VALID_ENTITY_ID;

        $this->entityManager->expects($this->once())
            ->method(constraint: 'getFilters')
            ->willReturn($this->filterCollection);

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'disable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->repository->expects($this->once())
            ->method(constraint: 'findOneBy')
            ->with(criteria: ['id' => $fillgapsId])
            ->willThrowException(new \Exception('Database error'));

        $this->filterCollection->expects($this->once())
            ->method(constraint: 'enable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->repository->findWithDeleted(id: $fillgapsId);
    }
}
