<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PutPurchasableItemCommand;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Shared\Financial\MoneyTransformer;
use App\V2\Infrastructure\Shared\Resource\ResourceTransformer;
use App\V2\Infrastructure\Validator\Admin\PutPurchasableItemValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PutPurchasableItemController extends CommandBusAccessor
{
    /**
     * @throws InvalidCurrencyCodeException
     * @throws ValidatorException
     * @throws InvalidUuidException
     */
    public function __invoke(Request $request, ParameterBagInterface $parameterBag): Response
    {
        $payload = json_decode($request->getContent(), true);
        $payload['price_currency'] ??= $parameterBag->get('app.currency_code');

        PutPurchasableItemValidator::validatePutPurchasableItemRequest($payload);

        $this->execute(
            new PutPurchasableItemCommand(
                resource: ResourceTransformer::fromPayload($payload),
                price: MoneyTransformer::fromPayload($payload),
            )
        );

        return new JsonResponse([], Response::HTTP_CREATED);
    }
}
