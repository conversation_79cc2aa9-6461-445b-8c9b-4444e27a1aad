<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter;

use App\Tests\V2\Domain\Filter\FilterRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Infrastructure\Persistence\Filter\DBALFilterRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Schema\Schema;

class DBALFilterRepositoryTest extends FilterRepositoryTestCase
{
    private const string TABLE_NAME = 'filter';
    private Connection $connection;

    protected function getRepository(): FilterRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALFilterRepository(
            connection: $this->connection,
            filterTableName: self::TABLE_NAME,
        );
    }

    private function createTable(): void
    {
        $this->connection->executeQuery('DROP TABLE IF EXISTS ' . static::TABLE_NAME);
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'integer');
        $table->addColumn('filter_category_id', 'integer');
        $table->addColumn('name', 'string', ['length' => 255]);
        $table->addColumn('code', 'string', ['length' => 50, 'notnull' => false]);
        $table->addColumn('sort', 'integer');
        $table->addColumn('parent_id', 'integer', ['notnull' => false]);

        $table->setPrimaryKey(['id']);
        $table->addUniqueConstraint(columnNames: ['code'], indexName: 'INX_UNQ_FILTER_CODE');
        $this->connection->createSchemaManager()->createTable($table);
    }

    protected function addFilter(Filter $filter): void
    {
        try {
            $this->connection->insert(
                table: self::TABLE_NAME,
                data: [
                    'id' => $filter->getId()->value(),
                    'filter_category_id' => $filter->getFilterCategoryId()->value(),
                    'name' => $filter->getName(),
                    'code' => $filter->getCode(),
                    'sort' => $filter->getSort(),
                    'parent_id' => $filter->getParentId()?->value(),
                ]
            );
        } catch (DBALException $e) {
            $this->fail($e->getMessage());
        }
    }
}
