<?php

declare(strict_types=1);

namespace App\Service\Course\admin;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseCategoryTranslation;
use App\Entity\Nps;
use App\Entity\Season;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseTranslation;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\TypeCourse as EnumTypeCourse;
use App\Service\SettingsService;
use App\V2\Application\Admin\LegacyAdminUrlGeneratorInterface;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityNotFoundException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\Translation\TranslatorInterface;

class CourseService
{
    private EntityManagerInterface $em;
    protected TranslatorInterface $trans;
    private SettingsService $settings;
    private LegacyAdminUrlGeneratorInterface $adminUrlGenerator;
    private CourseCreatorRepository $courseCreatorRepository;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        TranslatorInterface $translator,
        LegacyAdminUrlGeneratorInterface $adminUrlGenerator,
        CourseCreatorRepository $courseCreatorRepository
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->trans = $translator;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->courseCreatorRepository = $courseCreatorRepository;
    }

    /**
     * @throws InfrastructureException
     */
    public function getAllCourse($content, $user): array
    {
        $conditions = $content['conditions'] ?? [];
        $page = $content['page'] ?? 1;

        $sortBy = !empty($content['sortBy']) ? "c.{$content['sortBy']}" : 'c.createdAt';
        $sortDirection = !empty($content['sortDir']) ? $content['sortDir'] : 'DESC';

        $sharedAsCreatorCourseIds = $this->getSharedCreatorCourseIds($user);
        $courses = $this->em->getRepository(Course::class)
            ->findCourseStatsByFilters(
                user: $user,
                conditions: $conditions,
                page: $page,
                sortBy: $sortBy,
                sortDir: $sortDirection,
                sharedAsCreatorCourseIds: $sharedAsCreatorCourseIds
            );

        $data = [];
        foreach ($courses as $course) {
            $languagesCourse = $this->getTranslationsByCourse($course);
            $typeCourseTranslation = $this->em->getRepository(TypeCourseTranslation::class)->findOneBy([
                'translatable' => $course->getTypeCourse(),
                'locale' => $user->getLocale() ?: $this->settings->get('app.default_locale'),
            ]);
            $data[] = [
                'id' => $course->getId(),
                'code' => $course->getCode(),
                'name' => $course->getName(),
                'typeCourse' => $typeCourseTranslation->getName(),
                'typeCourseId' => $course->getTypeCourse()->getId(),
                'icon' => $course->getTypeCourse()->getIcon(),
                'category' => $course->getCategory()->getName(),
                'open' => $course->getOpen(),
                'active' => $course->getActive(),
                'totalChapters' => $course->getTotalChapter(),
                'thumbnailUrl' => $course->getThumbnailUrl(),
                'locale' => $course->getLocale(),
                'completed' => $course->isCompleted(),
                'languages' => $languagesCourse,
            ];
        }

        return $data;
    }

    /**
     * @throws InfrastructureException
     */
    private function getSharedCreatorCourseIds(User $user): array
    {
        if (!$user->isCreator()) {
            return [];
        }

        $criteria = CourseCreatorCriteria::createEmpty()
            ->filterByUserId(new Id($user->getId()));
        $coursesCreator = $this->courseCreatorRepository->findBy($criteria);

        if ($coursesCreator->isEmpty()) {
            return [];
        }

        $courseIds = [];
        foreach ($coursesCreator as $courseCreator) {
            $courseIds[] = $courseCreator->getCourseId()->value();
        }

        return $courseIds;
    }

    public function getAllTypeCoursesByLocale($locale): array
    {
        $data = [];
        $typeCourses = $this->em->getRepository(TypeCourse::class)
            ->findBy(['active' => true, 'denomination' => EnumTypeCourse::INTERN]);

        foreach ($typeCourses as $type) {
            $element = new \stdClass();

            /** @var TypeCourseTranslation $translation */
            $translation = $type->translate($locale);
            $element->id = $type->getId();
            $element->name = $translation->getName() ?? $type->getName();

            $data[] = $element;
        }

        return $data;
    }

    public function getAllCourseCategoriesByLocale($locale): array
    {
        $data = [];
        $courseCategories = $this->em->getRepository(CourseCategory::class)->findAll();

        foreach ($courseCategories as $courseCategory) {
            $element = new \stdClass();

            /** @var CourseCategoryTranslation $translation */
            $translation = $courseCategory->translate($locale);
            $element->id = $courseCategory->getId();
            $element->name = $translation->getName() ?? $courseCategory->getName();

            $data[] = $element;
        }

        return $data;
    }

    /**
     * @throws InfrastructureException
     */
    public function getTotalCourses($content, $user): int
    {
        $conditions = $content['conditions'] ?? [];
        $sharedAsCreatorCourseIds = $this->getSharedCreatorCourseIds($user);

        return \count($this->em->getRepository(Course::class)->findTotalCourseStatsByFilters(
            conditions: $conditions,
            user: $user,
            sharedAsCreatorCourseIds: $sharedAsCreatorCourseIds
        ));
    }

    public function getCoursedata(Course $course, $locale): array
    {
        $typeCourseTranslation = $this->em->getRepository(TypeCourseTranslation::class)->findOneBy([
            'translatable' => $course->getTypeCourse(),
            'locale' => $locale,
        ]);

        return [
            'id' => $course->getId(),
            'name' => $course->getName(),
            'category' => $course->getCategory()->getName(),
            'updatedBy' => $course->getUpdatedBy() ? $this->getUserAsString($course->getUpdatedBy()) : '',
            'createdBy' => $course->getCreatedBy() ? $this->getUserAsString($course->getCreatedBy()) : '',
            'createdAt' => $course->getCreatedAt(),
            'updatedAt' => $course->getUpdatedAt(),
            'description' => $course->getDescription(),
            'generalInformation' => $course->getGeneralInformation(),
            'image' => $this->settings->get('app.course_uploads_path') . DIRECTORY_SEPARATOR . $course->getImage(),
            'typeCourseId' => $course->getTypeCourse()->getId(),
            'typeCourse' => $typeCourseTranslation->getName(),
            'icon' => $course->getTypeCourse()->getIcon(),
            'totalChapters' => $course->getTotalChapter(),
            'locale' => $course->getLocale(),
            'languages' => $this->getTranslationsByCourse($course),
            'active' => $course->getActive(),
            'open' => $course->getOpen(),
            'open_visible' => $course->getOpenVisible(),
            'isNew' => $course->getIsNew(),
            'translation' => $course->getTranslation()?->getId(),
            'averageRating' => $this->getAverageRating($course),
            'usersStartCourse' => $this->em->getRepository(UserCourse::class)->countByCourse($course, false), // esto debe modificarse, mirar la tarea EL-2689
            'usersFinishCourse' => $this->em->getRepository(UserCourse::class)->countByCourse($course, true),
            'totalTimeCourse' => $this->em->getRepository(UserCourseChapter::class)->getTimeSpentByCourse($course),
        ];
    }

    private function getUserAsString(User $user): string
    {
        try {
            return $user->__toString();
        } catch (EntityNotFoundException $e) {
            $user = $this->em->getRepository(User::class)->findWithDeleted($user->getId());
            if ($user) {
                return $user->__toString();
            }

            return '';
        }
    }

    private function getAverageRating(Course $course): float|int
    {
        $queryParameters['pageSize'] = 20;
        $queryParameters['page'] = 1;
        $queryParameters['courseId'] = $course->getId();
        $queryParameters['typeCodeCourse'] = $course->getTypeCourse() ? $course->getTypeCourse()->getCode() : TypeCourse::CODE_ONLINE;
        $opinionsQuery = $this->em->getRepository(Nps::class)->getOpinionsByCourse($queryParameters);

        $total = 0;
        $count = \count($opinionsQuery);
        if (0 === $count) {
            return 0;
        }
        foreach ($opinionsQuery as $opinion) {
            $total += $opinion['rating'] && $opinion['rating'] > 0 ? \floatval($opinion['rating']) : null;
        }

        return $total / $count;
    }

    public function getCourseTranslatesData(Course $course): array
    {
        $data = [];
        $coursesTranslations = $course->getTranslations();

        foreach ($coursesTranslations as $courseTranslation) {
            $data[] = [
                'id' => $courseTranslation->getId(),
                'name' => $courseTranslation->getName(),
                'typeCourse' => $courseTranslation->getTypeCourse()->__toString(),
                'category' => $courseTranslation->getCategory()->getName(),
                'open' => $courseTranslation->getOpen(),
                'totalChapters' => $courseTranslation->getTotalChapter(),
                'thumbnailUrl' => $courseTranslation->getThumbnailUrl(),
                'locale' => $courseTranslation->getLocale(),
                'code' => $courseTranslation->getCode(),
                'active' => $courseTranslation->getActive(),
                'createdAt' => $courseTranslation->getCreatedAt()->format('d/m/Y H:i'),
            ];
        }

        return $data;
    }

    public function getAllCreatorsCourses(): array
    {
        $result = [];
        $creatorsCourses = $this->em->getRepository(Course::class)->findAllCreatorsCourses();
        foreach ($creatorsCourses as $creatorCourseId) {
            $user = $this->em->getRepository(User::class)->find($creatorCourseId);
            if ($user) {
                $element = new \stdClass();
                $element->id = $user->getId();
                $element->name = $user->getFullName();

                $result[] = $element;
            }
        }

        return $result;
    }

    private function getTranslationsByCourse(Course $course): array
    {
        $languages = [];
        $coursesTranslations = $course->getTranslations();

        foreach ($coursesTranslations as $courseTranslation) {
            $languages[] = [
                'locale' => $courseTranslation->getLocale(),
                'idCourse' => $courseTranslation->getId(),
                'nameCourse' => $courseTranslation->getName(),
                'published' => $courseTranslation->getActive(),
            ];
        }

        return $languages;
    }

    public function getParameters(Request $request): array
    {
        $content = [];
        $conditions = [];

        if (!empty($request->get('page'))) {
            $content['page'] = (int) $request->get('page');
        }

        $content['sortBy'] = $request->get('sortBy') ?? '';
        $content['sortDir'] = $request->get('sortDir') ?? '';

        $arrayKeys = ['category', 'typeCourse', 'createdBy'];
        foreach ($arrayKeys as $key) {
            if ($value = $request->get($key)) {
                $valCondition = explode(',', $value)[0];
                $conditions[$key] = $valCondition;
            }
        }

        $directKeys = [
            'createdAt', 'dateTo', 'open', 'open_visible', 'active',
            'isNew', 'search', 'locale', 'id', 'name', 'code',
        ];
        foreach ($directKeys as $key) {
            if ($value = $request->get($key)) {
                $conditions[$key] = $value;
            }
        }

        $content['conditions'] = $conditions ?? [];

        return $content;
    }

    public function getChapters(Course $course, string $url): array
    {
        $chapters = [];

        foreach ($course->getChapters() as $chapter) {
            $chapters[] = array_merge(
                [
                    'id' => $chapter->getId(),
                    'name' => $chapter->getTitle(),
                    'image' => $chapter->getImage()
                        ? ($this->settings->get('app.chapter_uploads_path') . '/' . $chapter->getImage())
                        : null,
                    'thumbnail-url' => $chapter->getThumbnailUrl(),
                    'description' => $chapter->getDescription(),
                    'typeId' => $chapter->getType()->getId(),
                    'typeName' => $chapter->getType()->getName(),
                    'order' => $chapter->getPosition(),
                    'seasonId' => $chapter->getSeason()->getId(),
                    'seasonName' => $chapter->getSeason()->getName(),
                    'seasonOrder' => $chapter->getSeason()->getSort(),
                    'icon' => $chapter->getType()->getIcon(),
                    'hasContentCompleted' => $chapter->hasContentCompleted(),
                    'editUrl' => $this->adminUrlGenerator
                        ->unsetAll()
                        ->setController('App\\Controller\\Admin\\ChapterCrudController')
                        ->setAction('edit')
                        ->setEntityId($chapter->getId())
                        ->set('referrer', $url)
                        ->generateUrl(),
                ],
                $chapter->getType()->isVcms() && null !== $chapter->getVcmsProject()
                    ? [
                        'projectEditUrl' => $chapter->getVirtualProjectEdit(),
                        'projectViewUrl' => $chapter->getVirtualProjectView(),
                    ]
                    : [],
                $chapter->getType()->isRoleplay() && null !== $chapter->getRoleplayProject()
                    ? [
                        'projectEditUrl' => $chapter->getRoleplayProjectEdit(),
                        'projectViewUrl' => $chapter->getRoleplayProjectView(),
                    ]
                    : [],
            );
        }

        return $chapters;
    }

    public function getSeason(Course $course): array
    {
        $seasons = [];

        foreach ($course->getSeasons() as $season) {
            $seasons[] = [
                'id' => $season->getId(),
                'name' => $season->getName(),
                'sort' => $season->getSort(),
                'type' => $season->getType(),
            ];
        }

        return $seasons;
    }

    public function createCourseSeasons(Request $request, Course $course): void
    {
        $season = new Season();
        $season->setCourse($course)
            ->setType($request->get('type'))
            ->setName($request->get('name'))
            ->setSort($this->em->getRepository(Season::class)->getNextSortByCourse($course));
        $this->em->persist($season);
        $this->em->flush();
    }

    public function editCourseSeasons(Season $season, array $content): void
    {
        $season->setName($content['name'])
            ->setType($content['type']);
        $this->em->persist($season);
        $this->em->flush();
    }

    public function getCourseAnnouncements(Course $course, User $user): array
    {
        $announcements = $this->em->getRepository(Announcement::class)
            ->findAnnouncementsByCourseForUser(course: $course, user: $user);

        return array_map(static function (Announcement $announcement): array {
            return [
                'id' => $announcement->getId(),
                'code' => $announcement->getCode(),
                'startAt' => $announcement->getStartAt()?->format('c'),
                'finishAt' => $announcement->getFinishAt()?->format('c'),
            ];
        }, $announcements);
    }

    /**
     * @throws InfrastructureException
     */
    public function getRestrictedCourseIdsForAccessByUserRole(User $user): array
    {
        $restrictedCourseIds = [];

        if ($user->isAdmin()) {
            return $restrictedCourseIds;
        }

        if ($user->isManager()) {
            $coursesManager = $this->em->getRepository(Course::class)->getCoursesManager($user);

            if (\count($coursesManager) <= 0) {
                return $restrictedCourseIds;
            }

            foreach ($coursesManager as $courseManager) {
                $restrictedCourseIds[] = $courseManager->getId();
            }
        }

        if ($user->isCreator()) {
            $restrictedCourseIds = array_merge(
                $restrictedCourseIds,
                $this->getCreatorCourseIds($user),
                $this->getSharedCreatorCourseIds($user),
            );
        }

        return array_unique($restrictedCourseIds);
    }

    private function getCreatorCourseIds(User $user): array
    {
        $courses = $this->em->getRepository(Course::class)->findBy(['createdBy' => $user]);

        $courseIds = [];
        foreach ($courses as $course) {
            $courseIds[] = $course->getId();
        }

        return $courseIds;
    }
}
