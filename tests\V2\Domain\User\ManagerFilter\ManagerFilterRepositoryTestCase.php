<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User\ManagerFilter;

use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\ManagerFilterNotFoundException;
use App\V2\Domain\User\Exception\ManagerFilterRepositoryException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use PHPUnit\Framework\TestCase;

abstract class ManagerFilterRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): ManagerFilterRepository;

    /**
     * @throws ManagerFilterRepositoryException
     * @throws InfrastructureException
     */
    public function testInsert(): void
    {
        $user1Filter1 = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = ManagerFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = ManagerFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );
        $duplicate = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $result = $repository->findBy(ManagerFilterCriteria::createEmpty());

        $this->assertCount(4, $result);

        try {
            $repository->insert($duplicate);
            $this->fail('Expected an exception to be thrown.');
        } catch (ManagerFilterRepositoryException $e) {
            $this->assertEquals(ManagerFilterRepositoryException::duplicateFilter(
                userId: new Id(1),
                filterId: new Id(1)
            ), $e);
        }
    }

    /**
     * @throws ManagerFilterNotFoundException
     * @throws ManagerFilterRepositoryException
     * @throws InfrastructureException
     */
    public function testFindOneBy(): void
    {
        $user1Filter1 = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = ManagerFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = ManagerFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $result = $repository->findOneBy(ManagerFilterCriteria::createEmpty()
            ->filterByUserId(new Id(1))
            ->filterByFilterId(new Id(1)));
        $this->assertEquals(
            $user1Filter1,
            $result
        );
        $this->assertNotSame($user1Filter1, $result);

        $result = $repository->findOneBy(ManagerFilterCriteria::createEmpty()
            ->filterByUserId(new Id(2)));
        $this->assertEquals(
            $user2Filter1,
            $result
        );
        $this->assertNotSame($user2Filter1, $result);

        $result = $repository->findOneBy(ManagerFilterCriteria::createEmpty()
            ->filterByFilterId(new Id(2)));
        $this->assertEquals(
            $user1Filter2,
            $result
        );
        $this->assertNotSame($user1Filter2, $result);

        try {
            $repository->findOneBy(ManagerFilterCriteria::createEmpty()->filterByUserId(new Id(3)));
            $this->fail('Expected an exception to be thrown.');
        } catch (ManagerFilterNotFoundException $e) {
            $this->assertEquals(new ManagerFilterNotFoundException(), $e);
        }

        try {
            $repository->findOneBy(ManagerFilterCriteria::createEmpty()->filterByFilterId(new Id(3)));
            $this->fail('Expected an exception to be thrown.');
        } catch (ManagerFilterNotFoundException $e) {
            $this->assertEquals(new ManagerFilterNotFoundException(), $e);
        }
    }

    /**
     * @throws ManagerFilterRepositoryException
     * @throws InfrastructureException
     * @throws CollectionException
     */
    public function testFindBy(): void
    {
        $filterId1 = new Id(1);
        $filterId2 = new Id(2);
        $userId1 = new Id(1);
        $userId2 = new Id(2);
        $user1Filter1 = ManagerFilterMother::create(
            userId: $userId1,
            filterId: $filterId1
        );
        $user1Filter2 = ManagerFilterMother::create(
            userId: $userId1,
            filterId: $filterId2
        );
        $user2Filter1 = ManagerFilterMother::create(
            userId: $userId2,
            filterId: $filterId1
        );
        $user2Filter2 = ManagerFilterMother::create(
            userId: $userId2,
            filterId: $filterId2
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $result = $repository->findBy(ManagerFilterCriteria::createEmpty());
        $this->assertCount(4, $result);

        $result = $repository->findBy(ManagerFilterCriteria::createEmpty()
            ->filterByUserId(new Id(1)));
        $this->assertCount(2, $result);
        $this->assertCount(0, array_diff([$user1Filter1, $user1Filter2], $result->all()));

        $result = $repository->findBy(ManagerFilterCriteria::createEmpty()
            ->filterByUserId(new Id(2)));
        $this->assertCount(2, $result);
        $this->assertCount(0, array_diff([$user2Filter1, $user2Filter2], $result->all()));

        $result = $repository->findBy(ManagerFilterCriteria::createEmpty()
            ->filterByUserId(new Id(1))
            ->filterByFilterId(new Id(1)));
        $this->assertCount(1, $result);
        $this->assertEquals($user1Filter1, $result->first());
        $this->assertNotSame($user1Filter1, $result->first());

        $result = $repository->findBy(
            ManagerFilterCriteria::createEmpty()
                ->filterByFilterIds(new IdCollection([$filterId1, $filterId2]))
        );
        $this->assertCount(4, $result);
        $this->assertCount(
            0,
            array_diff([$user1Filter1, $user1Filter2, $user2Filter1, $user2Filter2], $result->all())
        );

        $result = $repository->findBy(
            ManagerFilterCriteria::createEmpty()
                ->filterByFilterIds(new IdCollection([$filterId1]))
        );
        $this->assertCount(2, $result);
        $this->assertCount(
            0,
            array_diff([$user2Filter1, $user1Filter1], $result->all())
        );
    }

    /**
     * @throws ManagerFilterRepositoryException
     * @throws InfrastructureException
     */
    public function testDelete(): void
    {
        $user1Filter1 = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = ManagerFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = ManagerFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = ManagerFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $this->assertCount(4, $repository->findBy(ManagerFilterCriteria::createEmpty()));

        $repository->delete($user1Filter1);
        $result = $repository->findBy(ManagerFilterCriteria::createEmpty());
        $this->assertCount(3, $result);
        $this->assertCount(0, array_diff([$user1Filter2, $user2Filter1, $user2Filter2], $result->all()));
    }
}
