<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Filter;

use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterCollection;

class FilterTransformer
{
    public static function toArray(Filter $filter): array
    {
        return [
            'id' => $filter->getId()->value(),
            'name' => $filter->getName(),
        ];
    }

    public static function fromCollectionToArray(FilterCollection $collection): array
    {
        return array_map(
            fn (Filter $filter) => self::toArray($filter),
            $collection->all(),
        );
    }
}
