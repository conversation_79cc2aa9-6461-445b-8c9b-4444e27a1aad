import { CategoryFilterOptionsModel } from '@/contexts/shared/models/categoryFilterOptions.model.js'

export class CategoryFilterModel {
  constructor({ categories = [], filters = [], autoUpdate = true, showHeaderButtons = true } = {}) {
    this.setCategoryList(categories)
    this.setFilterList(filters)
    this.setDisableValue()
    this.setLoadingFilters()
    this.showHeaderButtons = showHeaderButtons || false
    this.autoUpdate = autoUpdate || false
  }

  setFilterList(filters = []) {
    this.filterList = (filters || []).map((filter) => new CategoryFilterOptionsModel(filter))
    return this
  }

  setCategoryList(categories = []) {
    this.categoryList = (categories || []).map((category) => new CategoryFilterOptionsModel(category))
    if (this.categoryList.length) this.setActiveCategory(this.categoryList[0])
    return this
  }

  setActiveCategory(category) {
    category.updateSelected(true)
  }

  setDisableValue(value = false) {
    this.disabled = value
    return this
  }

  setLoadingFilters(value = false) {
    this.loading = value
    return this
  }

  updateFiltersById(filters = [], isAdding = true) {
    if (this.autoUpdate) {
      filters.forEach((filter) => {
        filter.setUpdateStatus(false)
        filter.updateSelected(isAdding)
      })
    }
  }

  getPayload() {
    const data = {
      available: [],
      selected: [],
    }
    this.filterList.forEach((filter) => {
      if (filter.selected) {
        data.selected.push(filter.id)
      } else {
        data.available.push(filter.id)
      }
    })
    return data
  }
}
