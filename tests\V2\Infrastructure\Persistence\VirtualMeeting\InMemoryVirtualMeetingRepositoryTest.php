<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\VirtualMeeting;

use App\Tests\V2\Domain\VirtualMeeting\VirtualMeetingRepositoryTestCase;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Infrastructure\Persistence\VirtualMeeting\InMemoryVirtualMeetingRepository;

class InMemoryVirtualMeetingRepositoryTest extends VirtualMeetingRepositoryTestCase
{
    protected function getRepository(): VirtualMeetingRepository
    {
        return new InMemoryVirtualMeetingRepository();
    }
}
