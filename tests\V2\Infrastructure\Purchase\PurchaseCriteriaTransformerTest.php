<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase;

use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Pagination;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Purchase\PurchaseCriteriaTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PurchaseCriteriaTransformerTest extends TestCase
{
    /**
     * @throws InvalidCurrencyCodeException
     * @throws CollectionException
     */
    #[DataProvider('criteriaProvider')]
    public function testFromArray(array $data, PurchaseCriteria $expectedCriteria): void
    {
        $result = PurchaseCriteriaTransformer::fromArray($data);

        $this->assertEquals($expectedCriteria->getUserId(), $result->getUserId());
    }

    /**
     * @throws CollectionException
     */
    #[DataProvider('invalidCurrencyProvider')]
    public function testInvalidCurrencyThrowsException(array $inputData): void
    {
        $this->expectException(InvalidCurrencyCodeException::class);

        PurchaseCriteriaTransformer::fromArray($inputData);
    }

    public static function invalidCurrencyProvider(): \Generator
    {
        yield 'invalid currency in min amount' => [
            'inputData' => [
                'amount_min' => '1000',
                'amount_currency' => 'GBP', // Not supported
            ],
        ];

        yield 'invalid currency in max amount' => [
            'inputData' => [
                'amount_max' => '5000',
                'amount_currency' => 'JPY', // Not supported
            ],
        ];
    }

    /**
     * @throws CollectionException
     */
    public static function criteriaProvider(): \Generator
    {
        // Basic cases
        yield 'empty data' => [
            'data' => [],
            'expectedCriteria' => PurchaseCriteria::createEmpty(),
        ];

        yield 'with user_id only' => [
            'data' => ['user_id' => '123'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByUserId(new Id(123)),
        ];

        yield 'with status only' => [
            'data' => ['status' => 'completed'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByStatus(PurchaseStatus::Completed),
        ];

        yield 'with min amount only' => [
            'data' => ['amount_min' => '1500', 'amount_currency' => 'EUR'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 1500)),
        ];

        yield 'with max amount only' => [
            'data' => ['amount_max' => '4500', 'amount_currency' => 'EUR'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByMaxAmount(MoneyMother::create(amount: 4500)),
        ];

        yield 'with start date only' => [
            'data' => ['start_date' => '2023-01-01 00:00:00'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByStartDate(new \DateTimeImmutable('2023-01-01 00:00:00')),
        ];

        yield 'with end date only' => [
            'data' => ['end_date' => '2023-12-31 23:59:59'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByEndDate(new \DateTimeImmutable('2023-12-31 23:59:59')),
        ];

        // Edge cases
        yield 'zero amount values' => [
            'data' => ['amount_min' => '0', 'amount_max' => '0', 'amount_currency' => 'EUR'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 0))
                ->filterByMaxAmount(MoneyMother::create(amount: 0)),
        ];

        yield 'large amount values' => [
            'data' => ['amount_min' => '999999', 'amount_max' => '9999999', 'amount_currency' => 'EUR'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 999999))
                ->filterByMaxAmount(MoneyMother::create(amount: 9999999)),
        ];

        // Pagination cases
        yield 'with pagination' => [
            'data' => [
                'page' => '2',
                'page_size' => '10',
            ],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->withPagination(new Pagination(2, 10)),
        ];

        // Sorting cases
        yield 'with sorting' => [
            'data' => [
                'sort_by' => 'created_at',
                'sort_dir' => 'desc',
            ],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->sortBy(new SortCollection([
                    new Sort(
                        new SortableField('createdAt'),
                        SortDirection::DESC
                    ),
                ])),
        ];

        // Integration with MoneyTransformer
        yield 'money transformer integration' => [
            'data' => [
                'amount_min' => '1500',
                'amount_max' => '3000',
                'amount_currency' => 'EUR',
            ],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 1500))
                ->filterByMaxAmount(MoneyMother::create(amount: 3000)),
        ];

        // Combined filters
        yield 'with amount range' => [
            'data' => ['amount_min' => '2000', 'amount_max' => '6000', 'amount_currency' => 'EUR'],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 2000))
                ->filterByMaxAmount(MoneyMother::create(amount: 6000)),
        ];

        yield 'with date range' => [
            'data' => [
                'start_date' => '2023-01-01 00:00:00',
                'end_date' => '2023-12-31 23:59:59',
            ],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByStartDate(new \DateTimeImmutable('2023-01-01 00:00:00'))
                ->filterByEndDate(new \DateTimeImmutable('2023-12-31 23:59:59')),
        ];

        yield 'user_id and status filters' => [
            'data' => [
                'user_id' => '456',
                'status' => 'pending',
            ],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByUserId(new Id(456))
                ->filterByStatus(PurchaseStatus::Pending),
        ];

        // All filters combined
        yield 'all filters combined' => [
            'data' => [
                'user_id' => '123',
                'status' => 'completed',
                'amount_min' => '2000',
                'amount_max' => '8000',
                'amount_currency' => 'EUR',
                'start_date' => '2023-01-01 00:00:00',
                'end_date' => '2023-12-31 23:59:59',
                'sort_by' => 'created_at',
                'sort_dir' => 'desc',
                'page' => '1',
                'page_size' => '20',
            ],
            'expectedCriteria' => PurchaseCriteria::createEmpty()
                ->filterByUserId(new Id(123))
                ->filterByStatus(PurchaseStatus::Completed)
                ->filterByMinAmount(MoneyMother::create(amount: 2000))
                ->filterByMaxAmount(MoneyMother::create(amount: 8000))
                ->filterByStartDate(new \DateTimeImmutable('2023-01-01 00:00:00'))
                ->filterByEndDate(new \DateTimeImmutable('2023-12-31 23:59:59'))
                ->sortBy(new SortCollection([
                    new Sort(
                        new SortableField('createdAt'),
                        SortDirection::DESC
                    ),
                ]))
                ->withPagination(new Pagination(1, 20)),
        ];
    }
}
