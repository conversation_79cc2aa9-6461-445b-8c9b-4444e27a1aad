<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;

readonly class DeleteManagerFiltersCommand implements Command
{
    public function __construct(
        private Id $userId,
        private IdCollection $filterIds,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getFilterIds(): IdCollection
    {
        return $this->filterIds;
    }
}
