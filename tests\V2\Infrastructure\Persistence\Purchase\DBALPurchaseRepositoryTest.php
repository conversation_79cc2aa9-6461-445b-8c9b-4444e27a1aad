<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Purchase;

use App\Tests\V2\Domain\Purchase\PurchaseRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Infrastructure\Persistence\Purchase\DBALPurchaseRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALPurchaseRepositoryTest extends PurchaseRepositoryTestCase
{
    private const string TABLE_NAME = 'purchase';
    private const string PURCHASE_ITEM_TABLE_NAME = 'purchase_item';
    private Connection $connection;

    /**
     * @throws Exception
     * @throws SchemaException
     */
    protected function getRepository(): PurchaseRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();
        $this->createPurchaseItemTable();

        return new DBALPurchaseRepository(
            $this->connection,
            self::TABLE_NAME,
            self::PURCHASE_ITEM_TABLE_NAME
        );
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string', ['length' => 36]);
        $table->addColumn('user_id', 'string', ['length' => 36]);
        $table->addColumn('status', 'string', ['length' => 50]);
        $table->addColumn('amount', 'integer');
        $table->addColumn('currency_code', 'string', ['length' => 3]);
        $table->addColumn('tax_rate', 'float');
        $table->addColumn('tax_amount', 'integer');
        $table->addColumn('created_at', 'datetime');
        $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
        $table->addColumn('deleted_at', 'datetime', ['notnull' => false]);
        $table->setPrimaryKey(['id']);
        $table->addIndex(['user_id'], 'idx_purchase_user_id');
        $table->addIndex(['status'], 'idx_purchase_status');
        $table->addIndex(['created_at'], 'idx_purchase_created_at');

        $this->connection->createSchemaManager()->createTable($table);
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createPurchaseItemTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::PURCHASE_ITEM_TABLE_NAME);
        $table->addColumn('id', 'string', ['length' => 36]);
        $table->addColumn('purchase_id', 'string', ['length' => 36]);
        $table->addColumn('purchasable_item_id', 'string', ['length' => 36]);
        $table->addColumn('price_amount', 'integer');
        $table->addColumn('price_currency', 'string', ['length' => 3]);
        $table->setPrimaryKey(['id']);
        $table->addIndex(['purchase_id'], 'idx_purchase_item_purchase_id');
        $table->addIndex(['purchasable_item_id'], 'idx_purchase_item_purchasable_item_id');

        $this->connection->createSchemaManager()->createTable($table);
    }
}
