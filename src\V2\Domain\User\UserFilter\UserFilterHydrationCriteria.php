<?php

declare(strict_types=1);

namespace App\V2\Domain\User\UserFilter;

use App\V2\Domain\Shared\Hydrator\HydrationCriteria;

class UserFilterHydrationCriteria extends HydrationCriteria
{
    private bool $withFilters = false;

    public function isEmpty(): bool
    {
        return false === $this->withFilters;
    }

    public function withFilters(): self
    {
        $this->withFilters = true;

        return $this;
    }

    public function needsFilters(): bool
    {
        return $this->withFilters;
    }
}
