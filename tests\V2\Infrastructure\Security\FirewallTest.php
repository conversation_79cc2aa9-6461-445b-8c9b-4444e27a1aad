<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Security;

use App\V2\Infrastructure\Security\Firewall;
use App\V2\Infrastructure\Service\RouteMethodChecker;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;

class FirewallTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getFirewall(?RouteMethodChecker $checker = null): Firewall
    {
        return new Firewall(
            [
                'prefix' => '/v2',
                'access_control' => [
                    [
                        'path' => '^/v2/health-check',
                        'roles' => 'PUBLIC_ACCESS',
                    ],
                    [
                        'path' => '^/v2/user-public',
                        'roles' => 'PUBLIC_ACCESS',
                    ],
                    [
                        'path' => '^/v2/only-options',
                        'roles' => 'IS_AUTHENTICATED_FULLY',
                    ],
                    [
                        'path' => '^/v2/test-forbidden',
                        'roles' => 'IS_AUTHENTICATED_FULLY',
                    ],
                ],
            ],
            $checker ?? $this->createMock(RouteMethodChecker::class)
        );
    }

    /**
     * @throws Exception
     */
    private function getFirewallWithOverlappingRules(
        ?RouteMethodChecker $checker = null,
        array $roleHierarchy = []
    ): Firewall {
        return new Firewall(
            security: [
                'prefix' => '/v2',
                'access_control' => [
                    // Specific rule that allows ROLE_CREATOR
                    [
                        'path' => '^/v2/admin/courses/[\w-]+/creators/[\w-]+',
                        'roles' => ['ROLE_ADMIN', 'ROLE_CREATOR'],
                    ],
                    [
                        'path' => '^/v2/admin/test/role-hierarchy',
                        'roles' => ['ROLE_CREATOR'],
                    ],
                    // General rule that would deny ROLE_CREATOR
                    [
                        'path' => '^/v2/admin',
                        'roles' => ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
                    ],
                ],
            ],
            routeChecker: $checker ?? $this->createMock(RouteMethodChecker::class),
            roleHierarchy: $roleHierarchy,
        );
    }

    /**
     * @throws Exception
     */
    public function testPublicEndpoint()
    {
        $firewall = $this->getFirewall();

        $this->assertSame(
            true,
            $firewall->isGranted(
                new Request([], [], [], [], [], [
                    'REQUEST_URI' => '/v2/health-check',
                ], null),
                null
            )
        );

        $this->assertSame(
            true,
            $firewall->isGranted(
                new Request([], [], [], [], [], [
                    'REQUEST_URI' => '/v2/user-public/1/users',
                ], null),
                null
            )
        );
    }

    /**
     * @throws Exception
     */
    public function testNoTokenAndNoPublicEndpoint()
    {
        $request = new Request(
            server: [
                'REQUEST_URI' => '/v2/test-forbidden',
            ]
        );

        $this->expectExceptionObject(new UnauthorizedHttpException('Unauthorized'));
        $result = $this->getFirewall()->isGranted($request);
    }

    /**
     * Test that overlapping rules work correctly - access is granted if ANY rule allows it.
     *
     * @throws Exception
     */
    public function testOverlappingRulesAllowAccess(): void
    {
        $firewall = $this->getFirewallWithOverlappingRules();

        // Mock user with ROLE_CREATOR
        $user = $this->createMock(\Symfony\Component\Security\Core\User\UserInterface::class);
        $user->method('getRoles')->willReturn(['ROLE_USER', 'ROLE_CREATOR']);

        // Request to specific endpoint that should be allowed by the second rule
        $request = new Request(
            server: [
                'REQUEST_URI' => '/v2/admin/courses/123/creators/456',
            ]
        );

        // Should be granted because the specific rule allows ROLE_CREATOR
        // even though the general rule would deny it
        $result = $firewall->isGranted($request, $user);
        $this->assertTrue($result);
    }

    /**
     * Test that overlapping rules deny access when no rule allows it.
     *
     * @throws Exception
     */
    public function testOverlappingRulesDenyAccess(): void
    {
        $firewall = $this->getFirewallWithOverlappingRules();

        // Mock user with ROLE_USER only (not allowed by any rule)
        $user = $this->createMock(\Symfony\Component\Security\Core\User\UserInterface::class);
        $user->method('getRoles')->willReturn(['ROLE_USER']);

        // Request to specific endpoint
        $request = new Request(
            server: [
                'REQUEST_URI' => '/v2/admin/courses/123/creators/456',
            ]
        );

        // Should be denied because no rule allows ROLE_USER
        $result = $firewall->isGranted($request, $user);
        $this->assertFalse($result);
    }

    /**
     * @throws Exception
     */
    public function testOptionsRequest()
    {
        $checker = $this->createMock(RouteMethodChecker::class);
        $checker
            ->method('isOptionsOnlyRoute')
            ->willReturnCallback(fn (string $path) => '/v2/only-options' === $path);

        $firewall = $this->getFirewall($checker);

        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/v2/test-forbidden',
            'REQUEST_METHOD' => 'OPTIONS',
        ], null);

        $result = $firewall->isGranted($request, null);
        $this->assertSame(true, $result);

        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/v2/only-options',
            'REQUEST_METHOD' => 'OPTIONS',
        ], null);

        $this->expectExceptionObject(new RouteNotFoundException('No route found for "OPTIONS /v2/only-options"'));
        $firewall->isGranted($request, null);
    }

    public function testOptionsRequestWithNoPrefix()
    {
        $checker = $this->createMock(RouteMethodChecker::class);
        $checker
            ->method('isOptionsOnlyRoute')
            ->willReturnCallback(fn (string $path) => '/v1/only-options' === $path);

        $firewall = $this->getFirewall($checker);

        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/v1/test-forbidden',
            'REQUEST_METHOD' => 'OPTIONS',
        ], null);

        $result = $firewall->isGranted($request, null);
        $this->assertSame(true, $result);
    }

    public function testOptionsRequestWithNoPrefixAndNoOptionsOnlyRoute()
    {
        $checker = $this->createMock(RouteMethodChecker::class);
        $checker
            ->method('isOptionsOnlyRoute')
            ->willReturnCallback(fn (string $path) => false);

        $firewall = $this->getFirewall($checker);

        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/v1/test-forbidden',
            'REQUEST_METHOD' => 'OPTIONS',
        ], null);

        $result = $firewall->isGranted($request, null);
        $this->assertSame(true, $result);
    }

    /**
     * @throws Exception
     */
    public function testNoPrefix()
    {
        $firewall = $this->getFirewall();

        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/v1/test-forbidden',
        ], null);

        $result = $firewall->isGranted($request, null);
        $this->assertSame(true, $result);
    }

    #[DataProvider('provideTestRoleHierarchy')]
    public function testRoleHierarchy(array $userRoles, bool $isGranted): void
    {
        $firewall = $this->getFirewallWithOverlappingRules(
            roleHierarchy: [
                'ROLE_SUPER_ADMIN' => [
                    'ROLE_ADMIN',
                ],
                'ROLE_ADMIN' => [
                    'ROLE_CREATOR',
                    'ROLE_USER',
                ],
                'ROLE_CREATOR' => [
                    'ROLE_USER',
                ],
            ]
        );

        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/v2/admin/test/role-hierarchy',
        ], null);

        $user = $this->createMock(\Symfony\Component\Security\Core\User\UserInterface::class);
        $user->method('getRoles')
            ->willReturn($userRoles);
        $this->assertSame($isGranted, $firewall->isGranted($request, $user));
    }

    public static function provideTestRoleHierarchy(): \Generator
    {
        yield 'role creator' => [
            'userRoles' => ['ROLE_CREATOR'],
            'isGranted' => true,
        ];

        yield 'role user' => [
            'userRoles' => ['ROLE_USER'],
            'isGranted' => false,
        ];

        yield 'role admin' => [
            'userRoles' => ['ROLE_ADMIN'],
            'isGranted' => true,
        ];

        yield 'role superadmin' => [
            'userRoles' => ['ROLE_SUPER_ADMIN'],
            'isGranted' => true,
        ];
    }
}
