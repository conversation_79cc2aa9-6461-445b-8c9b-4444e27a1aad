<template>
  <div class="CategoryFilters">
    <header v-if="options.categoryList.length">
      <CategoryItem
        v-for="category in options.categoryList"
        :key="category.key"
        :item="category"
        :disabled="options.disabled"
        @set-active="updateActive(category)"
      />
    </header>
    <BaseSpinner v-if="options.loading" />
    <main v-else>
      <FilterListContainer
        :filter-list="listGroup[0]"
        :disabled="options.disabled"
        :show-header-buttons="options.showHeaderButtons"
        @update="addItem"
        @update-all="addAll"
      />
      <FilterListContainer
        type="remove"
        :filter-list="listGroup[1]"
        :disabled="options.disabled"
        :show-header-buttons="options.showHeaderButtons"
        @update="removeItem"
        @update-all="removeAll"
      />
    </main>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import FilterListContainer from '@/contexts/shared/components/CategoryFilter/FilterListContainer.vue'
import CategoryItem from '@/contexts/shared/components/CategoryFilter/CategoryItem.vue'
import BaseSpinner from '@/contexts/shared/components/BaseSpinner.vue'
import { CategoryFilterModel } from '@/contexts/shared/models/categoryFilter.model.js'

const emit = defineEmits(['setCategory', 'add', 'remove', 'addAll', 'removeAll'])
const props = defineProps({
  options: { type: [CategoryFilterModel, Object], default: () => ({}) },
})

const listGroup = computed(() => {
  const data = [[], []]
  props.options.filterList.forEach((filter) => {
    data[filter.selected ? 1 : 0].push(filter)
  })
  return data
})

function updateActive(category) {
  if (props.options.disabled) {
    return null
  }
  emit('setCategory', category)
  props.options.categoryList.forEach((item) => item.updateSelected(item.id === category.id))
}

function addItem(item) {
  item.setUpdateStatus(true)
  emit('add', [item.id])
  props.options.updateFiltersById([item], true)
}

function removeItem(item) {
  item.setUpdateStatus(true)
  emit('remove', [item.id])
  props.options.updateFiltersById([item], false)
}

function addAll(itemList = []) {
  emit(
    'addAll',
    itemList.map((item) => {
      item.setUpdateStatus(true)
      return item.id
    })
  )
  props.options.updateFiltersById(itemList, true)
}

function removeAll(itemList = []) {
  const idList = itemList.map((item) => {
    item.setUpdateStatus(true)
    return item.id
  })
  emit('removeAll', idList)
  props.options.updateFiltersById(itemList, false)
}
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.CategoryFilters {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  header {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  main {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem 2rem;

    @media #{breakpoint.$breakpoint-md} {
      grid-template-columns: 1fr;
      grid-template-rows: 300px 300px;

      .FilterListContainer {
        overflow-y: auto;

        &:deep(.fa-angle-right) {
          transform: rotate(90deg);
        }

        &:deep(.fa-angle-left) {
          transform: rotate(90deg);
        }
      }
    }

    @media #{breakpoint.$breakpoint-xs} {
      grid-template-rows: 38svh 38svh;
    }
  }
}
</style>
