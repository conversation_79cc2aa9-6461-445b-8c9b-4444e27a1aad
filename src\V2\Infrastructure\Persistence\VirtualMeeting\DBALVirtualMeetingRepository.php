<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\VirtualMeeting;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingRepositoryException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use App\V2\Infrastructure\Persistence\DBALDateTimeFormatter;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALVirtualMeetingRepository implements VirtualMeetingRepository
{
    public function __construct(
        private Connection $connection,
        private string $virtualMeetingTableName,
    ) {
    }

    #[\Override]
    public function put(VirtualMeeting $virtualMeeting): void
    {
        try {
            $this->findOneBy(
                VirtualMeetingCriteria::createById($virtualMeeting->getId())
            );

            $this->update($virtualMeeting);
        } catch (VirtualMeetingNotFoundException) {
            $this->insert($virtualMeeting);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function insert(VirtualMeeting $virtualMeeting): void
    {
        try {
            $this->connection->insert(
                table: $this->virtualMeetingTableName,
                data: $this->fromVirtualMeetingToArray($virtualMeeting),
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function update(VirtualMeeting $virtualMeeting): void
    {
        try {
            $data = $this->fromVirtualMeetingToArray($virtualMeeting);

            $this->connection->update(
                table: $this->virtualMeetingTableName,
                data: $data,
                criteria: ['id' => $virtualMeeting->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(VirtualMeetingCriteria $criteria): VirtualMeeting
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->setMaxResults(1)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new VirtualMeetingNotFoundException();
            }

            return $this->fromArrayToVirtualMeeting($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(VirtualMeetingCriteria $criteria): VirtualMeetingCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new VirtualMeetingCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToVirtualMeeting($values),
                    $result
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    public function countBy(VirtualMeetingCriteria $criteria): int
    {
        try {
            return $this->getQueryBuilderByCriteria($criteria)
                ->select('COUNT(*)')
                ->executeQuery()
                ->fetchOne();
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(VirtualMeeting $virtualMeeting): void
    {
        try {
            // Soft delete - update the deleted_at field
            $result = $this->connection->update(
                $this->virtualMeetingTableName,
                [
                    'deleted_at' => (new \DateTimeImmutable())->format('Y-m-d H:i:s'),
                ],
                ['id' => $virtualMeeting->getId()->value()]
            );

            if (0 === $result) {
                throw new VirtualMeetingNotFoundException();
            }
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws VirtualMeetingRepositoryException
     */
    private function fromVirtualMeetingToArray(VirtualMeeting $virtualMeeting): array
    {
        $data = [
            'id' => $virtualMeeting->getId()->value(),
            'type' => $this->fromVirtualMeetingTypeToString($virtualMeeting->getType()),
            'start_at' => DBALDateTimeFormatter::format($virtualMeeting->getStartAt()),
            'finish_at' => DBALDateTimeFormatter::format($virtualMeeting->getFinishAt()),
            'url' => $virtualMeeting->getUrl(),
            'created_at' => DBALDateTimeFormatter::format($virtualMeeting->getCreatedAt()),
        ];

        if (null !== $virtualMeeting->getUpdatedAt()) {
            $data['updated_at'] = DBALDateTimeFormatter::format($virtualMeeting->getUpdatedAt());
        }

        if (null !== $virtualMeeting->getDeletedAt()) {
            $data['deleted_at'] = DBALDateTimeFormatter::format($virtualMeeting->getDeletedAt());
        }

        return $data;
    }

    /**
     * @throws VirtualMeetingRepositoryException
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     */
    private function fromArrayToVirtualMeeting(array $values): VirtualMeeting
    {
        return new VirtualMeeting(
            id: new Uuid($values['id']),
            type: $this->fromStringToVirtualMeetingType($values['type']),
            startAt: DBALDateTimeFormatter::parse($values['start_at']),
            finishAt: DBALDateTimeFormatter::parse($values['finish_at']),
            url: $values['url'],
            createdAt: DBALDateTimeFormatter::parse($values['created_at']),
            updatedAt: isset($values['updated_at'])
                ? DBALDateTimeFormatter::parse($values['updated_at'])
                : null,
            deletedAt: isset($values['deleted_at'])
                ? DBALDateTimeFormatter::parse($values['deleted_at'])
                : null
        );
    }

    /**
     * @throws VirtualMeetingRepositoryException
     */
    private function getQueryBuilderByCriteria(VirtualMeetingCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->virtualMeetingTableName, 't');

        // Apply common criteria (ID, IDs, pagination, sorting)
        DBALCommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb);

        // Filter by type
        if (null !== $criteria->getType()) {
            $qb->andWhere('t.type = :type')
                ->setParameter('type', $this->fromVirtualMeetingTypeToString($criteria->getType()));
        }

        // Filter by startAt range
        if (null !== $criteria->getStartAtFrom()) {
            $qb->andWhere('t.start_at >= :start_at_from')
                ->setParameter('start_at_from', DBALDateTimeFormatter::format($criteria->getStartAtFrom()));
        }

        if (null !== $criteria->getStartAtTo()) {
            $qb->andWhere('t.start_at <= :start_at_to')
                ->setParameter('start_at_to', DBALDateTimeFormatter::format($criteria->getStartAtTo()));
        }

        // Filter by finishAt range
        if (null !== $criteria->getFinishAtFrom()) {
            $qb->andWhere('t.finish_at >= :finish_at_from')
                ->setParameter('finish_at_from', DBALDateTimeFormatter::format($criteria->getFinishAtFrom()));
        }

        if (null !== $criteria->getFinishAtTo()) {
            $qb->andWhere('t.finish_at <= :finish_at_to')
                ->setParameter('finish_at_to', DBALDateTimeFormatter::format($criteria->getFinishAtTo()));
        }

        // By default, exclude deleted meetings
        $qb->andWhere('t.deleted_at IS NULL');

        return $qb;
    }

    /**
     * @throws VirtualMeetingRepositoryException
     */
    private function fromStringToVirtualMeetingType(string $virtualMeetingAsString): VirtualMeetingType
    {
        return match ($virtualMeetingAsString) {
            'fixed' => VirtualMeetingType::Fixed,
            default => throw VirtualMeetingRepositoryException::invalidVirtualMeetingType($virtualMeetingAsString),
        };
    }

    /**
     * @throws VirtualMeetingRepositoryException
     */
    private function fromVirtualMeetingTypeToString(VirtualMeetingType $virtualMeetingType): string
    {
        return match ($virtualMeetingType) {
            VirtualMeetingType::Fixed => 'fixed',
            default => throw VirtualMeetingRepositoryException::invalidVirtualMeetingType($virtualMeetingType->name),
        };
    }
}
