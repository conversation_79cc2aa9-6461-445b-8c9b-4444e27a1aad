<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Resource\Exception;

use App\V2\Domain\Shared\Exception\EntityNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;

class ResourceNotFoundException extends EntityNotFoundException
{
    public function __construct(Resource $resource)
    {
        parent::__construct(
            \sprintf(
                'Resource of type %s with ID %s not found',
                $resource->getType()->name,
                $resource->getId()->value()
            )
        );
    }
}
