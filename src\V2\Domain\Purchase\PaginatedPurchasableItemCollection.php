<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Collection\PaginatedCollection;

class PaginatedPurchasableItemCollection extends PaginatedCollection
{
    public function __construct(
        private readonly PurchasableItemCollection $purchasableItemCollection,
        int $totalItems
    ) {
        parent::__construct($totalItems);
    }

    public function getCollection(): PurchasableItemCollection
    {
        return $this->purchasableItemCollection;
    }
}
