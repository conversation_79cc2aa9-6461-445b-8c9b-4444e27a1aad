<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\DeletePurchasableItemCommand;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class DeletePurchasableItemController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUuidException
     */
    public function __invoke(string $purchasableItemId, Request $request): Response
    {
        UuidValidator::validateUuid($purchasableItemId);

        $this->execute(
            new DeletePurchasableItemCommand(
                purchasableItemId: new Uuid($purchasableItemId),
            )
        );

        return new JsonResponse(
            data: null,
            status: Response::HTTP_NO_CONTENT,
        );
    }
}
