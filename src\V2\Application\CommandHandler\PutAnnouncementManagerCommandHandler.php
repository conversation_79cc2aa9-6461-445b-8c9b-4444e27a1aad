<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\Announcement;
use App\Entity\User;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Service\SettingsService;
use App\V2\Application\Command\PutAnnouncementManager;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Exception\PutAnnouncementManagerException;
use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class PutAnnouncementManagerCommandHandler
{
    public function __construct(
        private AnnouncementManagerRepository $announcementManagerRepository,
        private UserRepository $userRepository,
        private LegacyAnnouncementRepository $legacyAnnouncementRepository,
        private SettingsService $settingsService,
    ) {
    }

    /**
     * @throws AnnouncementNotFoundException
     * @throws InfrastructureException
     * @throws ManagerNotAuthorizedException
     * @throws PutAnnouncementManagerException
     * @throws CriteriaException
     */
    public function handle(PutAnnouncementManager $command): void
    {
        $announcement = $this->legacyAnnouncementRepository->findOneBy(['id' => $command->getAnnouncementId()->value()]);
        if (null === $announcement) {
            throw new AnnouncementNotFoundException();
        }
        // Check if the feature is enabled
        if (!$this->settingsService->get('app.announcement.managers.sharing')) {
            throw ManagerNotAuthorizedException::announcementManagerSharingIsDisabled();
        }

        $this->checkPermissions(user: $command->getRequestUser(), announcement: $announcement);

        try {
            $user = $this->userRepository->findOneBy(
                UserCriteria::createById($command->getUserId())
            );
        } catch (UserNotFoundException $e) {
            throw PutAnnouncementManagerException::fromPrevious($e);
        }

        if (!$user->isManager()) {
            throw PutAnnouncementManagerException::userIsNotAManager();
        }

        $this->checkManagerNotLinkedToAnnouncement(user: $user, announcement: $announcement);

        $this->ensureManagerIsNotTheCreatorOfAnnouncement(user: $user, announcement: $announcement);

        $announcementManager = new AnnouncementManager(
            userId: $command->getUserId(),
            announcementId: $command->getAnnouncementId(),
        );

        $this->announcementManagerRepository->insert($announcementManager);
    }

    /**
     * @throws ManagerNotAuthorizedException
     */
    private function checkPermissions(User $user, Announcement $announcement): void
    {
        if (
            !\in_array(User::ROLE_ADMIN, $user->getRoles())
            && \in_array(User::ROLE_MANAGER, $user->getRoles(), true)
            && ($announcement->getCreatedBy() && $announcement->getCreatedBy()->getId() !== $user->getId())
        ) {
            throw ManagerNotAuthorizedException::userNotAuthorized(announcement: $announcement, user: $user->__toString());
        }
    }

    /**
     * @throws PutAnnouncementManagerException
     * @throws InfrastructureException
     */
    private function checkManagerNotLinkedToAnnouncement(User $user, Announcement $announcement): void
    {
        try {
            $this->announcementManagerRepository->findOneBy(
                AnnouncementManagerCriteria::createEmpty()
                    ->filterByUserId($user->getId())
                    ->filterByAnnouncementId($announcement->getId())
            );

            throw PutAnnouncementManagerException::managerAlreadyExists();
        } catch (AnnouncementManagerNotFoundException) {
        }
    }

    /**
     * @throws PutAnnouncementManagerException
     */
    private function ensureManagerIsNotTheCreatorOfAnnouncement(User $user, Announcement $announcement): void
    {
        if ($user->getId() === $announcement->getCreatedBy()?->getId()) {
            throw PutAnnouncementManagerException::userIsTheCreatorOfAnnouncement();
        }
    }
}
