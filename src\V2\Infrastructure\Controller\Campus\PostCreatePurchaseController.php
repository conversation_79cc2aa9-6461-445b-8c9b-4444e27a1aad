<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Campus;

use App\V2\Application\Command\PostCreatePurchaseCommand;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Purchase\PurchaseTransformer;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use App\V2\Infrastructure\Validator\Campus\CreatePurchaseValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostCreatePurchaseController extends CommandBusAccessor
{
    /**
     * @throws RequestAttributeExtractorException
     * @throws ValidatorException
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws InvalidCurrencyCodeException
     */
    public function __invoke(Request $request, ParameterBagInterface $parameterBag): Response
    {
        $content = json_decode($request->getContent(), true);
        CreatePurchaseValidator::validatePostPurchasableItems($content ?? []);

        // TODO taxRate must be extracted from the user's location
        $taxRate = new TaxRate((float) $parameterBag->get('app.tax_rate'));
        $currency = Currency::fromCode(CurrencyCodeTransformer::fromString(
            $parameterBag->get('app.currency_code') ?? CurrencyCode::EUR
        ));

        $purchase = $this->execute(
            new PostCreatePurchaseCommand(
                purchasableItemIds: new UuidCollection(
                    array_map(fn (string $id) => new Uuid($id), $content['items'])
                ),
                userId: new Id(RequestAttributeExtractor::extractUser($request)->getId()),
                taxRate: $taxRate,
                currency: $currency,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                PurchaseTransformer::fromPurchaseToArray($purchase)
            )->toArray(),
            status: Response::HTTP_CREATED,
        );
    }
}
