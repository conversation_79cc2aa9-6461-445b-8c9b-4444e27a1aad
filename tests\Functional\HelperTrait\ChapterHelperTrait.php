<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\Season;
use App\Entity\TypeCourse;
use App\Tests\Mother\Entity\ChapterMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create, update, and delete Course.
 * Assumes that if your Course requires a TypeCourse, it is injected as a parameter
 * or created separately using the TypeCourseHelperTrait.
 */
trait ChapterHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetChapter(
        Course $course,
        ?Season $season = null,
        ?int $id = null,
        ?string $title = null,
        ?string $description = null,
        int $position = 1,
        bool $active = true,
        ?int $maxQuestion = null,
        ?ChapterType $chapterType = null,
        ?\DateTimeImmutable $deletedAt = null,
    ): Chapter {
        $em = $this->getEntityManager();

        if (null === $season) {
            $season = $this->createAndGetSeason($course);
        } else {
            $season = $em->getRepository(Season::class)->find($season->getId());
        }

        if (null === $chapterType) {
            $chapterType = $em
                ->getRepository(ChapterType::class)
                ->find(\App\Enum\ChapterType::CONTENT_TYPE);
        }

        $chapter = ChapterMother::create(
            id: $id,
            title: $title,
            description: $description,
            position: $position,
            active: $active,
            maxQuestion: $maxQuestion,
            type: $chapterType,
            course: $this->getEntityManager()->getRepository(Course::class)->find($course->getId()),
            season: $season,
            deletedAt: $deletedAt,
        );

        $em->persist($chapter);
        $em->flush();

        return $chapter;
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeChapter(Chapter $chapter): void
    {
        $em = $this->getEntityManager();
        $em->remove($chapter);
        $em->flush();
    }
}
