<?php

declare(strict_types=1);

namespace App\V2\Application\VirtualMeeting;

use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;

readonly class VirtualMeetingFactory
{
    /**
     * @throws InvalidVirtualMeetingException
     */
    public static function createFromDTO(
        Uuid $id,
        VirtualMeetingDTO $dto,
    ): VirtualMeeting {
        return new VirtualMeeting(
            $id,
            $dto->getType(),
            $dto->getStartAt(),
            $dto->getFinishAt(),
            $dto->getUrl(),
            new \DateTimeImmutable()
        );
    }

    /**
     * @throws InvalidVirtualMeetingException
     */
    public static function updateFromDTO(
        VirtualMeeting $existingVirtualMeeting,
        VirtualMeetingDTO $dto,
    ): VirtualMeeting {
        return new VirtualMeeting(
            $existingVirtualMeeting->getId(),
            $dto->getType(),
            $dto->getStartAt(),
            $dto->getFinishAt(),
            $dto->getUrl(),
            $existingVirtualMeeting->getCreatedAt(),
            new \DateTimeImmutable()
        );
    }
}
