<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\FilterCategory;
use App\Entity\FilterCategoryTranslation;
use App\Entity\User;
use App\V2\Domain\Shared\Criteria\SortDirection;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method FilterCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method FilterCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method FilterCategory[]    findAll()
 * @method FilterCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FilterCategoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FilterCategory::class);
    }

    public function getCategories(): array
    {
        return $this->createQueryBuilder('c')
            ->orderBy('c.sort', SortDirection::ASC->name)
            ->getQuery()
            ->getResult();
    }

    public function findOrCreateCategory(string $name): FilterCategory
    {
        $category = $this->_em->getRepository(FilterCategory::class)->findOneBy(['name' => $name]);
        if ($category) {
            return $category;
        }

        $category = new FilterCategory();
        $category->setName($name);
        $this->_em->persist($category);

        return $category;
    }

    public function findAllOrderBySort(): array
    {
        $query = $this->createQueryBuilder('s')
            ->select('s')
            ->orderBy('s.sort', SortDirection::ASC->name);

        return $query->getQuery()->getResult();
    }

    public function findByFilterOrderBySort(int $id)
    {
        $query = $this->createQueryBuilder('s')
            ->select('s')
            ->where('s.id = :id')
            ->setParameter('id', $id)
            ->orderBy('s.sort', SortDirection::ASC->name);

        return $query->getQuery()->setMaxResults(1)->getOneOrNullResult();
    }

    public function getNextSort(): int
    {
        $maxSort = $this->createQueryBuilder('s')
            ->select('MAX(s.sort)')
            ->getQuery()->getSingleScalarResult() ?? 0;

        return $maxSort + 1;
    }

    public function getListFilterCategoryTranslate(?string $locale = null): array
    {
        $AllFilter = $this->findAllOrderBySort();
        $filters = [];

        foreach ($AllFilter as $filter) {
            $name = $filter->getName();

            if (!empty($locale)) {
                /** @var FilterCategoryTranslation $translations */
                $translations = $filter->translate($locale, true);
                $name = $translations->getName() ?? $name;
            }

            $filters[] = [
                'id' => $filter->getId(),
                'name' => $name,
            ];
        }

        return $filters;
    }

    public function findFiltersAndCategoriesForUser(User $user): array
    {
        $qb = $this->createQueryBuilder('cat')
            ->select('cat.id AS categoryId, COALESCE(ct.name, cat.name) AS categoryName, f.id AS filterId, COALESCE(t.name, f.name) AS filterName')
            ->leftJoin('cat.translations', 'ct', 'WITH', 'ct.locale = :locale')
            ->leftJoin('cat.filters', 'f')
            ->leftJoin('f.translations', 't', 'WITH', 't.locale = :locale')
            ->setParameter('locale', $user->getLocale());

        if ($user->isManager() && !$user->isAdmin()) {
            $managerFilters = array_map(fn ($filter) => $filter->getId(), $user->getFilters()->toArray());

            if (empty($managerFilters)) {
                return [];
            } else {
                $qb->andWhere($qb->expr()->in('f.id', ':managerFilters'))
                    ->setParameter('managerFilters', $managerFilters);
            }
        }

        return $qb->orderBy('cat.sort', SortDirection::ASC->name)
            ->addOrderBy('cat.id', SortDirection::ASC->name)
            ->getQuery()->getResult();
    }
}
