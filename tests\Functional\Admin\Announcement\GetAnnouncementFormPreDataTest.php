<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementStepCreation;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseAnnouncementStepCreation;
use App\Enum\AnnouncementStepCreation as AnnouncementStepCreationEnum;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetAnnouncementFormPreDataTest extends FunctionalTestCase
{
    public const string TIMEZONES_SETTING = 'app.timezones';

    /**
     * @throws NotSupported
     * @throws MappingException
     */
    #[DataProvider('correctTemporalizationDataInResponseDataProvider')]
    public function testCorrectTemporalizationDataInResponse(
        bool $temporalizationConfigurationActive,
        bool $temporalizationConfigIsExpected,
    ): void {
        $announcementSecondStep = $this->getEntityManager()
            ->getRepository(AnnouncementStepCreation::class)
            ->find(AnnouncementStepCreationEnum::ANNOUNCEMENT_GENERAL_INFO);
        $defaultTypeCourseAnnouncementStepCreation = $this->getEntityManager()
            ->getRepository(TypeCourseAnnouncementStepCreation::class)
            ->findBy([
                'announcementStepCreation' => $announcementSecondStep,
            ]);

        foreach ($defaultTypeCourseAnnouncementStepCreation as $typeCourseAnnouncementStepCreation) {
            $defaultTypeCourseAnnouncementStepCreationActiveValues[$typeCourseAnnouncementStepCreation->getId()]
                = $typeCourseAnnouncementStepCreation->isActive();

            $typeCourseAnnouncementStepCreation->setActive(true);
        }

        $announcementConfigurationTypeRepository = $this->getRepository(AnnouncementConfigurationType::class);
        $temporalizationConfigurationType = $announcementConfigurationTypeRepository
            ->find(AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION);
        $defaultActive = $temporalizationConfigurationType->isActive();

        $temporalizationConfigurationType->setActive($temporalizationConfigurationActive);

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementFormPreDataEndpoint(),
            bearerToken: $this->loginAndGetToken()
        );

        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        $this->assertIsArray($data);

        $this->assertArrayHasKey('stepsConfigurations', $data);
        $this->assertIsArray($data['stepsConfigurations']);

        foreach ($data['stepsConfigurations'] as $announcementType => $announcementTypeConfiguration) {
            if (
                !\in_array($announcementType, [
                    TypeCourse::CODE_ONLINE,
                    TypeCourse::CODE_MIXED,
                ])
            ) {
                continue;
            }

            $this->assertIsArray($announcementTypeConfiguration);
            $this->assertIsArray($announcementTypeConfiguration[0]);

            $foundTemporalization = false;
            foreach ($announcementTypeConfiguration[0]['configurations'] as $configuration) {
                $this->assertIsArray($configuration);
                $this->assertArrayHasKey('id', $configuration);
                if (AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION === $configuration['id']) {
                    $foundTemporalization = true;
                    break;
                }
            }

            $this->assertEquals($temporalizationConfigIsExpected, $foundTemporalization);
        }

        $this->assertArrayHasKey('clientConfigurationAnnouncement', $data);
        $this->assertIsArray($data['clientConfigurationAnnouncement']);

        $foundTemporalization = false;
        foreach ($data['clientConfigurationAnnouncement'] as $configuration) {
            $this->assertIsArray($configuration);
            $this->assertArrayHasKey('id', $configuration);
            if (AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION === $configuration['id']) {
                $foundTemporalization = true;
                break;
            }
        }
        $this->assertEquals($temporalizationConfigIsExpected, $foundTemporalization);

        $this->assertArrayHasKey('typeCourses', $data);
        $this->assertIsArray($data['typeCourses']);

        foreach ($data['typeCourses'] as $typeCourse) {
            if (!\in_array($typeCourse['type'], [TypeCourse::CODE_ONLINE, TypeCourse::CODE_MIXED])) {
                continue;
            }

            $this->assertArrayHasKey('steps', $typeCourse);
            $this->assertIsArray($typeCourse['steps']);
            foreach ($typeCourse['steps'] as $step) {
                if (AnnouncementStepCreationEnum::ANNOUNCEMENT_GENERAL_INFO !== $step['id']) {
                    continue;
                }

                $this->assertArrayHasKey('configurations', $step);
                $this->assertIsArray($step['configurations']);

                $foundTemporalization = false;
                foreach ($step['configurations'] as $configuration) {
                    if (AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION === $configuration['id']) {
                        $foundTemporalization = true;
                        break;
                    }
                }
                $this->assertEquals($temporalizationConfigIsExpected, $foundTemporalization);
            }
        }

        if (isset($defaultTypeCourseAnnouncementStepCreationActiveValues)) {
            foreach ($defaultTypeCourseAnnouncementStepCreationActiveValues as $id => $active) {
                $typeCourseAnnouncementStepCreation = $this->getEntityManager()
                    ->getRepository(TypeCourseAnnouncementStepCreation::class)
                    ->find($id);
                if (null === $typeCourseAnnouncementStepCreation) {
                    continue;
                }
                $typeCourseAnnouncementStepCreation->setActive($active);
            }
        }

        $temporalizationConfigurationType = $announcementConfigurationTypeRepository
            ->find(AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION);
        $temporalizationConfigurationType->setActive($defaultActive);
    }

    public static function correctTemporalizationDataInResponseDataProvider(): array
    {
        return [
            'temporalization active' => [true, true],
            'temporalization inactive' => [false, false],
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     * @throws \JsonException
     */
    public function testCheckTimezonesOrdered()
    {
        $timezones = [
            'Europe/Madrid',
            'Africa/Accra',
            'America/Cayenne',
            'America/Caracas',
            'UTC',
            'Pacific/Wake',
            'Pacific/Tarawa',
            'Europe/Rome',
            'Europe/Riga',
            'Europe/Sarajevo',
            'Atlantic/Madeira',
        ];

        $this->updateSettingValue(
            value: (string) json_encode($timezones, JSON_THROW_ON_ERROR),
            code: self::TIMEZONES_SETTING
        );

        $userToken = $this->loginAndGetToken();
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementFormPreDataEndpoint(),
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        $data = $this->extractResponseData($response);
        $this->assertArrayHasKey('timezones', $data);

        $expectedTimezones = [
            'Africa/Accra',
            'America/Caracas',
            'America/Cayenne',
            'Atlantic/Madeira',
            'Europe/Madrid',
            'Europe/Riga',
            'Europe/Rome',
            'Europe/Sarajevo',
            'Pacific/Tarawa',
            'Pacific/Wake',
            'UTC',
        ];

        $this->assertEquals($expectedTimezones, $data['timezones'], 'Timezones should be returned in alphabetical order');
    }

    public function tearDown(): void
    {
        parent::tearDown();
    }
}
