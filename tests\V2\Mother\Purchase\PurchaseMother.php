<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Purchase;

use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Financial\TaxRateMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class PurchaseMother
{
    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function create(
        ?Uuid $id = null,
        ?Id $userId = null,
        ?PurchaseStatus $status = null,
        ?Money $amount = null,
        ?TaxRate $taxRate = null,
        ?Money $taxAmount = null,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ): Purchase {
        return new Purchase(
            id: $id ?? UuidMother::create(),
            userId: $userId ?? IdMother::create(),
            status: $status ?? PurchaseStatus::Pending,
            amount: $amount ?? MoneyMother::create(),
            taxRate: $taxRate ?? TaxRateMother::create(),
            taxAmount: $taxAmount ?? MoneyMother::create(),
            createdAt: $createdAt ?? new \DateTimeImmutable(),
            updatedAt: $updatedAt,
            deletedAt: $deletedAt,
        );
    }
}
