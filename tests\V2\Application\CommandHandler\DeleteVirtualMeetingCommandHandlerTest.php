<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Application\Command\DeleteVirtualMeeting;
use App\V2\Application\CommandHandler\DeleteVirtualMeetingCommandHandler;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class DeleteVirtualMeetingCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?VirtualMeetingRepository $virtualMeetingRepository = null,
    ): DeleteVirtualMeetingCommandHandler {
        return new DeleteVirtualMeetingCommandHandler(
            $virtualMeetingRepository ?? $this->createMock(VirtualMeetingRepository::class),
        );
    }

    /**
     * @throws InvalidUuidException
     */
    private function getCommand(
        ?Uuid $virtualMeetingId = null,
    ): DeleteVirtualMeeting {
        return new DeleteVirtualMeeting(
            $virtualMeetingId ?? UuidMother::create(),
        );
    }

    /**
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(DeleteVirtualMeetingCommandHandler::class, $handler);
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     * @throws \DateMalformedStringException
     * @throws CriteriaException
     */
    public function testHandleDeletesVirtualMeeting(): void
    {
        $virtualMeetingId = UuidMother::create();
        $command = $this->getCommand($virtualMeetingId);

        $virtualMeeting = VirtualMeetingMother::create(id: $virtualMeetingId);

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);

        // Expect findOneBy to be called with criteria containing the ID
        $virtualMeetingRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (VirtualMeetingCriteria $criteria) use ($virtualMeetingId) {
                return $criteria->getId()->equals($virtualMeetingId);
            }))
            ->willReturn($virtualMeeting);

        // Expect delete to be called with the virtual meeting
        $virtualMeetingRepository->expects($this->once())
            ->method('delete')
            ->with($this->callback(function (VirtualMeeting $meeting) use ($virtualMeetingId) {
                return $meeting->getId()->equals($virtualMeetingId);
            }));

        $handler = $this->getHandler($virtualMeetingRepository);

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function testHandleThrowsExceptionWhenVirtualMeetingNotFound(): void
    {
        $virtualMeetingId = UuidMother::create();
        $command = $this->getCommand($virtualMeetingId);

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);

        // Expect findOneBy to throw VirtualMeetingNotFoundException
        $notFoundException = new VirtualMeetingNotFoundException();
        $virtualMeetingRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (VirtualMeetingCriteria $criteria) use ($virtualMeetingId) {
                return $criteria->getId()->equals($virtualMeetingId);
            }))
            ->willThrowException($notFoundException);

        // Delete should not be called
        $virtualMeetingRepository->expects($this->never())
            ->method('delete');

        $handler = $this->getHandler($virtualMeetingRepository);

        $this->expectExceptionObject($notFoundException);

        $handler->handle($command);
    }
}
