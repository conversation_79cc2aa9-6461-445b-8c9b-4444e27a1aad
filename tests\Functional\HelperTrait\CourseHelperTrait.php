<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseAlerts;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Modules\CourseSection\Enum\SectionSubtypeEnum;
use App\Tests\Mother\Entity\TypeCourseMother;
use App\V2\Domain\Course\Diploma\DiplomaConfig;
use App\V2\Domain\Shared\Filter\FilterCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create, update, and delete Course.
 * Assumes that if your Course requires a TypeCourse, it is injected as a parameter
 * or created separately using the TypeCourseHelperTrait.
 */
trait CourseHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetCourse(
        string $name = 'Test Course Name',
        string $code = 'TestCourseCode-1',
        ?TypeCourse $typeCourse = null,
        ?string $description = 'Test Course Description',
        string $locale = 'es',
        bool $active = true,
        bool $open = true,
        bool $isNew = true,
        bool $openVisible = true,
        ?CourseCategory $courseCategory = null,
        ?\DateTimeInterface $newAt = null,
        ?int $duration = null,
        ?User $createdBy = null,
        ?bool $showDuration = null,
        ?FilterCollection $courseFilters = null,
    ): Course {
        $em = $this->getEntityManager();

        // Crear y persistir el TypeCourse primero si no se proporciona
        if (null === $typeCourse) {
            $typeCourse = $this->getTypeCourse();
            $em->refresh($typeCourse);
        }

        // Crear y persistir la CourseCategory si no se proporciona
        if (null === $courseCategory) {
            $courseCategory = $this->createAndGetCourseCategory();
            $em->refresh($courseCategory);
        }

        $course = new Course();
        $course->setName($name)
            ->setCode($code)
            ->setTypeCourse($typeCourse)
            ->setCategory($courseCategory)
            ->setLocale($locale)
            ->setDescription($description)
            ->setActive($active)
            ->setOpen($open)
            ->setIsNew($isNew)
            ->setOpenVisible($openVisible)
            ->setNewAt($newAt)
            ->setDuration($duration);

        if ($createdBy) {
            $course->setCreatedBy($createdBy);
        }

        if (null !== $showDuration) {
            $diplomaConfig = new DiplomaConfig($showDuration);
            $course->setDiplomaConfig($diplomaConfig);
        }

        if (null !== $courseFilters) {
            $course->setFilters($courseFilters->all());
        }

        $em->persist($course);
        $em->flush();
        $em->refresh($course);

        return $course;
    }

    protected function createAndGetCourseCategory(?string $name = null, ?int $sort = null): CourseCategory
    {
        $em = $this->getEntityManager();

        $courseCategory = new CourseCategory();

        $courseCategory->setName($name ?? 'Test Category');

        // If no sort is provided, get the next available sort value
        if (null === $sort) {
            $sort = $em->getRepository(CourseCategory::class)->getNextSort();
        }
        $courseCategory->setSort($sort);

        $em->persist($courseCategory);
        $em->flush();

        return $courseCategory;
    }

    protected function createAndGetCourseSection(
        string $name = 'Mi Formación',
        ?CourseCategory $courseCategory = null,
        ?array $meta = null,
    ): CourseSection {
        $em = $this->getEntityManager();

        $courseSection = new CourseSection();

        $courseSection->setName($name);
        $courseSection->setActive(true);
        $courseSection->setMeta($meta ?? SectionSubtypeEnum::setDefaultStructure());
        $courseSection->setSort(1);

        $em->persist($courseSection);
        $em->flush();

        return $courseSection;
    }

    /**
     * The default TypeCourse for courses, is online. It's recommended not to change the function parameters.
     * If the test requires other type course, create or get based on requirements.
     */
    protected function getTypeCourse(
        string $code = 'online',
        string $denomination = 'INTERN',
    ): TypeCourse {
        $typeCourse = $this->getEntityManager()->getRepository(TypeCourse::class)->findOneBy([
            'code' => $code,
            'denomination' => $denomination,
        ]);
        if (!$typeCourse) {
            $this->fail(\sprintf('TypeCourse with code "%s" not found', $code));
        }

        return $typeCourse;
    }

    /**
     * Creates and persists a TypeCourse and returns it.
     *
     * @throws ORMException
     */
    protected function createAndGetTypeCourse(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        bool $active = true,
        ?string $code = null,
        ?string $denomination = null,
        ?\DateTimeInterface $deletedAt = null,
    ): TypeCourse {
        $em = $this->getEntityManager();
        if (null !== $id) {
            $typeCourse = $em->getRepository(TypeCourse::class)->find($id);
            if (null !== $typeCourse) {
                return $typeCourse;
            }
        }

        $typeCourse = TypeCourseMother::create(
            id: $id,
            name: $name,
            description: $description,
            active: $active,
            code: $code,
            denomination: $denomination,
            deletedAt: $deletedAt,
        );

        $originalMetadata = $this->setCustomIdToEntity($typeCourse);
        $em->persist($typeCourse);
        $em->flush();
        $this->restoreEntityMetadata($typeCourse, $originalMetadata);

        return $typeCourse;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetUserCourse(
        User $user,
        Course $course,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishedAt = null
    ): UserCourse {
        $em = $this->getEntityManager();

        $userCourse = new UserCourse();
        $userCourse->setUser($user);
        $userCourse->setCourse($course);
        $userCourse->setStartedAt($startAt ?? new \DateTimeImmutable('today'));
        $userCourse->setFinishedAt($finishedAt);

        $em->persist($userCourse);
        $em->flush();

        return $userCourse;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetUserCourseChapter(
        UserCourse $userCourse,
        Chapter $chapter,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishedAt = null,
        $timeSpent = 0
    ): UserCourseChapter {
        $em = $this->getEntityManager();

        $userCourseChapter = new UserCourseChapter();
        $userCourseChapter->setUserCourse($userCourse);
        $userCourseChapter->setChapter($chapter);
        $userCourseChapter->setStartedAt($startAt ?? new \DateTimeImmutable('today'));
        $userCourseChapter->setFinishedAt($finishedAt);
        $userCourseChapter->setTimeSpent($timeSpent);

        $em->persist($userCourseChapter);
        $em->flush();

        return $userCourseChapter;
    }

    /**
     * Deletes a Course from the database.
     *
     * @throws ORMException
     */
    protected function removeCourse(Course $course): void
    {
        $em = $this->getEntityManager();
        $em->remove($course);
        $em->flush();
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeTypeCourse(TypeCourse $typeCourse): void
    {
        $alerts = $this->getEntityManager()->getRepository(TypeCourseAlerts::class)
            ->findBy(['typeCourse' => $typeCourse]);
        if (!empty($alerts)) {
            throw new \LogicException('Cannot delete type course with existing referencing alerts.');
        }

        $em = $this->getEntityManager();
        $em->remove($typeCourse);
        $em->flush();
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeUserCourse(UserCourse $userCourse): void
    {
        $em = $this->getEntityManager();
        $em->remove($userCourse);
        $em->flush();
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeUserCourseChapter(UserCourseChapter $userCourseChapter): void
    {
        $em = $this->getEntityManager();
        $em->remove($userCourseChapter);
        $em->flush();
    }
}
