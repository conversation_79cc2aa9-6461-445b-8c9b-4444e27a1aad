<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\GetPurchasableItemsValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetPurchasableItemsValidatorTest extends ValidatorTestCase
{
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        GetPurchasableItemsValidator::validateGetPurchasableItemsRequest($payload);
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Empty payload' => [
            [],
        ];

        yield 'Valid page and page_size' => [
            [
                'page' => '1',
                'page_size' => '10',
            ],
        ];

        yield 'Valid sort_by and sort_dir (asc)' => [
            [
                'sort_by' => 'name',
                'sort_dir' => 'asc',
            ],
        ];

        yield 'Valid sort_by and sort_dir (desc)' => [
            [
                'sort_by' => 'price_amount',
                'sort_dir' => 'desc',
            ],
        ];

        yield 'Valid search' => [
            [
                'search' => 'test course',
            ],
        ];

        yield 'Valid type (course)' => [
            [
                'type' => 'course',
            ],
        ];

        yield 'Valid type (subscription)' => [
            [
                'type' => 'subscription',
            ],
        ];

        yield 'Valid price_min' => [
            [
                'price_min' => '1000',
            ],
        ];

        yield 'Valid price_max' => [
            [
                'price_max' => '5000',
            ],
        ];

        yield 'Valid price range' => [
            [
                'price_min' => '1000',
                'price_max' => '5000',
            ],
        ];

        yield 'Valid price range with equal values' => [
            [
                'price_min' => '1000',
                'price_max' => '1000',
            ],
        ];

        yield 'Valid is_active (true)' => [
            [
                'is_active' => 'true',
            ],
        ];

        yield 'Valid is_active (false)' => [
            [
                'is_active' => 'false',
            ],
        ];

        yield 'Valid all fields' => [
            [
                'page' => '1',
                'page_size' => '10',
                'sort_by' => 'name',
                'sort_dir' => 'asc',
                'search' => 'test',
                'type' => 'course',
                'price_min' => '1000',
                'price_max' => '5000',
                'is_active' => 'true',
            ],
        ];
    }

    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            GetPurchasableItemsValidator::validateGetPurchasableItemsRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Empty page' => [
            [
                'page' => '',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'page is not a digit' => [
            [
                'page' => 'abc',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'page is less than 1' => [
            [
                'page' => '0',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'page_size is required when page is provided' => [
            [
                'page' => '1',
            ],
            [
                '[page]' => [
                    'Page size is required when page is provided.',
                ],
            ],
        ];

        yield 'Empty page_size' => [
            [
                'page' => '1',
                'page_size' => '',
            ],
            [
                '[page_size]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'page_size is not a digit' => [
            [
                'page' => '1',
                'page_size' => 'abc',
            ],
            [
                '[page_size]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'page_size is less than 1' => [
            [
                'page' => '1',
                'page_size' => '0',
            ],
            [
                '[page_size]' => [
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'page is required when page_size is provided' => [
            [
                'page_size' => '10',
            ],
            [
                '[page_size]' => [
                    'Page is required when page size is provided.',
                ],
            ],
        ];

        yield 'Empty sort_by' => [
            [
                'sort_by' => '',
                'sort_dir' => 'asc',
            ],
            [
                '[sort_by]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'sort_by is not a string' => [
            [
                'sort_by' => 123,
                'sort_dir' => 'asc',
            ],
            [
                '[sort_by]' => [
                    'This value should be of type string.',
                ],
            ],
        ];

        yield 'sort_dir is required when sort_by is provided' => [
            [
                'sort_by' => 'name',
            ],
            [
                '[sort_by]' => [
                    'Sort direction is required when sort by is provided.',
                ],
            ],
        ];

        yield 'Empty sort_dir' => [
            [
                'sort_by' => 'name',
                'sort_dir' => '',
            ],
            [
                '[sort_dir]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_dir is not a string' => [
            [
                'sort_by' => 'name',
                'sort_dir' => 123,
            ],
            [
                '[sort_dir]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_dir invalid choice' => [
            [
                'sort_by' => 'name',
                'sort_dir' => 'invalid',
            ],
            [
                '[sort_dir]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_by is required when sort_dir is provided' => [
            [
                'sort_dir' => 'asc',
            ],
            [
                '[sort_dir]' => [
                    'Sort by is required when sort direction is provided.',
                ],
            ],
        ];

        yield 'Empty search' => [
            [
                'search' => '',
            ],
            [
                '[search]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'search is not a string' => [
            [
                'search' => 123,
            ],
            [
                '[search]' => [
                    'This value should be of type string.',
                ],
            ],
        ];

        yield 'Empty type' => [
            [
                'type' => '',
            ],
            [
                '[type]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'type is not a string' => [
            [
                'type' => 123,
            ],
            [
                '[type]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'type invalid choice' => [
            [
                'type' => 'invalid',
            ],
            [
                '[type]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'Empty price_min' => [
            [
                'price_min' => '',
            ],
            [
                '[price_min]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'price_min is not a digit' => [
            [
                'price_min' => 'abc',
            ],
            [
                '[price_min]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'Empty price_max' => [
            [
                'price_max' => '',
            ],
            [
                '[price_max]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'price_max is not a digit' => [
            [
                'price_max' => 'abc',
            ],
            [
                '[price_max]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'Empty is_active' => [
            [
                'is_active' => '',
            ],
            [
                '[is_active]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'is_active invalid choice' => [
            [
                'is_active' => 'invalid',
            ],
            [
                '[is_active]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'is_active is not a string' => [
            [
                'is_active' => 123,
            ],
            [
                '[is_active]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'price_min greater than price_max' => [
            [
                'price_min' => '5000',
                'price_max' => '1000',
            ],
            [
                '[price_min]' => [
                    'Price min must be less than or equal to price max.',
                ],
                '[price_max]' => [
                    'Price max must be greater than or equal to price min.',
                ],
            ],
        ];

        yield 'price_min equal to price_max (should be valid but testing edge case)' => [
            [
                'price_min' => '1000',
                'price_max' => '999',
            ],
            [
                '[price_min]' => [
                    'Price min must be less than or equal to price max.',
                ],
                '[price_max]' => [
                    'Price max must be greater than or equal to price min.',
                ],
            ],
        ];

        yield 'Invalid extra field' => [
            [
                'invalid_field' => 'value',
            ],
            [
                '[invalid_field]' => [
                    'This field was not expected.',
                ],
            ],
        ];
    }
}
