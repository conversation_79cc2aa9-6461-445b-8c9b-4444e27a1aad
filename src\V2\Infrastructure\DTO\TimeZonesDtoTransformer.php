<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\DTO;

use App\V2\Application\DTO\TimeZonesDto;

class TimeZonesDtoTransformer
{
    /**
     * @return array<int, array<string, mixed>>
     */
    public static function toArray(TimeZonesDto $timeZonesDto): array
    {
        $result = [];
        foreach ($timeZonesDto->getTimezones() as $timezone) {
            $result[] = [
                'value' => $timezone,
                'default' => $timezone === $timeZonesDto->getDefault(),
            ];
        }

        return $result;
    }
}
