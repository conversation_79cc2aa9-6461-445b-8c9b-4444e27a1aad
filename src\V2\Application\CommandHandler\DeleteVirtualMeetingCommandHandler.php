<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\DeleteVirtualMeeting;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;

readonly class DeleteVirtualMeetingCommandHandler
{
    public function __construct(
        private VirtualMeetingRepository $virtualMeetingRepository,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     * @throws CriteriaException
     */
    public function handle(DeleteVirtualMeeting $command): void
    {
        $virtualMeeting = $this->virtualMeetingRepository->findOneBy(
            VirtualMeetingCriteria::createById(
                $command->getVirtualMeetingId()
            )
        );

        $this->virtualMeetingRepository->delete($virtualMeeting);
    }
}
