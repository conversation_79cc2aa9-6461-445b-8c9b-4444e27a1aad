<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\User;

use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;

class ManagerFilterTransformer
{
    public static function toArray(ManagerFilter $managerFilter): array
    {
        if (null === $managerFilter->getFilter()) {
            return ['id' => $managerFilter->getFilterId()->value()];
        }

        return [
            'id' => $managerFilter->getFilter()->getId()->value(),
            'name' => $managerFilter->getFilter()->getName(),
            'category_id' => $managerFilter->getFilter()->getCategoryId()->value(),
        ];
    }

    public static function fromCollectionToArray(ManagerFilterCollection $collection): array
    {
        return array_map(
            fn (ManagerFilter $managerFilter) => self::toArray($managerFilter),
            $collection->all(),
        );
    }
}
