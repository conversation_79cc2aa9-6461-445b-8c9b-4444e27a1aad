<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends EntityWithId<Uuid>
 */
class PurchaseItem extends EntityWithId
{
    private ?PurchasableItem $purchasableItem = null;

    public function __construct(
        Uuid $id,
        private readonly Uuid $purchaseId,
        private readonly Uuid $purchasableItemId,
        private readonly Money $price,
    ) {
        parent::__construct($id);
    }

    public function getPurchaseId(): Uuid
    {
        return $this->purchaseId;
    }

    public function getPurchasableItemId(): Uuid
    {
        return $this->purchasableItemId;
    }

    public function getPrice(): Money
    {
        return $this->price;
    }

    public function setPurchasableItem(PurchasableItem $purchasableItem): self
    {
        $this->purchasableItem = $purchasableItem;

        return $this;
    }

    public function getPurchasableItem(): ?PurchasableItem
    {
        return $this->purchasableItem;
    }
}
