<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\VirtualMeeting;

use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Infrastructure\VirtualMeeting\VirtualMeetingTransformer;
use PHPUnit\Framework\TestCase;

class VirtualMeetingTransformerTest extends TestCase
{
    /**
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    public function testFromVirtualMeetingToArrayWithDefaultMother(): void
    {
        $vm = VirtualMeetingMother::create();

        $result = VirtualMeetingTransformer::fromVirtualMeetingToArray($vm);

        $this->assertSame([
            'type' => 'fixed',
            'url' => 'https://example.com/meeting',
        ], $result);
    }

    /**
     * @throws \DateMalformedStringException
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     */
    public function testFromVirtualMeetingToArrayWithCustomUrl(): void
    {
        $customUrl = 'https://meet.example.org/room/123';
        $vm = VirtualMeetingMother::create(url: $customUrl);

        $result = VirtualMeetingTransformer::fromVirtualMeetingToArray($vm);

        $this->assertSame([
            'type' => 'fixed',
            'url' => $customUrl,
        ], $result);
    }
}
