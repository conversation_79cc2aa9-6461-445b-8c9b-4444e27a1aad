<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Financial;

use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;

class CurrencyCodeTransformer
{
    public static function fromString(string $currencyCode): CurrencyCode
    {
        return match ($currencyCode) {
            'EUR' => CurrencyCode::EUR,
            'USD' => CurrencyCode::USD,
            default => throw InvalidCurrencyCodeException::withCode($currencyCode)
        };
    }

    public static function toString(CurrencyCode $currencyCode): string
    {
        return match ($currencyCode) {
            CurrencyCode::EUR => 'EUR',
            CurrencyCode::USD => 'USD',
        };
    }
}
