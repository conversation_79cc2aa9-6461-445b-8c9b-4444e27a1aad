<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Subscription\SubscriptionMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Subscription\Subscription;

trait SubscriptionFixtureTrait
{
    /**
     * @throws InvalidUuidException
     */
    private function setAndGetSubscriptionInRepository(
        ?Uuid $id = null,
        ?string $name = null,
        ?string $description = null,
    ): Subscription {
        $subscription = SubscriptionMother::create(
            id: $id,
            name: $name,
            description: $description,
        );

        $this->client->getContainer()->get('App\V2\Domain\Subscription\SubscriptionRepository')
            ->put($subscription);

        return $subscription;
    }
}
