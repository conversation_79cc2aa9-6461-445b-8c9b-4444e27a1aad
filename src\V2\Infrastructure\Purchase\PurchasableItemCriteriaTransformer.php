<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase;

use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Infrastructure\Shared\Financial\MoneyTransformer;
use App\V2\Infrastructure\Shared\QueryParamTransformer\PaginationTransformer;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;

class PurchasableItemCriteriaTransformer
{
    /**
     * @throws InvalidCurrencyCodeException
     * @throws CollectionException
     */
    public static function fromArray(array $data): PurchasableItemCriteria
    {
        $criteria = PurchasableItemCriteria::createEmpty();

        if (isset($data['search'])) {
            $criteria->filterBySearch($data['search']);
        }

        if (isset($data['type'])) {
            $criteria->filterByResourceType(ResourceTypeTransformer::fromString($data['type']));
        }

        if (isset($data['price_min'])) {
            $criteria->filterByMinPrice(MoneyTransformer::fromPayload([
                'price_amount' => (int) $data['price_min'],
                'price_currency' => $data['price_currency'] ?? 'EUR',
            ]));
        }

        if (isset($data['price_max'])) {
            $criteria->filterByMaxPrice(MoneyTransformer::fromPayload([
                'price_amount' => (int) $data['price_max'],
                'price_currency' => $data['price_currency'] ?? 'EUR',
            ]));
        }

        if (isset($data['is_active'])) {
            $criteria->filterByIsActive('true' === $data['is_active']);
        }

        if (isset($data['sort_by'])) {
            $criteria->sortBy(
                new SortCollection([
                    new Sort(
                        PurchasableItemSortableTransformer::toSortableField($data['sort_by']),
                        PurchasableItemSortableTransformer::toSortDirection($data['sort_dir'] ?? 'asc')
                    ),
                ])
            );
        }

        if (isset($data['page']) || isset($data['page_size'])) {
            $criteria->withPagination(
                PaginationTransformer::toPagination($data)
            );
        }

        return $criteria;
    }
}
