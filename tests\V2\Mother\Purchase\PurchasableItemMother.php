<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Purchase;

use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class PurchasableItemMother
{
    /**
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public static function create(
        ?Uuid $id = null,
        ?string $name = null,
        ?string $description = null,
        ?Money $price = null,
        ?Resource $resource = null,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
        ?bool $isActive = null,
    ): PurchasableItem {
        return new PurchasableItem(
            id: $id ?? UuidMother::create(),
            name: $name ?? 'Item',
            description: $description ?? 'Description',
            price: $price ?? MoneyMother::create(),
            resource: $resource ?? ResourceMother::create(),
            createdAt: $createdAt ?? new \DateTimeImmutable(),
            updatedAt: $updatedAt,
            deletedAt: $deletedAt,
            isActive: $isActive ?? false,
        );
    }
}
