openapi: 3.0.0
info:
  title: Easylearning Common API v2
  description: Common API endpoints for Easylearning
  version: 2.0.0

servers:
  - url: /api/v2/common
    description: Common API endpoints for v2

security:
  - bearerAuth: []

paths:
  /time-zones:
    get:
      tags:
        - Time Zones
      summary: Get available time zones
      description: Returns a list of available time zones in the system, indicating which one is the default
      operationId: getTimeZones
      responses:
        '200':
          description: List of available time zones
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                          example: "Europe/Madrid"
                        default:
                          type: boolean
                          example: true
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
