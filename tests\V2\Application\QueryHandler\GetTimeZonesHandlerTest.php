<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\Service\SettingsService;
use App\V2\Application\Query\GetTimeZones;
use App\V2\Application\QueryHandler\GetTimeZonesHandler;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetTimeZonesHandlerTest extends TestCase
{
    private GetTimeZonesHandler $handler;
    private SettingsService|MockObject $settingsService;

    private array $timeZones;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->handler = new GetTimeZonesHandler($this->settingsService);
        $this->timeZones = ['Europe/Madrid', 'America/New_York', 'Asia/Tokyo'];
    }

    /**
     * @throws InfrastructureException
     */
    public function testHandleWithCorrectData(): void
    {
        $defaultTimeZone = 'Europe/Madrid';

        $this->settingsService
            ->expects($this->exactly(2))
            ->method('get')
            ->willReturnMap([
                ['app.timezones', $this->timeZones],
                ['app.default_timezone', $defaultTimeZone],
            ]);

        $result = $this->handler->handle(new GetTimeZones());

        $this->assertSame($this->timeZones, $result->getTimezones());
        $this->assertSame($defaultTimeZone, $result->getDefault());
    }

    public function testHandleWithDefaultTimeZoneNotInList(): void
    {
        $defaultTimeZone = 'Europe/London';

        $this->settingsService
            ->expects($this->exactly(2))
            ->method('get')
            ->willReturnMap([
                ['app.timezones', $this->timeZones],
                ['app.default_timezone', $defaultTimeZone],
            ]);

        $this->expectException(InfrastructureException::class);
        $this->expectExceptionMessage('Default timezone is not in the available timezones list');

        $this->handler->handle(new GetTimeZones());
    }
}
