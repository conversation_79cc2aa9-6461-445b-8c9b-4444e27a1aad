<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Course\Manager;

use App\Tests\V2\Domain\Course\Manager\CourseManagerRepositoryTestCase;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Infrastructure\Persistence\Course\Manager\InMemoryCourseManagerRepository;

class InMemoryCourseManagerRepositoryTest extends CourseManagerRepositoryTestCase
{
    private ?InMemoryCourseManagerRepository $repository = null;

    protected function getRepository(): CourseManagerRepository
    {
        $this->repository = new InMemoryCourseManagerRepository();

        return $this->repository;
    }

    protected function addItem(CourseManager $courseManager): void
    {
        if (null === $this->repository) {
            $this->fail('Call getRepository() before addItem()');
        }

        $this->repository->add($courseManager);
    }
}
