<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\RouletteWord;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method RouletteWord|null find($id, $lockMode = null, $lockVersion = null)
 * @method RouletteWord|null findOneBy(array $criteria, array $orderBy = null)
 * @method RouletteWord[]    findAll()
 * @method RouletteWord[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RouletteWordRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RouletteWord::class);
    }

    /**
     * Find a RouletteWord entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?RouletteWord
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $rouletteWord = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $rouletteWord;
    }
}
