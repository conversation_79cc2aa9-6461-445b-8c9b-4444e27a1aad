<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Purchase;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\PurchasableItemEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\PurchasableItemFixtureTrait;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetPurchasableItemsControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use PurchasableItemFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        $this->truncateEntities([
            'purchasable_item',
            'course',
        ]);
        parent::tearDown();
    }

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testBasicGetPurchasableItems(): void
    {
        $userToken = $this->loginAndGetToken();

        $purchasableItem = $this->setAndGetPurchasableItemInRepository();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(
                page: 1,
                pageSize: 10
            ),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $decodedResponse = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $decodedResponse);
        $this->assertIsArray($decodedResponse['data']);

        $this->assertArrayHasKey('metadata', $decodedResponse);
        $this->assertArrayHasKey('page', $decodedResponse['metadata']);
        $this->assertArrayHasKey('total_pages', $decodedResponse['metadata']);
        $this->assertArrayHasKey('limit', $decodedResponse['metadata']);
        $this->assertArrayHasKey('total', $decodedResponse['metadata']);

        $firstItem = $decodedResponse['data'][0];
        $this->assertArrayHasKey('id', $firstItem);
        $this->assertArrayHasKey('name', $firstItem);
        $this->assertArrayHasKey('description', $firstItem);
        $this->assertArrayHasKey('price_amount', $firstItem);
        $this->assertArrayHasKey('price_currency', $firstItem);
        $this->assertArrayHasKey('resource_type', $firstItem);
        $this->assertArrayHasKey('resource_id', $firstItem);
        $this->assertArrayHasKey('is_active', $firstItem);
        $this->assertEquals($purchasableItem->getId()->value(), $firstItem['id']);
        $this->assertEquals($purchasableItem->getName(), $firstItem['name']);
        $this->assertEquals($purchasableItem->getDescription(), $firstItem['description']);
        $this->assertEquals($purchasableItem->getPrice()->value(), $firstItem['price_amount']);
        $this->assertEquals($purchasableItem->getPrice()->currency()->code()->name, $firstItem['price_currency']);
        $this->assertEquals(
            ResourceTypeTransformer::toString($purchasableItem->getResource()->getType()),
            $firstItem['resource_type']
        );
        $this->assertEquals($purchasableItem->getResource()->getId()->value(), $firstItem['resource_id']);
        $this->assertEquals($purchasableItem->getIsActive(), $firstItem['is_active']);
    }

    public function testPaginationParameters(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(
                page: 1,
                pageSize: 5
            ),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $content);
        $this->assertIsArray($content['data']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertArrayHasKey('page', $content['metadata']);
        $this->assertArrayHasKey('total_pages', $content['metadata']);
        $this->assertArrayHasKey('limit', $content['metadata']);
        $this->assertArrayHasKey('total', $content['metadata']);

        $this->assertEquals(1, $content['metadata']['page']);
        $this->assertEquals(5, $content['metadata']['limit']);
    }

    #[DataProvider('sortableFieldsProvider')]
    public function testSortableFields(string $sortBy, string $sortDir): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(
                page: 1,
                pageSize: 10,
                sortBy: $sortBy,
                sortDir: $sortDir
            ),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $content);
        $this->assertIsArray($content['data']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertArrayHasKey('page', $content['metadata']);
        $this->assertArrayHasKey('total_pages', $content['metadata']);
        $this->assertArrayHasKey('limit', $content['metadata']);
        $this->assertArrayHasKey('total', $content['metadata']);
    }

    public static function sortableFieldsProvider(): \Generator
    {
        // Test each PurchasableItem-specific sortable field
        yield 'sort by id asc' => ['id', 'asc'];
        yield 'sort by id desc' => ['id', 'desc'];

        yield 'sort by name asc' => ['name', 'asc'];
        yield 'sort by name desc' => ['name', 'desc'];

        yield 'sort by description asc' => ['description', 'asc'];
        yield 'sort by description desc' => ['description', 'desc'];

        yield 'sort by price_amount asc' => ['price_amount', 'asc'];
        yield 'sort by price_amount desc' => ['price_amount', 'desc'];

        yield 'sort by is_active asc' => ['is_active', 'asc'];
        yield 'sort by is_active desc' => ['is_active', 'desc'];

        // Test LifeCycle sortable fields (inherited)
        yield 'sort by created_at asc' => ['created_at', 'asc'];
        yield 'sort by created_at desc' => ['created_at', 'desc'];

        yield 'sort by updated_at asc' => ['updated_at', 'asc'];
        yield 'sort by updated_at desc' => ['updated_at', 'desc'];
    }

    public function testFilterParameters(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(
                page: 1,
                pageSize: 10,
                search: 'test',
                type: 'course',
                isActive: true
            ),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $content);
        $this->assertIsArray($content['data']);
    }

    #[DataProvider('validationErrorProvider')]
    public function testValidationErrors(
        array $queryParams,
        int $expectedStatusCode,
        array $expectedViolations,
    ): void {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemsEndpoint(
                page: $queryParams['page'] ?? null,
                pageSize: $queryParams['page_size'] ?? null,
                sortBy: $queryParams['sort_by'] ?? null,
                sortDir: $queryParams['sort_dir'] ?? null,
                search: $queryParams['search'] ?? null,
                type: $queryParams['type'] ?? null,
                priceMin: $queryParams['price_min'] ?? null,
                priceMax: $queryParams['price_max'] ?? null,
                isActive: isset($queryParams['is_active']) ? (bool) $queryParams['is_active'] : null,
            ),
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);

        if (Response::HTTP_BAD_REQUEST === $expectedStatusCode) {
            $this->assertArrayHasKey('message', $content);

            // Handle different error types
            if (empty($expectedViolations)) {
                // For sorting exceptions, the message contains the invalid field name
                $this->assertNotEquals('Validation failed', $content['message']);
            } else {
                // For validation errors, expect standard validation format
                $this->assertEquals('Validation failed', $content['message']);

                $this->assertArrayHasKey('metadata', $content);
                $this->assertArrayHasKey('violations', $content['metadata']);

                foreach ($expectedViolations as $field => $expectedMessage) {
                    $this->assertArrayHasKey($field, $content['metadata']['violations']);
                    if (\is_array($expectedMessage)) {
                        foreach ($expectedMessage as $message) {
                            $this->assertContains($message, $content['metadata']['violations'][$field]);
                        }
                    } else {
                        $violationMessages = $content['metadata']['violations'][$field];
                        if (\is_array($violationMessages)) {
                            $this->assertContains($expectedMessage, $violationMessages);
                        } else {
                            $this->assertStringContainsString($expectedMessage, $violationMessages);
                        }
                    }
                }
            }
        }
    }

    public static function validationErrorProvider(): \Generator
    {
        yield 'page without page_size' => [
            'queryParams' => ['page' => 1],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[page]' => 'Page size is required when page is provided.',
            ],
        ];

        yield 'invalid page value' => [
            'queryParams' => ['page' => 0, 'page_size' => 10],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[page]' => 'Page must be greater than 0.',
            ],
        ];

        yield 'invalid sort direction' => [
            'queryParams' => ['sort_by' => 'name', 'sort_dir' => 'invalid'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[sort_dir]' => 'The value you selected is not a valid choice.',
            ],
        ];

        yield 'sort direction without sort by' => [
            'queryParams' => ['sort_dir' => 'asc'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[sort_dir]' => 'Sort by is required when sort direction is provided.',
            ],
        ];

        yield 'invalid type' => [
            'queryParams' => ['type' => 'invalid'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[type]' => 'The value you selected is not a valid choice.',
            ],
        ];

        yield 'invalid sort field' => [
            'queryParams' => ['sort_by' => 'invalid_field', 'sort_dir' => 'asc'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [],
        ];
    }
}
