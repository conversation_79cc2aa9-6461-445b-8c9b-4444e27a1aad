<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase;

use App\V2\Domain\Purchase\Exception\PurchaseItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryPurchaseRepository implements PurchaseRepository
{
    private PurchaseCollection $collection;
    private PurchaseItemCollection $purchaseItemsCollection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new PurchaseCollection([]);
        $this->purchaseItemsCollection = new PurchaseItemCollection([]);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function put(Purchase $purchase): void
    {
        $purchases = $this->collection->allIndexedById();
        $purchases[$purchase->getId()->value()] = clone $purchase;
        $this->collection->replace($purchases);

        if ($purchase->getPurchaseItems()) {
            foreach ($purchase->getPurchaseItems()->all() as $item) {
                $this->putPurchaseItem($item);
            }
        }
    }

    /**
     * @throws PurchaseNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOneBy(PurchaseCriteria $criteria): Purchase
    {
        $purchases = $this->filterByCriteria($criteria);

        if ($purchases->isEmpty()) {
            throw new PurchaseNotFoundException();
        }

        return $purchases->first();
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function findBy(PurchaseCriteria $criteria): PurchaseCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function countBy(PurchaseCriteria $criteria): int
    {
        return $this->filterByCriteria($criteria)->count();
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function delete(Purchase $purchase): void
    {
        $purchases = $this->collection->allIndexedById();
        unset($purchases[$purchase->getId()->value()]);
        $this->collection->replace($purchases);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(PurchaseCriteria $criteria): PurchaseCollection
    {
        $collection = $this->collection->filter(
            fn (Purchase $purchase) => (
                null === $criteria->getUserId()
                    || $purchase->getUserId()->equals($criteria->getUserId())
            ) && (
                null === $criteria->getStatus()
                    || $purchase->getStatus() === $criteria->getStatus()
            ) && (
                null === $criteria->getMinAmount()
                    || $purchase->getAmount()->greaterThanOrEqual($criteria->getMinAmount())
            ) && (
                null === $criteria->getMaxAmount()
                    || $purchase->getAmount()->lessThanOrEqual($criteria->getMaxAmount())
            ) && (
                null === $criteria->getStartDate()
                    || $purchase->getCreatedAt()->getTimestamp() >= $criteria->getStartDate()->getTimestamp()
            ) && (
                null === $criteria->getEndDate()
                    || $purchase->getCreatedAt()->getTimestamp() <= $criteria->getEndDate()->getTimestamp()
            )
        );

        /** @var PurchaseCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $collection);

        return $collection->map(
            fn (Purchase $purchase) => new Purchase(
                id: $purchase->getId(),
                userId: $purchase->getUserId(),
                status: $purchase->getStatus(),
                amount: $purchase->getAmount(),
                taxRate: $purchase->getTaxRate(),
                taxAmount: $purchase->getTaxAmount(),
                createdAt: $purchase->getCreatedAt(),
                updatedAt: $purchase->getUpdatedAt(),
                deletedAt: $purchase->getDeletedAt(),
            )
        );
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function putPurchaseItem(PurchaseItem $purchaseItem): void
    {
        $purchaseItems = $this->purchaseItemsCollection->allIndexedById();
        $purchaseItems[$purchaseItem->getId()->value()] = clone $purchaseItem;
        $this->purchaseItemsCollection->replace($purchaseItems);
    }

    /**
     * @throws PurchaseItemNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOnePurchaseItemBy(PurchaseItemCriteria $criteria): ?PurchaseItem
    {
        $purchaseItems = $this->filterPurchaseItemsByCriteria($criteria);

        if ($purchaseItems->isEmpty()) {
            throw new PurchaseItemNotFoundException();
        }

        return $purchaseItems->first();
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function findPurchaseItemsBy(PurchaseItemCriteria $criteria): PurchaseItemCollection
    {
        return $this->filterPurchaseItemsByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function countPurchaseItemsBy(PurchaseItemCriteria $criteria): int
    {
        return $this->filterPurchaseItemsByCriteria($criteria)->count();
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function deletePurchaseItem(PurchaseItem $purchaseItem): void
    {
        $purchaseItems = $this->purchaseItemsCollection->allIndexedById();
        unset($purchaseItems[$purchaseItem->getId()->value()]);
        $this->purchaseItemsCollection->replace($purchaseItems);
    }

    /**
     * @throws CollectionException
     */
    private function filterPurchaseItemsByCriteria(PurchaseItemCriteria $criteria): PurchaseItemCollection
    {
        $purchaseItems = $this->purchaseItemsCollection->filter(
            fn (PurchaseItem $purchaseItem) => (
                null === $criteria->getPurchaseId()
                    || $purchaseItem->getPurchaseId()->equals($criteria->getPurchaseId())
            ) && (
                null === $criteria->getPurchaseIds()
                    || $criteria->getPurchaseIds()->contains($purchaseItem->getPurchaseId())
            ) && (
                null === $criteria->getPurchasableItemId()
                    || $purchaseItem->getPurchasableItemId()->equals($criteria->getPurchasableItemId())
            ) && (
                null === $criteria->getMinPrice()
                    || $purchaseItem->getPrice()->greaterThanOrEqual($criteria->getMinPrice())
            ) && (
                null === $criteria->getMaxPrice()
                    || $purchaseItem->getPrice()->lessThanOrEqual($criteria->getMaxPrice())
            )
        );

        /** @var PurchaseItemCollection $purchaseItems */
        $purchaseItems = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $purchaseItems);

        return $purchaseItems->map(
            fn (PurchaseItem $purchaseItem) => new PurchaseItem(
                id: $purchaseItem->getId(),
                purchaseId: $purchaseItem->getPurchaseId(),
                purchasableItemId: $purchaseItem->getPurchasableItemId(),
                price: $purchaseItem->getPrice(),
            )
        );
    }
}
