<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use App\Tests\Functional\V2\Fixtures\CourseManagerFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

class AnnouncementFormGeneralInfoFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use CourseManagerFixtureTrait;
    use AnnouncementManagerFixtureTrait;

    private User $manager1;
    private User $manager2;

    private Course $course1;
    private Course $course2;
    private Course $course3;

    private Announcement $announcement1;
    private Announcement $announcement2;
    private Announcement $announcement3;

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->manager1 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->manager2 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->course1 = $this->createAndGetCourse(name: 'Course 1', code: 'COURSE-1');
        $this->course2 = $this->createAndGetCourse(name: 'Course 2', code: 'COURSE-2');
        $this->course3 = $this->createAndGetCourse(name: 'Course 3', code: 'COURSE-3');

        $this->announcement1 = $this->createAndGetAnnouncement(course: $this->course1, createdBy: $this->manager1);
        $this->announcement2 = $this->createAndGetAnnouncement(course: $this->course2, createdBy: $this->manager2);
        $this->announcement3 = $this->createAndGetAnnouncement(course: $this->course3, createdBy: $this->manager2);

        $this->setValueAndGetSetting(code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING, value: 'false');
    }

    public function testCanModifyAnyAnnouncementAsAdmin(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            body: [
                'id' => $this->announcement1->getId(),
                'courseId' => $this->course1->getId(),
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testCreateAnnouncementAsAdmin(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            body: [
                'courseId' => $this->course1->getId(),
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testCanModifyAnnouncementAsManagerWithSharingDisabled(): void
    {
        $this->setValueAndGetSetting(code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING, value: 'false');
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'id' => $this->announcement1->getId(),
                'courseId' => $this->course1->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'id' => $this->announcement2->getId(),
                'courseId' => $this->course2->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> is not authorized to perform this action for announcement Course 2:',
            $content['message']
        );
    }

    public function testCanModifyAnnouncementAsManagerWithSharingEnabled(): void
    {
        $this->setValueAndGetSetting(code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING, value: 'true');

        $this->setAndGetAnnouncementManager(
            userId: $this->manager1->getId(),
            announcementId: $this->announcement2->getId(),
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'id' => $this->announcement2->getId(),
                'courseId' => $this->course2->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'id' => $this->announcement3->getId(),
                'courseId' => $this->course3->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> is not authorized to perform this action for announcement Course 3:',
            $content['message']
        );
    }

    public function testCanCreateAnnouncementAsManager(): void
    {
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Test no course assigned
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'courseId' => $this->course2->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        // Test assign a course to the manager
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->manager1->getId()),
            courseId: new Id($this->course3->getId()),
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'courseId' => $this->course2->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> does not have permission to create an announcement for the course: Course 2',
            $content['message']
        );

        // Test the course shared with the manager
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->manager1->getId()),
            courseId: new Id($this->course2->getId()),
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'courseId' => $this->course2->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testUpdateAnnouncementCourse(): void
    {
        $course4 = $this->createAndGetCourse(name: 'Course 4', code: 'COURSE-4');

        // Set the course 3 as shared with the manager
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->manager1->getId()),
            courseId: new Id($this->course3->getId()),
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'id' => $this->announcement1->getId(),
                'courseId' => $course4->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> does not have permission to create an announcement for the course: Course 4',
            $content['message']
        );

        // Share the course number 4
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->manager1->getId()),
            courseId: new Id($course4->getId()),
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGeneralInfoEndpoint(),
            queryParams: [
                'id' => $this->announcement1->getId(),
                'courseId' => $course4->getId(),
            ],
            headers: [
                'Content-Type' => 'multipart/form-data',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING, value: 'false');
        $this->truncateEntities([
            Announcement::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->manager1->getId(),
            $this->manager2->getId(),
        ]);
        parent::tearDown();
    }
}
