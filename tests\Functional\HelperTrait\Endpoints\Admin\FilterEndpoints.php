<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class FilterEndpoints
{
    public static function getFilterCategoriesEndpoint(?string $name = null, ?int $parentId = null): string
    {
        $url = '/api/v2/admin/filters/categories';
        $params = [];
        if (null !== $name) {
            $params['name'] = $name;
        }

        if (null !== $parentId) {
            $params['parent_id'] = $parentId;
        }

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }

    public static function getFilters(
        ?string $search = null,
        ?int $categoryId = null,
        ?int $parentId = null,
    ): string {
        $url = '/api/v2/admin/filters';
        $params = [];

        if (null !== $search) {
            $params['search'] = $search;
        }

        if (null !== $categoryId) {
            $params['category_id'] = $categoryId;
        }

        if (null !== $parentId) {
            $params['parent_id'] = $parentId;
        }

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }
}
