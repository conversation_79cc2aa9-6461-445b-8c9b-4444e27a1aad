<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Annoucement\CronJob;

use App\Entity\ZipFileTask;
use App\Service\Annoucement\CronJob\ZipFileTaskService;
use PHPUnit\Framework\TestCase;

class ZipFileTaskServiceTest extends TestCase
{
    private ZipFileTaskService $zipFileTaskService;

    protected function setUp(): void
    {
        // Create a partial mock to test only the getTaskParams method
        $this->zipFileTaskService = $this->createPartialMock(
            ZipFileTaskService::class,
            []
        );
    }

    /**
     * @dataProvider taskParamsProvider
     */
    public function testGetTaskParamsBackwardCompatibility(array $storedParams, array $expectedResult): void
    {
        // Create mock ZipFileTask
        $zipFileTask = $this->createMock(ZipFileTask::class);
        $zipFileTask->method('getParams')->willReturn($storedParams);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->zipFileTaskService);
        $method = $reflection->getMethod('getTaskParams');
        $method->setAccessible(true);

        $result = $method->invoke($this->zipFileTaskService, $zipFileTask);

        $this->assertEquals($expectedResult, $result);
    }

    public static function taskParamsProvider(): array
    {
        return [
            'legacy_nested_structure' => [
                'storedParams' => [
                    'params' => [
                        'survey' => true,
                        'certificates' => true,
                        'userId' => 123,
                    ],
                ],
                'expectedResult' => [
                    'survey' => true,
                    'certificates' => true,
                    'userId' => 123,
                ],
            ],
            'new_flat_structure' => [
                'storedParams' => [
                    'survey' => true,
                    'certificates' => true,
                    'userId' => 123,
                ],
                'expectedResult' => [
                    'survey' => true,
                    'certificates' => true,
                    'userId' => 123,
                ],
            ],
            'mixed_structure_legacy_takes_precedence' => [
                'storedParams' => [
                    'survey' => false, // This should be ignored
                    'params' => [
                        'survey' => true, // This should be used
                        'certificates' => true,
                    ],
                ],
                'expectedResult' => [
                    'survey' => true,
                    'certificates' => true,
                ],
            ],
            'empty_params' => [
                'storedParams' => [],
                'expectedResult' => [],
            ],
            'empty_nested_params' => [
                'storedParams' => [
                    'params' => [],
                ],
                'expectedResult' => [],
            ],
        ];
    }

    public function testGetTaskParamsWithNullParams(): void
    {
        $zipFileTask = $this->createMock(ZipFileTask::class);
        $zipFileTask->method('getParams')->willReturn([
            'params' => null,
            'survey' => true,
        ]);

        $reflection = new \ReflectionClass($this->zipFileTaskService);
        $method = $reflection->getMethod('getTaskParams');
        $method->setAccessible(true);

        $result = $method->invoke($this->zipFileTaskService, $zipFileTask);

        // When params is null, should fallback to the main array
        $this->assertEquals(['params' => null, 'survey' => true], $result);
    }
}
