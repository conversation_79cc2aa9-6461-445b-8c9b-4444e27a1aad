<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\VirtualMeeting;

use App\V2\Domain\VirtualMeeting\VirtualMeeting;

class VirtualMeetingTransformer
{
    public static function fromVirtualMeetingToArray(VirtualMeeting $virtualMeeting): array
    {
        return [
            'type' => VirtualMeetingTypeTransformer::fromVirtualMeetingTypeToString($virtualMeeting->getType()),
            'url' => $virtualMeeting->getUrl(),
        ];
    }
}
