<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\User;

use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Filter;

class FilterMother
{
    public static function create(
        ?Id $id = null,
        ?string $name = null,
        ?Id $categoryId = null,
    ): Filter {
        return new Filter(
            id: $id ?? new Id(1),
            name: $name ?? 'Filter 1',
            categoryId: $categoryId ?? new Id(1),
        );
    }
}
