<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\Entity\User;
use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Id\Id;

readonly class PutAnnouncementManager implements Command
{
    public function __construct(
        private Id $announcementId,
        private Id $userId,
        private User $requestUser,
    ) {
    }

    public function getAnnouncementId(): Id
    {
        return $this->announcementId;
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getRequestUser(): User
    {
        return $this->requestUser;
    }
}
