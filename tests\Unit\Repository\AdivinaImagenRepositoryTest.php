<?php

declare(strict_types=1);

namespace App\Tests\Unit\Repository;

use App\Entity\AdivinaImagen;
use App\Repository\AdivinaImagenRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\FilterCollection;
use PHPUnit\Framework\TestCase;

class AdivinaImagenRepositoryTest extends TestCase
{
    private const int VALID_ADIVINA_IMAGEN_ID = 1;
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function testFindWithDeletedDisablesAndEnablesSoftDeleteFilter(): void
    {
        $adivinaImagen = new AdivinaImagen();
        $filterCollection = $this->createMock(FilterCollection::class);
        $entityManager = $this->createMock(EntityManagerInterface::class);

        $repository = $this->createPartialMock(
            AdivinaImagenRepository::class,
            ['getEntityManager', 'findOneBy']
        );

        $repository->expects($this->once())
            ->method('getEntityManager')
            ->willReturn($entityManager);

        $entityManager->expects($this->once())
            ->method('getFilters')
            ->willReturn($filterCollection);

        $filterCollection->expects($this->once())
            ->method('disable')
            ->with(self::SOFT_DELETE_FILTER);

        $repository->expects($this->once())
            ->method('findOneBy')
            ->with(criteria: ['id' => self::VALID_ADIVINA_IMAGEN_ID])
            ->willReturn($adivinaImagen);

        $filterCollection->expects($this->once())
            ->method('enable')
            ->with(self::SOFT_DELETE_FILTER);

        $result = $repository->findWithDeleted(self::VALID_ADIVINA_IMAGEN_ID);

        $this->assertSame($adivinaImagen, $result);
    }

    public function testFindWithDeletedReturnsNullWhenEntityNotFound(): void
    {
        $filterCollection = $this->createMock(FilterCollection::class);
        $entityManager = $this->createMock(EntityManagerInterface::class);

        $repository = $this->createPartialMock(
            AdivinaImagenRepository::class,
            ['getEntityManager', 'findOneBy']
        );

        $repository->expects($this->once())
            ->method('getEntityManager')
            ->willReturn($entityManager);

        $entityManager->expects($this->once())
            ->method('getFilters')
            ->willReturn($filterCollection);

        $filterCollection->expects($this->once())
            ->method('disable')
            ->with(self::SOFT_DELETE_FILTER);

        $repository->expects($this->once())
            ->method('findOneBy')
            ->with(criteria: ['id' => self::VALID_ADIVINA_IMAGEN_ID])
            ->willReturn(null);

        $filterCollection->expects($this->once())
            ->method('enable')
            ->with(self::SOFT_DELETE_FILTER);

        $result = $repository->findWithDeleted(self::VALID_ADIVINA_IMAGEN_ID);

        $this->assertNull($result);
    }

    public function testFindWithDeletedEnsuresFilterIsReenabledOnException(): void
    {
        $filterCollection = $this->createMock(FilterCollection::class);
        $entityManager = $this->createMock(EntityManagerInterface::class);

        $repository = $this->createPartialMock(
            AdivinaImagenRepository::class,
            ['getEntityManager', 'findOneBy']
        );

        $repository->expects($this->once())
            ->method('getEntityManager')
            ->willReturn($entityManager);

        $entityManager->expects($this->once())
            ->method('getFilters')
            ->willReturn($filterCollection);

        $filterCollection->expects($this->once())
            ->method('disable')
            ->with(self::SOFT_DELETE_FILTER);

        $repository->expects($this->once())
            ->method('findOneBy')
            ->with(criteria: ['id' => self::VALID_ADIVINA_IMAGEN_ID])
            ->willThrowException(new \Exception('Database error'));

        $filterCollection->expects($this->once())
            ->method('enable')
            ->with(self::SOFT_DELETE_FILTER);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $repository->findWithDeleted(self::VALID_ADIVINA_IMAGEN_ID);
    }
}
