<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;

class PurchasableItemCriteria extends CriteriaWithUuid
{
    private ?string $search = null;
    private ?Resource $resource = null;
    private ?ResourceType $resourceType = null;
    private ?Money $minPrice = null;
    private ?Money $maxPrice = null;
    private ?bool $isActive = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->search
            && null === $this->resource
            && null === $this->resourceType
            && null === $this->minPrice
            && null === $this->maxPrice
            && null === $this->isActive;
    }

    public function filterBySearch(string $search): self
    {
        $this->search = $search;

        return $this;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }

    public function filterByResource(Resource $resource): self
    {
        $this->resource = $resource;

        return $this;
    }

    public function getResource(): ?Resource
    {
        return $this->resource;
    }

    public function filterByResourceType(ResourceType $resourceType): self
    {
        $this->resourceType = $resourceType;

        return $this;
    }

    public function getResourceType(): ?ResourceType
    {
        return $this->resourceType;
    }

    public function filterByMinPrice(Money $minPrice): self
    {
        $this->minPrice = $minPrice;

        return $this;
    }

    public function getMinPrice(): ?Money
    {
        return $this->minPrice;
    }

    public function filterByMaxPrice(Money $maxPrice): self
    {
        $this->maxPrice = $maxPrice;

        return $this;
    }

    public function getMaxPrice(): ?Money
    {
        return $this->maxPrice;
    }

    public function filterByIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }
}
