<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\User;

use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;

class UserFilterTransformer
{
    public static function toArray(UserFilter $userFilter): array
    {
        if (null === $userFilter->getFilter()) {
            return ['id' => $userFilter->getFilterId()->value()];
        }

        return [
            'id' => $userFilter->getFilter()->getId()->value(),
            'name' => $userFilter->getFilter()->getName(),
            'category_id' => $userFilter->getFilter()->getCategoryId()->value(),
        ];
    }

    public static function fromCollectionToArray(UserFilterCollection $collection): array
    {
        return array_map(
            fn (UserFilter $userFilter) => self::toArray($userFilter),
            $collection->all(),
        );
    }
}
