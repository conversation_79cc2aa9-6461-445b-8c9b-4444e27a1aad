<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use App\Tests\Functional\V2\Fixtures\FilterFixtureTrait;
use App\Tests\Functional\V2\Fixtures\ManagerFilterFixtureTrait;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class DeleteManagerFiltersFunctionalTest extends FunctionalTestCase
{
    use FilterFixtureTrait;
    use ManagerFilterFixtureTrait;

    private const string EMAIL_MANAGER = '<EMAIL>';
    private ?User $manager;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->manager = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: self::EMAIL_MANAGER);
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1)
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(-1),
            body: json_encode([1, 2, 3]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ], $content['metadata']);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(1),
            body: json_encode([0, -1, 'aA']),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[0]' => 'This value should be greater than or equal to 1.',
                '[1]' => 'This value should be greater than or equal to 1.',
                '[2]' => 'This value should be of type integer.',
            ],
        ], $content['metadata']);
    }

    public function testUserNotFound(): void
    {
        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint(999),
            body: json_encode([1, 2, 3]),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'User not found',
            $content['message'],
        );
    }

    /**
     * @throws InfrastructureException
     */
    public function testDeleteManagerFilters(): void
    {
        $token = $this->loginAndGetToken();

        // Try filter that does not exist
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            body: json_encode([1, 2, 3]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'At least one of the filters does not exist',
            $content['message'],
        );

        $this->setAndGetFilterInRepository(id: new Id(1), filterCategoryId: new Id(1), name: 'Filter 1');
        $this->setAndGetFilterInRepository(id: new Id(2), filterCategoryId: new Id(1), name: 'Filter 2');
        $this->setAndGetFilterInRepository(id: new Id(3), filterCategoryId: new Id(1), name: 'Filter 3');

        // Try to remove unassigned filter
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            body: json_encode([1, 2, 3]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'At least one of the filters is not assigned to a user',
            $content['message'],
        );

        // Init and remove
        $this->setAndGetManagerFilterInRepository(userId: new Id($this->manager->getId()), filterId: new Id(1));
        $this->setAndGetManagerFilterInRepository(userId: new Id($this->manager->getId()), filterId: new Id(2));
        $this->setAndGetManagerFilterInRepository(userId: new Id($this->manager->getId()), filterId: new Id(3));

        /** @var ManagerFilterRepository $repository */
        $repository = $this->getManagerFilterRepository();
        $result = $repository
            ->findBy(ManagerFilterCriteria::createEmpty()->filterByUserId(new Id($this->manager->getId())));
        $this->assertCount(3, $result);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminUsersEndpoints::managerFiltersEndpoint($this->manager->getId()),
            body: json_encode([1, 3]),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $result = $repository
            ->findBy(ManagerFilterCriteria::createEmpty()->filterByUserId(new Id($this->manager->getId())));
        $this->assertCount(1, $result);
        $this->assertEquals(2, $result->first()->getFilterId()->value());
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([$this->manager->getId()]);
        parent::tearDown();
    }
}
