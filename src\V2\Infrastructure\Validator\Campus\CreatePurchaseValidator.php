<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Campus;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;

class CreatePurchaseValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validatePostPurchasableItems(array $data): void
    {
        // First validate that the data structure is correct
        $structureConstraints = [
            new Constraints\NotBlank(message: 'Body must not be blank'),
            new Constraints\Type('array'),
            new Constraints\Collection([
                'fields' => [
                    'items' => [
                        new Constraints\NotBlank(message: 'Items field is required'),
                        new Constraints\Type('array', message: 'Items must be an array'),
                        new Constraints\Count(min: 1, minMessage: 'At least one item is required'),
                        new Constraints\All([
                            new Constraints\Type('string'),
                            new Constraints\NotBlank(),
                            new Constraints\Uuid(versions: [Constraints\Uuid::V4_RANDOM]),
                        ]),
                    ],
                ],
                'allowExtraFields' => false,
            ]),
        ];

        parent::validate($data, $structureConstraints);
    }
}
