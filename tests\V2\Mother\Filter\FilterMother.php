<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Filter;

use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Shared\Id\Id;

class FilterMother
{
    public const string DEFAULT_NAME = 'Filter 1';
    public const string DEFAULT_CODE = 'FILTER-1';

    public static function create(
        ?Id $id = null,
        ?Id $filterCategoryId = null,
        ?string $name = null,
        ?string $code = null,
        ?int $sort = null,
        ?Id $parentId = null,
    ): Filter {
        return new Filter(
            id: $id ?? new Id(random_int(1, 10)),
            filterCategoryId: $filterCategoryId ?? new Id(random_int(1, 10)),
            name: $name ?? self::DEFAULT_NAME,
            code: $code ?? self::DEFAULT_CODE,
            sort: $sort ?? 0,
            parentId: $parentId,
        );
    }
}
