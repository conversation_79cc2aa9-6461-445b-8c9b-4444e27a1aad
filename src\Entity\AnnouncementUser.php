<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\AnnouncementUserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Class AnnouncementUser.
 *
 * @ORM\Entity(repositoryClass=AnnouncementUserRepository::class)
 *
 * @ORM\Table(name="announcement_user")
 */
class AnnouncementUser
{
    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"announcement","forum"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="Announcement", inversedBy="called")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $announcement;

    /**
     * @ORM\ManyToOne(targetEntity="User", inversedBy="announcements")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"announcement"})
     */
    private $user;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     *
     * @Groups({"announcement"})
     */
    private $notified;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementGroup::class, inversedBy="announcementUsers")
     */
    private $announcementGroup;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isAproved;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $dateApproved;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isDownloadDiploma;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isReadDidacticGuide;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $dateReadDidacticGuide;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isDownloadDidacticGuide;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $dateDownloadDidacticGuide;

    /**
     * @ORM\OneToMany(
     *     targetEntity=ClassroomvirtualUser::class, mappedBy="announcementuser", cascade={"persist", "remove"}
     *     )
     */
    private $classroomvirtualUsers;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $valuedCourseAt;

    /**
     * @ORM\ManyToOne(targetEntity=UserCompany::class, inversedBy="announcementUsers")
     */
    private $userCompany;

    /**
     * @ORM\ManyToOne(targetEntity=UserProfessionalCategory::class, inversedBy="announcementUsers")
     */
    private $userProfessionalCategory;

    /**
     * @ORM\ManyToOne(targetEntity=UserWorkCenter::class, inversedBy="announcementUsers")
     */
    private $userWorkCenter;

    /**
     * @ORM\ManyToOne(targetEntity=UserWorkDepartment::class, inversedBy="announcementUsers")
     */
    private $userWorkDepartment;

    /**
     * @ORM\ManyToOne(targetEntity=UserStudyLevel::class, inversedBy="announcementUsers")
     */
    private $userStudyLevel;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isConfirmationAssistance;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $dateConfirmationAssistance;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $external = false;

    /**
     * @ORM\OneToOne(targetEntity=AnnouncementUserDigitalSignature::class, mappedBy="announcementUser", cascade={"persist", "remove"})
     */
    private $announcementGroupSession;

    public function __construct()
    {
        $this->classroomvirtualUsers = new ArrayCollection();
        $this->isConfirmationAssistance = false;
    }

    public function __toString(): string
    {
        return (string) $this->id;
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement($announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getNotified(): ?\DateTimeInterface
    {
        return $this->notified;
    }

    public function setNotified(\DateTimeInterface $notified): self
    {
        $this->notified = $notified;

        return $this;
    }

    public function getAnnouncementGroup(): ?AnnouncementGroup
    {
        return $this->announcementGroup;
    }

    public function setAnnouncementGroup(?AnnouncementGroup $announcementGroup): self
    {
        $this->announcementGroup = $announcementGroup;

        return $this;
    }

    public function isAproved(): ?bool
    {
        return $this->isAproved;
    }

    public function setAproved(?bool $isAproved): self
    {
        $this->isAproved = $isAproved;

        return $this;
    }

    public function getDateApproved(): ?\DateTimeInterface
    {
        return $this->dateApproved;
    }

    public function setDateApproved(?\DateTimeInterface $dateApproved): self
    {
        $this->dateApproved = $dateApproved;

        return $this;
    }

    public function isDownloadDiploma(): ?bool
    {
        return $this->isDownloadDiploma;
    }

    public function setDownloadDiploma(?bool $isDownloadDiploma): self
    {
        $this->isDownloadDiploma = $isDownloadDiploma;

        return $this;
    }

    public function isReadDidacticGuide(): ?bool
    {
        return $this->isReadDidacticGuide;
    }

    public function setReadDidacticGuide(?bool $isReadDidacticGuide): self
    {
        $this->isReadDidacticGuide = $isReadDidacticGuide;

        return $this;
    }

    public function getDateReadDidacticGuide(): ?\DateTimeInterface
    {
        return $this->dateReadDidacticGuide;
    }

    public function setDateReadDidacticGuide(?\DateTimeInterface $dateReadDidacticGuide): self
    {
        $this->dateReadDidacticGuide = $dateReadDidacticGuide;

        return $this;
    }

    public function isDownloadDidacticGuide(): ?bool
    {
        return $this->isDownloadDidacticGuide;
    }

    public function setDownloadDidacticGuide(?bool $isDownloadDidacticGuide): self
    {
        $this->isDownloadDidacticGuide = $isDownloadDidacticGuide;

        return $this;
    }

    public function getDateDownloadDidacticGuide(): ?\DateTimeInterface
    {
        return $this->dateDownloadDidacticGuide;
    }

    public function setDateDownloadDidacticGuide(?\DateTimeInterface $dateDownloadDidacticGuide): self
    {
        $this->dateDownloadDidacticGuide = $dateDownloadDidacticGuide;

        return $this;
    }

    /**
     * @return Collection<int, ClassroomvirtualUser>
     */
    public function getClassroomvirtualUsers(): Collection
    {
        return $this->classroomvirtualUsers;
    }

    public function addClassroomvirtualUser(ClassroomvirtualUser $classroomvirtualUser): self
    {
        if (!$this->classroomvirtualUsers->contains($classroomvirtualUser)) {
            $this->classroomvirtualUsers[] = $classroomvirtualUser;
            $classroomvirtualUser->setAnnouncementuser($this);
        }

        return $this;
    }

    public function removeClassroomvirtualUser(ClassroomvirtualUser $classroomvirtualUser): self
    {
        if ($this->classroomvirtualUsers->removeElement($classroomvirtualUser)) {
            // set the owning side to null (unless already changed)
            if ($classroomvirtualUser->getAnnouncementuser() === $this) {
                $classroomvirtualUser->setAnnouncementuser(null);
            }
        }

        return $this;
    }

    public function getValuedCourseAt(): ?\DateTimeImmutable
    {
        return $this->valuedCourseAt;
    }

    public function setValuedCourseAt(?\DateTimeImmutable $valuedCourseAt): self
    {
        $this->valuedCourseAt = $valuedCourseAt;

        return $this;
    }

    public function getUserCompany(): ?UserCompany
    {
        return $this->userCompany;
    }

    public function setUserCompany(?UserCompany $userCompany): self
    {
        $this->userCompany = $userCompany;

        return $this;
    }

    public function getUserProfessionalCategory(): ?UserProfessionalCategory
    {
        return $this->userProfessionalCategory;
    }

    public function setUserProfessionalCategory(?UserProfessionalCategory $userProfessionalCategory): self
    {
        $this->userProfessionalCategory = $userProfessionalCategory;

        return $this;
    }

    public function getUserWorkCenter(): ?UserWorkCenter
    {
        return $this->userWorkCenter;
    }

    public function setUserWorkCenter(?UserWorkCenter $userWorkCenter): self
    {
        $this->userWorkCenter = $userWorkCenter;

        return $this;
    }

    public function getUserWorkDepartment(): ?UserWorkDepartment
    {
        return $this->userWorkDepartment;
    }

    public function setUserWorkDepartment(?UserWorkDepartment $userWorkDepartment): self
    {
        $this->userWorkDepartment = $userWorkDepartment;

        return $this;
    }

    public function getUserStudyLevel(): ?UserStudyLevel
    {
        return $this->userStudyLevel;
    }

    public function setUserStudyLevel(?UserStudyLevel $userStudyLevel): self
    {
        $this->userStudyLevel = $userStudyLevel;

        return $this;
    }

    public function isIsConfirmationAssistance(): ?bool
    {
        return $this->isConfirmationAssistance ?? false;
    }

    public function setIsConfirmationAssistance(?bool $isConfirmationAssistance): self
    {
        $this->isConfirmationAssistance = $isConfirmationAssistance;

        return $this;
    }

    public function getDateConfirmationAssistance(): ?\DateTimeInterface
    {
        return $this->dateConfirmationAssistance;
    }

    public function setDateConfirmationAssistance(?\DateTimeInterface $dateConfirmationAssistance): self
    {
        $this->dateConfirmationAssistance = $dateConfirmationAssistance;

        return $this;
    }

    public function isExternal(): ?bool
    {
        return $this->external;
    }

    public function setExternal(bool $external): self
    {
        $this->external = $external;

        return $this;
    }

    public function getAnnouncementGroupSession(): ?AnnouncementUserDigitalSignature
    {
        return $this->announcementGroupSession;
    }

    public function setAnnouncementGroupSession(?AnnouncementUserDigitalSignature $announcementGroupSession): self
    {
        // unset the owning side of the relation if necessary
        if (null === $announcementGroupSession && null !== $this->announcementGroupSession) {
            $this->announcementGroupSession->setAnnouncementUser(null);
        }

        // set the owning side of the relation if necessary
        if (null !== $announcementGroupSession && $announcementGroupSession->getAnnouncementUser() !== $this) {
            $announcementGroupSession->setAnnouncementUser($this);
        }

        $this->announcementGroupSession = $announcementGroupSession;

        return $this;
    }

    public function __clone()
    {
        $this->id = null;
        $this->announcementGroup = null;
        $this->announcementGroupSession = null;
        $this->classroomvirtualUsers = new ArrayCollection();
        $this->isAproved = null;
        $this->dateApproved = null;
        $this->isDownloadDiploma = null;
        $this->isReadDidacticGuide = null;
        $this->dateReadDidacticGuide = null;
        $this->isDownloadDidacticGuide = null;
        $this->dateDownloadDidacticGuide = null;
        $this->valuedCourseAt = null;
    }
}
