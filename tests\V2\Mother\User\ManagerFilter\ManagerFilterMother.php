<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\User\ManagerFilter;

use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;

class ManagerFilterMother
{
    public static function create(
        ?Id $userId = null,
        ?Id $filterId = null,
    ): ManagerFilter {
        return new ManagerFilter(
            userId: $userId ?? new Id(random_int(1, 50)),
            filterId: $filterId ?? new Id(random_int(1, 50)),
        );
    }
}
