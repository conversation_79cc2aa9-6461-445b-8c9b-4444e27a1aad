<?php

declare(strict_types=1);

namespace App\Tests\Unit\V2\Application\CommandHandler;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\User;
use App\Repository\AnnouncementGroupRepository;
use App\Repository\AnnouncementRepository;
use App\Repository\AnnouncementUserRepository;
use App\Tests\Mother\Entity\AnnouncementGroupMother;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Application\Command\PostAnnouncementGroupUserCommand;
use App\V2\Application\CommandHandler\PostAnnouncementGroupUserCommandHandler;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationServiceInterface;
use App\V2\Domain\Announcement\Exception\AnnouncementGroupNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Exception\PostAnnouncementGroupUserException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PostAnnouncementGroupUserCommandHandlerTest extends TestCase
{
    private PostAnnouncementGroupUserCommandHandler $handler;
    private AnnouncementRepository|MockObject $announcementRepository;
    private AnnouncementGroupRepository|MockObject $announcementGroupRepository;
    private AnnouncementUserRepository|MockObject $announcementUserRepository;
    private UserRepository|MockObject $userRepository;
    private AnnouncementAuthorizationServiceInterface|MockObject $announcementAuthorizationService;

    protected function setUp(): void
    {
        $this->announcementRepository = $this->createMock(AnnouncementRepository::class);
        $this->announcementGroupRepository = $this->createMock(AnnouncementGroupRepository::class);
        $this->announcementUserRepository = $this->createMock(AnnouncementUserRepository::class);
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->announcementAuthorizationService = $this->createMock(AnnouncementAuthorizationServiceInterface::class);

        $this->handler = new PostAnnouncementGroupUserCommandHandler(
            announcementRepository: $this->announcementRepository,
            announcementGroupRepository: $this->announcementGroupRepository,
            announcementUserRepository: $this->announcementUserRepository,
            userRepository: $this->userRepository,
            announcementAuthorizationService: $this->announcementAuthorizationService,
        );
    }

    public function testHandleThrowsExceptionWhenAnnouncementNotFound(): void
    {
        $command = $this->createCommand();

        $this->announcementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn(null);

        $this->expectException(AnnouncementNotFoundException::class);

        $this->handler->handle($command);
    }

    public function testHandleThrowsExceptionWhenGroupNotFound(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);

        $this->announcementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->announcementGroupRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 2, 'announcement' => 1])
            ->willReturn(null);

        $this->expectException(AnnouncementGroupNotFoundException::class);

        $this->handler->handle($command);
    }

    public function testHandleThrowsExceptionWhenGroupDoesNotBelongToAnnouncement(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);

        $this->announcementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->announcementGroupRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 2, 'announcement' => 1])
            ->willReturn(null);

        $this->expectException(AnnouncementGroupNotFoundException::class);

        $this->handler->handle($command);
    }

    public function testHandleThrowsExceptionWhenUserNotAuthorized(): void
    {
        $command = $this->createCommand(isAdmin: false, isManager: false);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $group = AnnouncementGroupMother::create(id: 2, announcement: $announcement);

        $this->announcementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->announcementGroupRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 2, 'announcement' => 1])
            ->willReturn($group);

        $this->announcementAuthorizationService->expects($this->once())
            ->method('ensureUserCanManageAnnouncement')
            ->willThrowException(
                ManagerNotAuthorizedException::userNotAuthorized(
                    announcement: $announcement,
                    user: '<EMAIL>',
                )
            );

        $this->expectException(ManagerNotAuthorizedException::class);

        $this->handler->handle($command);
    }

    public function testHandleThrowsExceptionWhenUserAlreadyInGroup(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $group = AnnouncementGroupMother::create(id: 2, announcement: $announcement);
        $targetUser = UserMother::create(id: 3);
        $existingAnnouncementUser = $this->createMock(AnnouncementUser::class);

        $this->stubRepositories(announcement: $announcement, group: $group, targetUser: $targetUser);

        $this->announcementUserRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'announcement' => $announcement,
                'user' => $targetUser,
            ])
            ->willReturn($existingAnnouncementUser);

        $this->expectExceptionObject(PostAnnouncementGroupUserException::userAlreadyInAnnouncement());

        $this->handler->handle($command);
    }

    public function testHandleThrowsExceptionWhenMaxGroupSizeReached(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course, usersPerGroup: 2);
        $group = AnnouncementGroupMother::create(id: 2, announcement: $announcement);
        $targetUser = UserMother::create(id: 3);

        $this->stubRepositories(announcement: $announcement, group: $group, targetUser: $targetUser);

        // User not already in group
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'announcement' => $announcement,
                'user' => $targetUser,
            ])
            ->willReturn(null);

        // Group is at capacity
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('count')
            ->with(['announcementGroup' => $group])
            ->willReturn(2);

        $this->expectExceptionObject(PostAnnouncementGroupUserException::maxGroupSizeReached());

        $this->handler->handle($command);
    }

    public function testHandleSuccessfullyCreatesAnnouncementUser(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course, usersPerGroup: 10);
        $group = AnnouncementGroupMother::create(id: 2, announcement: $announcement);
        $targetUser = UserMother::create(id: 3);

        $this->stubRepositories(announcement: $announcement, group: $group, targetUser: $targetUser);

        // User not already in group
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'announcement' => $announcement,
                'user' => $targetUser,
            ])
            ->willReturn(null);

        // Group has space
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('count')
            ->with(['announcementGroup' => $group])
            ->willReturn(5);

        $this->announcementUserRepository
            ->expects($this->once())
            ->method('persist')
            ->with($this->isInstanceOf(AnnouncementUser::class));

        $this->handler->handle($command);
    }

    private function stubRepositories(
        Announcement $announcement,
        AnnouncementGroup $group,
        User $targetUser
    ): void {
        $this->announcementRepository
            ->method('findOneBy')
            ->with(['id' => $announcement->getId()])
            ->willReturn($announcement);

        $this->announcementGroupRepository
            ->method('findOneBy')
            ->with(['id' => $group->getId(), 'announcement' => $announcement->getId()])
            ->willReturn($group);

        $this->userRepository
            ->method('findOneBy')
            ->willReturn($targetUser);
    }

    private function createCommand(
        int $announcementId = 1,
        int $groupId = 2,
        int $userId = 3,
        bool $isAdmin = true,
        bool $isManager = false
    ): PostAnnouncementGroupUserCommand {
        $roles = [];
        if ($isAdmin) {
            $roles[] = User::ROLE_ADMIN;
        }
        if ($isManager) {
            $roles[] = User::ROLE_MANAGER;
        }

        $requestUser = UserMother::create(
            id: 999,
            roles: $roles
        );

        return new PostAnnouncementGroupUserCommand(
            announcementId: new Id($announcementId),
            groupId: new Id($groupId),
            userId: new Id($userId),
            requestUser: $requestUser,
        );
    }

    public function testHandleThrowsExceptionWhenMaxGroupSizeReachedWithSpecificCount(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course, usersPerGroup: 25);
        $group = AnnouncementGroupMother::create(id: 2, announcement: $announcement);
        $targetUser = UserMother::create(id: 3);

        $this->stubRepositories(announcement: $announcement, group: $group, targetUser: $targetUser);

        // User not already in group
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'announcement' => $announcement,
                'user' => $targetUser,
            ])
            ->willReturn(null);

        // Mock group size validation - simulate group with 25 users (max reached)
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('count')
            ->with(['announcementGroup' => $group])
            ->willReturn(25);

        $this->expectExceptionObject(PostAnnouncementGroupUserException::maxGroupSizeReached());

        $this->handler->handle($command);
    }

    public function testHandleAllowsUserWhenGroupSizeUnderLimit(): void
    {
        $command = $this->createCommand();
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course, usersPerGroup: 25);
        $group = AnnouncementGroupMother::create(id: 2, announcement: $announcement);
        $targetUser = UserMother::create(id: 3);

        $this->stubRepositories(announcement: $announcement, group: $group, targetUser: $targetUser);

        // User not already in group
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'announcement' => $announcement,
                'user' => $targetUser,
            ])
            ->willReturn(null);

        // Mock group size validation - simulate group with 20 users (under limit)
        $this->announcementUserRepository
            ->expects($this->once())
            ->method('count')
            ->with(['announcementGroup' => $group])
            ->willReturn(20);

        $this->announcementUserRepository
            ->expects($this->once())
            ->method('persist')
            ->with($this->isInstanceOf(AnnouncementUser::class));

        $this->handler->handle($command);
    }
}
