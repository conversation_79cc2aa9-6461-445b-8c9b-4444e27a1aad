import Vue from 'vue';

export const ROLE_LIST = {
    SUPER_ADMIN: 'ROLE_SUPER_ADMIN',
    ADMIN: 'ROLE_ADMIN',
    TUTOR: 'ROLE_TUTOR',
    MANAGER: 'ROLE_MANAGER',
    CREATOR: 'ROLE_CREATOR',
    MANAGER_EDITOR: 'ROLE_MANAGER_EDITOR',
    TEAM_MANAGER: 'ROLE_TEAM_MANAGER',
    USER: 'ROLE_USER',
    SUBSIDIZER: 'ROLE_SUBSIDIZER',
    INSPECTOR: 'ROLE_INSPECTOR',
    DEVELOPER: 'ROLE_DEVELOPER',
    SUPPORT: 'ROLE_SUPPORT',
}
export class Auth {
    #roles = [];
    #permissionList = {};
    #permissionSize = 0;
    #user = undefined;
    #isSuperAdmin = false;
    #isAdmin = false;
    #isTutor = false;
    #isManager = false;
    #isCreator = false;

    #hasRole(role) {
        const roles = this.#roles;

        for (let i = 0; i < roles.length; i++) {
            if (roles[i] === role) {
                return true;
            }
        }

        return false;
    }

    setPermissionList(permissions = {}) {
        this.#permissionList = permissions || {};
        this.#permissionSize = Object.keys(this.#permissionList).length
    }

    hasPermission(action = '') {
        if (this.#isSuperAdmin || this.#isAdmin || !this.#permissionSize) return true;
        return this.#roles.some(role => (this.#permissionList[role] || []).some(item => item === action))
    }

    getUser() {
        return this.#user;
    }

    isGranted(attribute) {
        if (this.#isSuperAdmin || this.#isAdmin) return true;// To avoid time n calls when searching in hierarchy

        // Validate if is Authenticated
        if ((typeof attribute === 'string' || attribute instanceof String) && this.#hasRole(attribute)) return true;

        if (Array.isArray(attribute) || attribute instanceof Array) {
            for (let i = 0; i < attribute.length; i++) {
                if (this.#hasRole(attribute[i])) return true;
            }
        }

        return false;
    }

    isCreator() {
        return this.#isCreator;
    }

    isTutor() {
        return this.#isTutor;
    }

    isManager() {
        return this.#isManager;
    }

    isAdmin()
    {
        return this.#isAdmin;
    }

    isSuperAdmin()
    {
        return this.#isSuperAdmin;
    }

    initFromJwt(token) {
        const split = token.split('.');
        if (split.length < 2) return;
        let base64Url = split[1];
        let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        let jsonPayload = decodeURIComponent(window.atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        let parsed = JSON.parse(jsonPayload);

        let roles = [];


        if ('roles' in parsed) roles = parsed.roles;

        const rolesTemp = [...new Set(roles)];

        // Make sure the user has the role without searching in the hierarchy
        this.#isSuperAdmin = rolesTemp.find(r => r === ROLE_LIST.SUPER_ADMIN) !== undefined;
        this.#isAdmin = rolesTemp.find(r => r === ROLE_LIST.ADMIN) !== undefined;
        this.#isTutor = rolesTemp.find(r => r === ROLE_LIST.TUTOR) !== undefined;
        this.#isManager = rolesTemp.find(r => r === ROLE_LIST.MANAGER) !== undefined;
        this.#isCreator = rolesTemp.find(r => r === ROLE_LIST.CREATOR) !== undefined;

        /**
         * Used for user access not user role type
         */
        if ('roleHierarchy' in parsed) {
            const hierarchy = parsed.roleHierarchy;
            const keys = Object.keys(hierarchy);

            // Add hierarchy roles to roles, to avoid multiple calls
            for (let i = 0; i < roles.length; i++) {
                const found = keys.find(k => k === roles[i]);
                if (!found) continue;
                roles = roles.concat(hierarchy[found]);
            }
        }
        if ('user' in parsed) this.#user = parsed.user;

        this.#roles = [...new Set(roles)];
    }
}

export default function initAuthVerification() {
    const auth = new Auth();
    Vue.prototype.$auth = auth;
    Vue.prototype.$isGranted = function (attribute) {
        return this.$auth.isGranted(attribute);
    }

    Vue.prototype.$getUser = function () {
        return this.$auth.getUser();
    }

    Vue.prototype.$isCreator = function () {
        return this.$auth.isSuperAdmin() || this.$auth.isCreator();
    }

    Vue.prototype.$isTutor = function () {
        return this.$auth.isSuperAdmin() || this.$auth.isTutor();
    }

    Vue.prototype.$isManager = function () {
        return this.$auth.isSuperAdmin() || this.$auth.isManager();
    }

    Vue.prototype.$isAdmin = function () {
        return this.$auth.isSuperAdmin() || this.$auth.isAdmin();
    }

    Vue.prototype.$isSuperAdmin = function () {
        return this.$auth.isSuperAdmin();
    }
    return auth;
}
