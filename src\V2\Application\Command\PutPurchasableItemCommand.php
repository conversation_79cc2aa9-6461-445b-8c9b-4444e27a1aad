<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;

readonly class PutPurchasableItemCommand implements Command
{
    public function __construct(
        private Resource $resource,
        private Money $price,
    ) {
    }

    public function getResource(): Resource
    {
        return $this->resource;
    }

    public function getPrice(): Money
    {
        return $this->price;
    }
}
