<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\User;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Filter\FilterMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Application\Query\Admin\GetFiltersQuery;
use App\V2\Application\QueryHandler\Admin\GetFiltersQueryHandler;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class GetFiltersQueryHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?FilterRepository $filterRepository = null,
        ?ManagerFilterRepository $managerFilterRepository = null,
    ): GetFiltersQueryHandler {
        return new GetFiltersQueryHandler(
            filterRepository: $filterRepository ?? $this->createMock(FilterRepository::class),
            managerFilterRepository: $managerFilterRepository ?? $this->createMock(ManagerFilterRepository::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testAsManager(): void
    {
        $user = UserMother::create(id: 2, roles: [User::ROLE_MANAGER]);

        $filterCollection = new FilterCollection([
            FilterMother::create(
                id: new Id(1),
                name: 'Filter 1',
            ),
            FilterMother::create(
                id: new Id(3),
                name: 'Filter 1',
            ),
        ]);

        $managerFilters = new ManagerFilterCollection([
            ManagerFilterMother::create(userId: new Id($user->getId()), filterId: new Id(1)),
            ManagerFilterMother::create(userId: new Id($user->getId()), filterId: new Id(3)),
        ]);

        $filterRepository = $this->createMock(FilterRepository::class);
        $filterRepository->expects($this->once())
            ->method('findBy')
            ->willReturnCallback(function (FilterCriteria $criteria) use ($filterCollection) {
                $this->assertCount(2, $criteria->getIds());

                return $filterCollection;
            });

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $managerFilterRepository->expects($this->exactly(2))
            ->method('findBy')
            ->willReturn(new ManagerFilterCollection([]), $managerFilters);

        $handler = $this->getHandler(
            filterRepository: $filterRepository,
            managerFilterRepository: $managerFilterRepository,
        );

        $result = $handler->handle(new GetFiltersQuery(
            criteria: FilterCriteria::createEmpty(),
            requestedBy: $user,
        ));

        $this->assertCount(0, $result);

        $result = $handler->handle(new GetFiltersQuery(
            criteria: FilterCriteria::createEmpty(),
            requestedBy: $user,
        ));
        $this->assertCount(2, $result);
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testAsAdmin(): void
    {
        $user = UserMother::create(id: 2, roles: [User::ROLE_ADMIN, User::ROLE_MANAGER]);

        $filterCollection = new FilterCollection([
            FilterMother::create(
                id: new Id(1),
                name: 'Filter 1',
            ),
            FilterMother::create(
                id: new Id(3),
                name: 'Filter 1',
            ),
        ]);

        $filterRepository = $this->createMock(FilterRepository::class);
        $filterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($filterCollection);

        $handler = $this->getHandler(
            filterRepository: $filterRepository,
        );

        $result = $handler->handle(new GetFiltersQuery(
            criteria: FilterCriteria::createEmpty(),
            requestedBy: $user,
        ));

        $this->assertCount(2, $result);
    }
}
