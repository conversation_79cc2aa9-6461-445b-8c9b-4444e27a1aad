<?php

declare(strict_types=1);

namespace App\V2\Application\Query;

use App\V2\Domain\Bus\Query;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;

readonly class GetVirtualMeetings implements Query
{
    public function __construct(
        private VirtualMeetingCriteria $criteria,
    ) {
    }

    public function getCriteria(): VirtualMeetingCriteria
    {
        return $this->criteria;
    }
}
